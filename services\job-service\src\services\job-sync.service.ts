import { Job } from '../models/job.model';
import { JobApiService, JobApiResponse } from './job-api.service';
import { logger } from '../utils/logger';
import { MonitoringService } from './monitoring.service';

export class JobSyncService {
  private jobApiService: JobApiService;
  private monitoringService: MonitoringService;

  constructor() {
    this.jobApiService = new JobApiService();
    this.monitoringService = MonitoringService.getInstance();
  }

  /**
   * Sync jobs from external APIs
   */
  async syncJobs(
    query: string = 'developer',
    location: string = 'us',
    maxJobs: number = 100
  ): Promise<{ synced: number; skipped: number; errors: number }> {
    const startTime = Date.now();
    logger.info(`Starting job sync for query: "${query}" in location: "${location}"`);
    
    let synced = 0;
    let skipped = 0;
    let errors = 0;

    try {
      // Fetch jobs from all sources
      const externalJobs = await this.jobApiService.fetchJobsFromAllSources(
        query,
        location,
        maxJobs
      );

      logger.info(`Fetched ${externalJobs.length} jobs from external APIs`);

      // Process each job
      for (const externalJob of externalJobs) {
        try {
          const result = await this.processExternalJob(externalJob);
          if (result === 'synced') {
            synced++;
          } else {
            skipped++;
          }
        } catch (error) {
          logger.error(`Error processing job ${externalJob.id}:`, error);
          errors++;
        }
      }

      const syncTime = Date.now() - startTime;
      const success = errors === 0;
      
      // Record metrics
      this.monitoringService.recordSyncMetrics(
        success,
        syncTime,
        synced + skipped + errors
      );
      
      logger.info(`Job sync completed: ${synced} synced, ${skipped} skipped, ${errors} errors in ${syncTime}ms`);
    } catch (error) {
      const syncTime = Date.now() - startTime;
      
      // Record failed metrics
      this.monitoringService.recordSyncMetrics(false, syncTime, 0);
      
      logger.error('Error during job sync:', error);
      throw error;
    }

    return { synced, skipped, errors };
  }

  /**
   * Process a single external job
   */
  private async processExternalJob(externalJob: JobApiResponse): Promise<'synced' | 'skipped'> {
    try {
      // Check if job already exists
      const existingJob = await Job.findOne({
        $or: [
          { 'externalId': externalJob.id },
          { 
            title: externalJob.title,
            company: externalJob.company,
            location: externalJob.location,
            'source': this.mapSourceToEnum(externalJob.source)
          }
        ]
      });

      if (existingJob) {
        logger.debug(`Job ${externalJob.id} already exists, skipping`);
        return 'skipped';
      }

    // Create new job document
    const jobData = {
      title: externalJob.title,
      company: externalJob.company,
      description: externalJob.description,
      location: externalJob.location,
      type: externalJob.type,
      salary: externalJob.salary,
      requirements: externalJob.requirements || [],
      benefits: externalJob.benefits || [],
      skills: externalJob.skills || [],
      experience: externalJob.experience,
      education: externalJob.education || 'any',
      remote: externalJob.remote || false,
      status: 'active',
      postedBy: 'system', // System-imported jobs
      expiresAt: externalJob.expiresAt ? new Date(externalJob.expiresAt) : undefined,
      tags: [externalJob.source, 'external'],
      isFeatured: false,
      applicationCount: 0,
      viewCount: 0,
      // Store external job metadata
      externalId: externalJob.id,
      source: this.mapSourceToEnum(externalJob.source),
      externalUrl: externalJob.url,
      postedAt: new Date(externalJob.postedAt),
    };

      const job = new Job(jobData);
      await job.save();

      logger.debug(`Successfully synced job: ${externalJob.title} at ${externalJob.company}`);
      return 'synced';
    } catch (error) {
      logger.error(`Database error processing job ${externalJob.id}:`, error);
      throw error;
    }
  }

  /**
   * Clean up old external jobs
   */
  async cleanupOldJobs(daysOld: number = 30): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);

    const result = await Job.deleteMany({
      source: { $in: ['external', 'adzuna', 'indeed', 'linkedin', 'glassdoor'] },
      postedAt: { $lt: cutoffDate },
      status: { $in: ['active', 'paused'] }
    });

    logger.info(`Cleaned up ${result.deletedCount} old external jobs`);
    return result.deletedCount;
  }

  /**
   * Get job sync statistics
   */
  async getSyncStats(): Promise<{
    totalJobs: number;
    externalJobs: number;
    internalJobs: number;
    activeJobs: number;
    lastSync?: Date | undefined;
  }> {
    const [totalJobs, externalJobs, internalJobs, activeJobs] = await Promise.all([
      Job.countDocuments(),
      Job.countDocuments({ source: { $in: ['external', 'adzuna', 'indeed', 'linkedin', 'glassdoor'] } }),
      Job.countDocuments({ source: 'internal' }),
      Job.countDocuments({ status: 'active' }),
    ]);

    // Get the most recent external job to estimate last sync
    const lastExternalJob = await Job.findOne(
      { source: { $in: ['external', 'adzuna', 'indeed', 'linkedin', 'glassdoor'] } },
      { postedAt: 1 }
    ).sort({ postedAt: -1 });

    return {
      totalJobs,
      externalJobs,
      internalJobs, 
      activeJobs,
      lastSync: lastExternalJob?.postedAt ? new Date(lastExternalJob.postedAt) : undefined,
    };
  }

  /**
   * Sync jobs for specific categories
   */
  async syncJobsByCategory(): Promise<void> {
    const categories = [
      { query: 'software developer', location: 'us' },
      { query: 'frontend developer', location: 'us' },
      { query: 'backend developer', location: 'us' },
      { query: 'full stack developer', location: 'us' },
      { query: 'devops engineer', location: 'us' },
      { query: 'data scientist', location: 'us' },
      { query: 'machine learning engineer', location: 'us' },
      { query: 'product manager', location: 'us' },
      { query: 'designer', location: 'us' },
      { query: 'marketing', location: 'us' },
    ];

    logger.info(`Starting category-based job sync for ${categories.length} categories`);

    for (const category of categories) {
      try {
        await this.syncJobs(category.query, category.location, 20);
        // Small delay between categories to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 2000));
      } catch (error) {
        logger.error(`Error syncing category "${category.query}":`, error);
      }
    }

    logger.info('Category-based job sync completed');
  }

  /**
   * Map external source to database enum
   */
  private mapSourceToEnum(source: string): string {
    const sourceMap: Record<string, string> = {
      'adzuna': 'adzuna',
      'indeed': 'indeed',
      'linkedin': 'linkedin',
      'glassdoor': 'glassdoor',
    };
    
    return sourceMap[source] || 'external';
  }
}
