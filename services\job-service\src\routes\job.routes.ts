import { Router } from 'express';
import { ResponseUtil } from '../utils/response';
import { Job } from '../models/job.model';

const router = Router();

// Get all jobs
router.get('/', async (req, res) => {
  try {
    const { page = 1, limit = 10, status = 'active', type, location, company, skills } = req.query;
    const skip = (Number(page) - 1) * Number(limit);
    
    const filter: any = { status };
    if (type) filter.type = type;
    if (location) filter.location = { $regex: location, $options: 'i' };
    if (company) filter.company = { $regex: company, $options: 'i' };
    if (skills) filter.skills = { $in: Array.isArray(skills) ? skills : [skills] };
    
    const jobs = await Job.find(filter)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(Number(limit));
    
    const total = await Job.countDocuments(filter);
    
    const response = ResponseUtil.success(
      {
        jobs,
        total,
        page: Number(page),
        limit: Number(limit),
        totalPages: Math.ceil(total / Number(limit)),
      },
      'Jobs retrieved successfully'
    );

    res.status(response.statusCode).json(response);
  } catch (error) {
    const response = ResponseUtil.error('Failed to retrieve jobs', 500);
    res.status(response.statusCode).json(response);
  }
});

// Get job by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const job = await Job.findById(id);
    if (!job) {
      const response = ResponseUtil.error('Job not found', 404);
      return res.status(response.statusCode).json(response);
    }
    
    // Increment view count
    await Job.findByIdAndUpdate(id, { $inc: { viewCount: 1 } });
    
    const response = ResponseUtil.success(job, 'Job retrieved successfully');
    return res.status(response.statusCode).json(response);
  } catch (error) {
    const response = ResponseUtil.error('Failed to retrieve job', 500);
    return res.status(response.statusCode).json(response);
  }
});

// Create job
router.post('/', async (req, res) => {
  try {
    const jobData = {
      ...req.body,
      postedBy: req.body.postedBy || 'system', // TODO: Get from authenticated user context
    };
    
    const job = new Job(jobData);
    await job.save();
    
    const response = ResponseUtil.created(job, 'Job created successfully');
    res.status(response.statusCode).json(response);
  } catch (error) {
    const response = ResponseUtil.error('Failed to create job', 500);
    res.status(response.statusCode).json(response);
  }
});

export { router as jobRoutes };
