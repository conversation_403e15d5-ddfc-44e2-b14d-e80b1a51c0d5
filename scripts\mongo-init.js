// MongoDB initialization script
// This script runs when the MongoDB container starts for the first time

print('Starting MongoDB initialization...');

// Switch to the job_platform database
db = db.getSiblingDB('job_platform');

// Create collections with validation
print('Creating collections with schema validation...');

// Users collection
db.createCollection('users', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['email', 'firstName', 'lastName', 'role', 'subscriptionTier'],
      properties: {
        email: {
          bsonType: 'string',
          pattern: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        },
        firstName: { bsonType: 'string' },
        lastName: { bsonType: 'string' },
        role: {
          bsonType: 'string',
          enum: ['admin', 'user', 'premium', 'enterprise']
        },
        subscriptionTier: {
          bsonType: 'string',
          enum: ['free', 'basic', 'premium', 'enterprise']
        }
      }
    }
  }
});

// Jobs collection
db.createCollection('jobs', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['title', 'company', 'jobType', 'experienceLevel', 'status'],
      properties: {
        title: { bsonType: 'string' },
        jobType: {
          bsonType: 'string',
          enum: ['full_time', 'part_time', 'contract', 'freelance', 'internship', 'remote', 'hybrid']
        },
        experienceLevel: {
          bsonType: 'string',
          enum: ['entry', 'junior', 'mid', 'senior', 'lead', 'executive']
        },
        status: {
          bsonType: 'string',
          enum: ['active', 'paused', 'closed', 'filled']
        }
      }
    }
  }
});

// Resumes collection
db.createCollection('resumes', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['userId', 'name', 'status'],
      properties: {
        userId: { bsonType: 'objectId' },
        name: { bsonType: 'string' },
        status: {
          bsonType: 'string',
          enum: ['processing', 'ready', 'error']
        }
      }
    }
  }
});

// Applications collection
db.createCollection('applications', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['userId', 'jobId', 'resumeId', 'status'],
      properties: {
        userId: { bsonType: 'objectId' },
        jobId: { bsonType: 'objectId' },
        resumeId: { bsonType: 'objectId' },
        status: {
          bsonType: 'string',
          enum: ['draft', 'submitted', 'under_review', 'interview_scheduled', 
                'interviewed', 'offer_received', 'accepted', 'rejected', 'withdrawn']
        }
      }
    }
  }
});

// Create indexes for better performance
print('Creating indexes...');

// Users indexes
db.users.createIndex({ email: 1 }, { unique: true });
db.users.createIndex({ googleId: 1 }, { sparse: true, unique: true });
db.users.createIndex({ role: 1 });
db.users.createIndex({ subscriptionTier: 1 });
db.users.createIndex({ isDeleted: 1 });
db.users.createIndex({ createdAt: 1 });

// Jobs indexes
db.jobs.createIndex({ title: 'text', description: 'text' });
db.jobs.createIndex({ 'company.name': 1 });
db.jobs.createIndex({ jobType: 1 });
db.jobs.createIndex({ experienceLevel: 1 });
db.jobs.createIndex({ status: 1 });
db.jobs.createIndex({ 'location.country': 1, 'location.city': 1 });
db.jobs.createIndex({ tags: 1 });
db.jobs.createIndex({ createdAt: -1 });
db.jobs.createIndex({ expiresAt: 1 });
db.jobs.createIndex({ isDeleted: 1 });

// Resumes indexes
db.resumes.createIndex({ userId: 1 });
db.resumes.createIndex({ status: 1 });
db.resumes.createIndex({ isDefault: 1 });
db.resumes.createIndex({ createdAt: -1 });
db.resumes.createIndex({ isDeleted: 1 });

// Applications indexes
db.applications.createIndex({ userId: 1 });
db.applications.createIndex({ jobId: 1 });
db.applications.createIndex({ resumeId: 1 });
db.applications.createIndex({ status: 1 });
db.applications.createIndex({ appliedAt: -1 });
db.applications.createIndex({ userId: 1, status: 1 });
db.applications.createIndex({ isDeleted: 1 });

// Sessions indexes (for auth service)
db.sessions.createIndex({ userId: 1 });
db.sessions.createIndex({ sessionId: 1 }, { unique: true });
db.sessions.createIndex({ expiresAt: 1 }, { expireAfterSeconds: 0 });
db.sessions.createIndex({ isActive: 1 });

// Analytics indexes
db.analyticsevents.createIndex({ userId: 1 });
db.analyticsevents.createIndex({ eventType: 1 });
db.analyticsevents.createIndex({ timestamp: -1 });
db.analyticsevents.createIndex({ sessionId: 1 });

// Notifications indexes
db.notifications.createIndex({ userId: 1 });
db.notifications.createIndex({ status: 1 });
db.notifications.createIndex({ type: 1 });
db.notifications.createIndex({ scheduledAt: 1 });
db.notifications.createIndex({ createdAt: -1 });

print('Creating admin user...');

// Create default admin user
const adminUser = {
  email: '<EMAIL>',
  password: '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBdXwtGtrOrBiW', // 'admin123'
  firstName: 'Admin',
  lastName: 'User',
  role: 'admin',
  subscriptionTier: 'enterprise',
  isVerified: true,
  isActive: true,
  isSuspended: false,
  profile: {
    bio: 'System Administrator',
    profileVisibility: 'private',
    searchable: false,
    skills: [],
    education: [],
    experience: [],
    languages: [{ language: 'English', proficiency: 'native' }]
  },
  preferences: {
    notifications: {
      email: {
        jobAlerts: false,
        applicationUpdates: true,
        marketingEmails: false,
        weeklyDigest: false
      },
      push: {
        jobAlerts: false,
        applicationUpdates: true,
        messages: true
      },
      sms: {
        criticalUpdates: true,
        jobAlerts: false
      }
    },
    jobSearch: {
      preferredJobTypes: [],
      preferredLocations: [],
      salaryRange: { min: 0, max: 0, currency: 'USD' },
      remoteWork: false,
      willingToRelocate: false
    },
    privacy: {
      showProfile: false,
      showSalaryExpectations: false,
      allowRecruiterContact: false,
      showApplicationHistory: false
    },
    interface: {
      theme: 'light',
      language: 'en',
      timezone: 'UTC',
      dateFormat: 'MM/DD/YYYY'
    }
  },
  analytics: {
    profileViews: 0,
    searchAppearances: 0,
    applicationsSent: 0,
    interviewsScheduled: 0,
    offersReceived: 0,
    loginStreak: 0,
    totalLogins: 0,
    averageSessionDuration: 0,
    lastActiveAt: new Date(),
    featuresUsed: [],
    premiumFeaturesUsed: [],
    responseRate: 0,
    interviewRate: 0,
    offerRate: 0
  },
  isDeleted: false,
  createdAt: new Date(),
  updatedAt: new Date()
};

try {
  db.users.insertOne(adminUser);
  print('✅ Admin user created successfully');
} catch (error) {
  print('⚠️  Admin user already exists or error occurred:', error.message);
}

print('✅ MongoDB initialization completed successfully!');
print('📊 Database: job_platform');
print('👤 Admin user: <EMAIL> / admin123');
print('🔧 Collections created with validation and indexes');