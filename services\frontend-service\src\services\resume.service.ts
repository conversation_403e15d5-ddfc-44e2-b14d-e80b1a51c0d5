import { apiService } from './api';
import { Resume, ResumeContent, ResumeAnalysis } from '@/types/api';

class ResumeService {
  // Resume management
  async getResumes(): Promise<{ resumes: Resume[]; total: number }> {
    return apiService.get<{ resumes: Resume[]; total: number }>('/resumes');
  }

  async getResumeById(id: string): Promise<Resume> {
    return apiService.get<Resume>(`/resumes/${id}`);
  }

  async uploadResume(
    file: File, 
    options?: {
      name?: string;
      isDefault?: boolean;
    },
    onProgress?: (progress: number) => void
  ): Promise<Resume> {
    const formData = new FormData();
    formData.append('file', file);
    
    if (options?.name) {
      formData.append('name', options.name);
    }
    
    if (options?.isDefault) {
      formData.append('isDefault', options.isDefault.toString());
    }

    return apiService.upload<Resume>('/resumes/upload', formData, onProgress);
  }

  async updateResume(id: string, updates: {
    name?: string;
    isDefault?: boolean;
    content?: Partial<ResumeContent>;
  }): Promise<Resume> {
    return apiService.put<Resume>(`/resumes/${id}`, updates);
  }

  async deleteResume(id: string): Promise<void> {
    return apiService.delete(`/resumes/${id}`);
  }

  async setDefaultResume(id: string): Promise<void> {
    return apiService.post(`/resumes/${id}/set-default`);
  }

  async duplicateResume(id: string, name?: string): Promise<Resume> {
    return apiService.post<Resume>(`/resumes/${id}/duplicate`, { name });
  }

  // Resume analysis and optimization
  async analyzeResume(id: string, jobDescription?: {
    title: string;
    requirements: string[];
    skills: string[];
    experience: string;
    education: string;
  }): Promise<ResumeAnalysis> {
    return apiService.post<ResumeAnalysis>(`/resumes/${id}/analyze`, { jobDescription });
  }

  async optimizeResume(id: string, jobId?: string): Promise<{
    optimizedContent: ResumeContent;
    suggestions: Array<{
      type: 'content' | 'format' | 'keywords' | 'structure';
      message: string;
      priority: 'high' | 'medium' | 'low';
      applied: boolean;
    }>;
  }> {
    return apiService.post(`/resumes/${id}/optimize`, { jobId });
  }

  async getATSCompatibility(id: string): Promise<{
    score: number;
    issues: Array<{
      type: 'format' | 'content' | 'structure';
      message: string;
      severity: 'critical' | 'warning' | 'info';
      suggestion: string;
    }>;
    recommendations: string[];
  }> {
    return apiService.get(`/resumes/${id}/ats-compatibility`);
  }

  // Keyword optimization
  async analyzeKeywords(id: string, jobDescription?: string): Promise<{
    matchedKeywords: string[];
    missingKeywords: string[];
    keywordDensity: Record<string, number>;
    suggestions: string[];
  }> {
    return apiService.post(`/resumes/${id}/analyze-keywords`, { jobDescription });
  }

  async suggestKeywords(id: string, industry?: string): Promise<{
    recommended: string[];
    trending: string[];
    industrySpecific: string[];
  }> {
    return apiService.get(`/resumes/${id}/suggest-keywords`, { 
      params: { industry } 
    });
  }

  // Resume templates
  async getResumeTemplates(): Promise<Array<{
    id: string;
    name: string;
    category: string;
    preview: string;
    isPopular: boolean;
    isPremium: boolean;
  }>> {
    return apiService.get('/resumes/templates');
  }

  async createResumeFromTemplate(templateId: string, resumeData: {
    name: string;
    personalInfo: ResumeContent['personalInfo'];
    summary?: string;
  }): Promise<Resume> {
    return apiService.post<Resume>('/resumes/from-template', {
      templateId,
      ...resumeData,
    });
  }

  // Resume versions
  async getResumeVersions(id: string): Promise<Array<{
    id: string;
    version: number;
    name: string;
    createdAt: string;
    changes: string[];
  }>> {
    return apiService.get(`/resumes/${id}/versions`);
  }

  async revertToVersion(id: string, versionId: string): Promise<Resume> {
    return apiService.post<Resume>(`/resumes/${id}/revert`, { versionId });
  }

  // Resume sharing
  async generateShareableLink(id: string, options?: {
    expiresIn?: number; // hours
    password?: string;
    allowDownload?: boolean;
  }): Promise<{ shareLink: string; expiresAt: string }> {
    return apiService.post(`/resumes/${id}/share`, options);
  }

  async revokeShareableLink(id: string): Promise<void> {
    return apiService.delete(`/resumes/${id}/share`);
  }

  // Download and export
  async downloadResume(id: string): Promise<{ downloadUrl: string }> {
    return apiService.get<{ downloadUrl: string }>(`/resumes/${id}/download`);
  }

  async exportResume(id: string, format: 'json' | 'xml'): Promise<void> {
    return apiService.download(`/resumes/${id}/export`, `resume.${format}`, {
      params: { format }
    });
  }

  // Resume parsing (for uploaded files)
  async parseResumeContent(id: string): Promise<ResumeContent> {
    return apiService.get<ResumeContent>(`/resumes/${id}/parse`);
  }

  async reprocessResume(id: string): Promise<Resume> {
    return apiService.post<Resume>(`/resumes/${id}/reprocess`);
  }

  // AI-powered improvements
  async improveSummary(id: string, targetRole?: string): Promise<{
    suggestions: string[];
    improved: string;
  }> {
    return apiService.post(`/resumes/${id}/improve-summary`, { targetRole });
  }

  async improveExperience(id: string, experienceIndex: number): Promise<{
    suggestions: string[];
    improved: string;
    bulletPoints: string[];
  }> {
    return apiService.post(`/resumes/${id}/improve-experience`, { experienceIndex });
  }

  async generateAchievements(experience: {
    company: string;
    position: string;
    description: string;
    skills: string[];
  }): Promise<string[]> {
    return apiService.post('/resumes/generate-achievements', experience);
  }

  // Resume statistics
  async getResumeStats(id: string): Promise<{
    views: number;
    downloads: number;
    applications: number;
    lastViewed?: string;
    averageViewTime: number;
    topViewingSources: Array<{ source: string; count: number }>;
  }> {
    return apiService.get(`/resumes/${id}/stats`);
  }

  // Bulk operations
  async bulkDelete(resumeIds: string[]): Promise<void> {
    return apiService.delete('/resumes/bulk', { data: { resumeIds } });
  }

  async bulkAnalyze(resumeIds: string[]): Promise<Record<string, ResumeAnalysis>> {
    return apiService.post('/resumes/bulk/analyze', { resumeIds });
  }
}

export const resumeService = new ResumeService();
