import { Types, FilterQuery } from 'mongoose';
import { User } from '../models/user.model';
import { UserDocument } from '../types/auth.types';
import { CreateUserData, UpdateUserData } from '../types/user.types';
import { NotFoundError, ConflictError } from '../utils/errors';

export class UserService {
  /**
   * Create a new user
   */
  public async create(userData: CreateUserData): Promise<UserDocument> {
    try {
      const user = new User({
        ...userData,
        profile: {
          bio: '',
          skills: [],
          education: [],
          experience: [],
          languages: [{ language: 'English', proficiency: 'native' }],
          profileVisibility: 'private',
          searchable: false,
        },
        preferences: {
          notifications: {
            email: {
              jobAlerts: true,
              applicationUpdates: true,
              marketingEmails: false,
              weeklyDigest: true,
            },
            push: {
              jobAlerts: true,
              applicationUpdates: true,
              messages: true,
            },
            sms: {
              criticalUpdates: true,
              jobAlerts: false,
            },
          },
          jobSearch: {
            preferredJobTypes: [],
            preferredLocations: [],
            salaryRange: { min: 0, max: 0, currency: 'USD' },
            remoteWork: false,
            willingToRelocate: false,
          },
          privacy: {
            showProfile: false,
            showSalaryExpectations: false,
            allowRecruiterContact: false,
            showApplicationHistory: false,
          },
          interface: {
            theme: 'light',
            language: 'en',
            timezone: 'UTC',
            dateFormat: 'MM/DD/YYYY',
          },
        },
        analytics: {
          profileViews: 0,
          searchAppearances: 0,
          applicationsSent: 0,
          interviewsScheduled: 0,
          offersReceived: 0,
          loginStreak: 0,
          totalLogins: 0,
          averageSessionDuration: 0,
          lastActiveAt: new Date(),
          featuresUsed: [],
          premiumFeaturesUsed: [],
          responseRate: 0,
          interviewRate: 0,
          offerRate: 0,
        },
      });

      return await user.save();
    } catch (error: unknown) {
      if (
        error &&
        typeof error === 'object' &&
        'code' in error &&
        error.code === 11000
      ) {
        throw new ConflictError('User already exists with this email');
      }
      throw error;
    }
  }

  /**
   * Find user by ID
   */
  public async findById(
    id: string | Types.ObjectId
  ): Promise<UserDocument | null> {
    return await User.findById(id);
  }

  /**
   * Find user by email
   */
  public async findByEmail(email: string): Promise<UserDocument | null> {
    return await User.findOne({ email: email.toLowerCase() }).select('+password');
  }

  /**
   * Find user by Google ID
   */
  public async findByGoogleId(googleId: string): Promise<UserDocument | null> {
    return await User.findOne({ googleId });
  }

  /**
   * Find all users with filters
   */
  public async findAll(
    filters: FilterQuery<UserDocument> = {}
  ): Promise<UserDocument[]> {
    return await User.find(filters);
  }

  /**
   * Update user
   */
  public async update(
    id: string | Types.ObjectId,
    updateData: UpdateUserData
  ): Promise<UserDocument> {
    const user = await User.findByIdAndUpdate(
      id,
      { ...updateData, updatedAt: new Date() },
      { new: true, runValidators: true }
    );

    if (!user) {
      throw new NotFoundError('User not found');
    }

    return user;
  }

  /**
   * Update user password
   */
  public async updatePassword(
    id: string | Types.ObjectId,
    hashedPassword: string
  ): Promise<void> {
    const result = await User.updateOne(
      { _id: id },
      {
        password: hashedPassword,
        passwordChangedAt: new Date(),
        updatedAt: new Date(),
      }
    );

    if (result.matchedCount === 0) {
      throw new NotFoundError('User not found');
    }
  }

  /**
   * Update last login timestamp
   */
  public async updateLastLogin(id: string | Types.ObjectId): Promise<void> {
    await User.updateOne(
      { _id: id },
      {
        lastLoginAt: new Date(),
        $inc: { 'analytics.totalLogins': 1 },
        updatedAt: new Date(),
      }
    );
  }

  /**
   * Verify user email
   */
  public async verifyEmail(id: string | Types.ObjectId): Promise<void> {
    const result = await User.updateOne(
      { _id: id },
      {
        isVerified: true,
        emailVerificationToken: undefined,
        emailVerificationExpires: undefined,
        updatedAt: new Date(),
      }
    );

    if (result.matchedCount === 0) {
      throw new NotFoundError('User not found');
    }
  }

  /**
   * Link Google account to existing user
   */
  public async linkGoogleAccount(
    id: string | Types.ObjectId,
    googleId: string
  ): Promise<void> {
    const result = await User.updateOne(
      { _id: id },
      {
        googleId,
        updatedAt: new Date(),
      }
    );

    if (result.matchedCount === 0) {
      throw new NotFoundError('User not found');
    }
  }

  /**
   * Unlink Google account from user
   */
  public async unlinkGoogleAccount(
    id: string | Types.ObjectId
  ): Promise<void> {
    const result = await User.updateOne(
      { _id: id },
      {
        $unset: { googleId: 1 },
        updatedAt: new Date(),
      }
    );

    if (result.matchedCount === 0) {
      throw new NotFoundError('User not found');
    }
  }

  /**
   * Soft delete user
   */
  public async softDelete(id: string | Types.ObjectId): Promise<void> {
    const result = await User.updateOne(
      { _id: id },
      {
        isDeleted: true,
        deletedAt: new Date(),
        isActive: false,
        updatedAt: new Date(),
      }
    );

    if (result.matchedCount === 0) {
      throw new NotFoundError('User not found');
    }
  }

  /**
   * Restore soft deleted user
   */
  public async restore(id: string | Types.ObjectId): Promise<void> {
    const result = await User.updateOne(
      { _id: id },
      {
        isDeleted: false,
        deletedAt: undefined,
        isActive: true,
        updatedAt: new Date(),
      }
    );

    if (result.matchedCount === 0) {
      throw new NotFoundError('User not found');
    }
  }

  /**
   * Suspend user account
   */
  public async suspend(
    id: string | Types.ObjectId,
    reason: string
  ): Promise<void> {
    const result = await User.updateOne(
      { _id: id },
      {
        isSuspended: true,
        suspendedAt: new Date(),
        suspendedReason: reason,
        updatedAt: new Date(),
      }
    );

    if (result.matchedCount === 0) {
      throw new NotFoundError('User not found');
    }
  }

  /**
   * Unsuspend user account
   */
  public async unsuspend(id: string | Types.ObjectId): Promise<void> {
    const result = await User.updateOne(
      { _id: id },
      {
        isSuspended: false,
        suspendedAt: undefined,
        suspendedReason: undefined,
        updatedAt: new Date(),
      }
    );

    if (result.matchedCount === 0) {
      throw new NotFoundError('User not found');
    }
  }

  /**
   * Update user analytics
   */
  public async updateAnalytics(
    id: string | Types.ObjectId,
    analyticsUpdate: Record<string, unknown>
  ): Promise<void> {
    await User.updateOne(
      { _id: id },
      {
        $set: {
          ...Object.keys(analyticsUpdate).reduce(
            (acc, key) => {
              acc[`analytics.${key}`] = analyticsUpdate[key];
              return acc;
            },
            {} as Record<string, unknown>
          ),
          'analytics.lastActiveAt': new Date(),
          updatedAt: new Date(),
        },
      }
    );
  }

  /**
   * Search users
   */
  public async search(query: {
    searchTerm?: string;
    skills?: string[];
    location?: { country?: string; city?: string };
    experienceLevel?: string;
    page?: number;
    limit?: number;
  }): Promise<{ users: UserDocument[]; total: number }> {
    const filters: FilterQuery<UserDocument> = {
      isDeleted: false,
      isActive: true,
    };

    // Search term (name, email, bio)
    if (query.searchTerm) {
      filters.$or = [
        { firstName: { $regex: query.searchTerm, $options: 'i' } },
        { lastName: { $regex: query.searchTerm, $options: 'i' } },
        { email: { $regex: query.searchTerm, $options: 'i' } },
        { 'profile.bio': { $regex: query.searchTerm, $options: 'i' } },
      ];
    }

    // Skills filter
    if (query.skills && query.skills.length > 0) {
      filters['profile.skills.name'] = { $in: query.skills };
    }

    // Location filter
    if (query.location) {
      if (query.location.country) {
        filters['profile.location.country'] = query.location.country;
      }
      if (query.location.city) {
        filters['profile.location.city'] = query.location.city;
      }
    }

    const page = query.page ?? 1;
    const limit = query.limit ?? 20;
    const skip = (page - 1) * limit;

    const [users, total] = await Promise.all([
      User.find(filters)
        .select('-password -emailVerificationToken')
        .skip(skip)
        .limit(limit)
        .sort({ createdAt: -1 }),
      User.countDocuments(filters),
    ]);

    return { users, total };
  }
}
