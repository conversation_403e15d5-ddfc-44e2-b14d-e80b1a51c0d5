import { Router } from 'express';
import { ResponseUtil } from '../utils/response';
import { serviceRegistry } from '../services/service-registry';
import * as os from 'os';

const router = Router();

// Note: In production, add authentication middleware here

/**
 * Get gateway metrics
 */
router.get('/', (req, res) => {
  const metrics = {
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    cpu: process.cpuUsage(),
    system: {
      platform: process.platform,
      nodeVersion: process.version,
      arch: process.arch,
      loadavg: os.loadavg(),
      freemem: os.freemem(),
      totalmem: os.totalmem(),
      cpus: os.cpus().length,
    },
    services: {
      total: serviceRegistry.getServiceList().length,
      healthy: serviceRegistry.getHealthyServices().length,
      details: serviceRegistry.getServiceHealth(),
    },
  };

  res.json(ResponseUtil.success(metrics, 'Gateway metrics retrieved'));
});

/**
 * Get service-specific metrics
 */
router.get('/services', (req, res) => {
  const serviceMetrics = {
    timestamp: new Date().toISOString(),
    services: serviceRegistry.getServiceHealth(),
    summary: {
      total: serviceRegistry.getServiceList().length,
      healthy: serviceRegistry.getHealthyServices().length,
      unhealthy:
        serviceRegistry.getServiceList().length -
        serviceRegistry.getHealthyServices().length,
    },
  };

  res.json(ResponseUtil.success(serviceMetrics, 'Service metrics retrieved'));
});

/**
 * Get performance metrics
 */
router.get('/performance', (req, res) => {
  const performanceMetrics = {
    timestamp: new Date().toISOString(),
    process: {
      uptime: process.uptime(),
      memory: {
        ...process.memoryUsage(),
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-explicit-any, @typescript-eslint/no-unsafe-member-access
        external: (process.memoryUsage() as any).external ?? 0,
      },
      cpu: process.cpuUsage(),
      pid: process.pid,
      ppid: process.ppid,
    },
    system: {
      loadavg: os.loadavg(),
      freemem: os.freemem(),
      totalmem: os.totalmem(),
      uptime: os.uptime(),
    },
    eventLoop: {
      // Note: These would require additional monitoring libraries in production
      lag: 0, // Placeholder - would use @nodejs/node-report or similar
      utilization: 0, // Placeholder
    },
  };

  res.json(
    ResponseUtil.success(performanceMetrics, 'Performance metrics retrieved')
  );
});

/**
 * Reset service health checks
 */
router.post('/services/health/reset', (req, res) => {
  // This would trigger immediate health checks for all services
  // Implementation would depend on the service registry capabilities

  res.json(
    ResponseUtil.success(
      { message: 'Health checks reset initiated' },
      'Health checks reset'
    )
  );
});

export { router as metricsRoutes };
