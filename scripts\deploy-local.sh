#!/bin/bash

# Job Application Platform - Local Docker Deployment Script
# This script handles local development deployment with Docker

set -e  # Exit on any error

echo "🚀 Starting Job Application Platform Local Deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
check_docker() {
    print_status "Checking Docker installation..."
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi

    if ! docker info &> /dev/null; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi

    print_success "Docker is running"
}

# Setup environment files
setup_environment() {
    print_status "Setting up environment files..."
    
    if [ ! -f .env ]; then
        print_status "Creating environment files..."
        ./scripts/setup-env.sh
    else
        print_success "Environment files already exist"
    fi
}

# Start infrastructure services (MongoDB, Redis)
start_infrastructure() {
    print_status "Starting infrastructure services (MongoDB, Redis)..."
    
    # Stop any existing containers
    docker-compose down 2>/dev/null || true
    
    # Start only infrastructure services
    docker-compose up -d mongodb redis
    
    print_status "Waiting for infrastructure to be ready..."
    sleep 10
    
    # Check if MongoDB is ready
    max_attempts=30
    attempt=1
    while [ $attempt -le $max_attempts ]; do
        if docker exec job-platform-mongodb mongosh --eval "db.adminCommand('ping')" &> /dev/null; then
            print_success "MongoDB is ready"
            break
        else
            if [ $attempt -eq $max_attempts ]; then
                print_error "MongoDB failed to start after $max_attempts attempts"
                return 1
            fi
            print_status "Waiting for MongoDB... (attempt $attempt/$max_attempts)"
            sleep 2
            ((attempt++))
        fi
    done
    
    # Check if Redis is ready
    attempt=1
    while [ $attempt -le $max_attempts ]; do
        if docker exec job-platform-redis redis-cli ping &> /dev/null; then
            print_success "Redis is ready"
            break
        else
            if [ $attempt -eq $max_attempts ]; then
                print_error "Redis failed to start after $max_attempts attempts"
                return 1
            fi
            print_status "Waiting for Redis... (attempt $attempt/$max_attempts)"
            sleep 2
            ((attempt++))
        fi
    done
}

# Install dependencies and build services
build_services() {
    print_status "Installing dependencies and building services..."
    
    # Install root dependencies
    npm install
    
    # Install shared dependencies and build
    print_status "Building shared library..."
    cd shared && npm install && npm run build && cd ..
    
    # Build each service
    services=("api-gateway" "auth-service" "user-service" "job-service" "resume-service")
    
    for service in "${services[@]}"; do
        if [ -d "services/$service" ]; then
            print_status "Building $service..."
            cd "services/$service"
            npm install
            npm run build 2>/dev/null || print_warning "Build failed for $service (this is okay for development)"
            cd ../..
        fi
    done
    
    print_success "Services built successfully"
}

# Start all services locally (not in Docker)
start_services_local() {
    print_status "Starting services locally..."
    
    # Create logs directory
    mkdir -p logs
    
    # Start services in background using tsx (TypeScript runner)
    services=("api-gateway:3000" "auth-service:3001" "user-service:3002" "job-service:3003" "resume-service:3004")
    
    for service_port in "${services[@]}"; do
        service=$(echo $service_port | cut -d':' -f1)
        port=$(echo $service_port | cut -d':' -f2)
        
        if [ -d "services/$service" ]; then
            print_status "Starting $service on port $port..."
            cd "services/$service"
            
            # Install tsx if not present
            if ! npm list tsx > /dev/null 2>&1; then
                print_status "Installing tsx for $service..."
                npm install tsx --save-dev
            fi
            
            # Start service with tsx (bypasses TypeScript compilation)
            PORT=$port npx tsx src/index.ts > "../../logs/$service.log" 2>&1 &
            echo $! > "../../logs/$service.pid"
            cd ../..
            sleep 3
        fi
    done
    
    print_success "All services started"
}

# Health check for services
check_service_health() {
    print_status "Performing health checks..."
    
    services=(
        "3000:API Gateway"
        "3001:Auth Service"
        "3002:User Service"
        "3003:Job Service"
        "3004:Resume Service"
    )
    
    sleep 10  # Give services time to start
    
    for service in "${services[@]}"; do
        port=$(echo $service | cut -d':' -f1)
        name=$(echo $service | cut -d':' -f2)
        
        print_status "Checking $name (http://localhost:$port/health)..."
        
        max_attempts=15
        attempt=1
        
        while [ $attempt -le $max_attempts ]; do
            if curl -f -s "http://localhost:$port/health" > /dev/null 2>&1; then
                print_success "$name is healthy"
                break
            else
                if [ $attempt -eq $max_attempts ]; then
                    print_warning "$name health check failed after $max_attempts attempts (this might be okay)"
                    break
                fi
                print_status "Attempt $attempt/$max_attempts failed, retrying in 2 seconds..."
                sleep 2
                ((attempt++))
            fi
        done
    done
}

# Show deployment status
show_status() {
    print_status "Deployment Status:"
    echo ""
    echo "🌐 API Gateway:        http://localhost:3000"
    echo "🔐 Auth Service:       http://localhost:3001"
    echo "👤 User Service:       http://localhost:3002"
    echo "💼 Job Service:        http://localhost:3003"
    echo "📄 Resume Service:     http://localhost:3004"
    echo ""
    echo "📊 MongoDB:            mongodb://localhost:27017"
    echo "🗄️  Redis:              redis://localhost:6379"
    echo ""
    echo "🔍 Health Checks:"
    echo "   API Gateway:        http://localhost:3000/health"
    echo "   Auth Service:       http://localhost:3001/health"
    echo "   User Service:       http://localhost:3002/health"
    echo "   Job Service:        http://localhost:3003/health"
    echo "   Resume Service:     http://localhost:3004/health"
    echo ""
    echo "📚 API Documentation:  http://localhost:3000/api"
    echo ""
    echo "🔑 Default Admin Credentials:"
    echo "   Email:    <EMAIL>"
    echo "   Password: admin123"
    echo ""
    echo "📝 Service Logs:"
    echo "   API Gateway:        tail -f logs/api-gateway.log"
    echo "   Auth Service:       tail -f logs/auth-service.log"
    echo "   User Service:       tail -f logs/user-service.log"
    echo "   Job Service:        tail -f logs/job-service.log"
    echo "   Resume Service:     tail -f logs/resume-service.log"
    echo ""
    print_success "Job Application Platform is ready! 🎉"
}

# Cleanup function
cleanup() {
    print_status "Cleaning up..."
    
    # Kill service processes
    if [ -d "logs" ]; then
        for pidfile in logs/*.pid; do
            if [ -f "$pidfile" ]; then
                pid=$(cat "$pidfile")
                kill "$pid" 2>/dev/null || true
                rm "$pidfile"
            fi
        done
    fi
    
    # Stop Docker containers
    docker-compose down 2>/dev/null || true
}

# Stop services
stop_services() {
    print_status "Stopping all services..."
    cleanup
    print_success "All services stopped"
}

# Main function
main() {
    local action=${1:-start}
    
    case $action in
        start)
            echo "=========================================="
            echo "  Job Application Platform Local Setup"
            echo "=========================================="
            echo ""
            
            # Trap cleanup on exit
            trap cleanup EXIT
            
            check_docker
            setup_environment
            start_infrastructure
            build_services
            start_services_local
            check_service_health
            show_status
            
            echo ""
            print_status "Press Ctrl+C to stop all services"
            
            # Keep script running
            while true; do
                sleep 10
            done
            ;;
        stop)
            stop_services
            ;;
        restart)
            stop_services
            sleep 2
            main start
            ;;
        status)
            show_status
            ;;
        logs)
            service=${2:-api-gateway}
            if [ -f "logs/$service.log" ]; then
                tail -f "logs/$service.log"
            else
                print_error "Log file for $service not found"
                echo "Available logs:"
                ls logs/*.log 2>/dev/null || echo "No log files found"
            fi
            ;;
        *)
            echo "Usage: $0 [start|stop|restart|status|logs [service]]"
            echo ""
            echo "Commands:"
            echo "  start    - Start all services (default)"
            echo "  stop     - Stop all services"
            echo "  restart  - Restart all services"
            echo "  status   - Show service status"
            echo "  logs     - Show logs for a service"
            echo ""
            echo "Examples:"
            echo "  $0 start"
            echo "  $0 logs auth-service"
            echo "  $0 stop"
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"