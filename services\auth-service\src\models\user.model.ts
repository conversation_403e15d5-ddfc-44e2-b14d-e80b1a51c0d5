import { Schema, model } from 'mongoose';
import { UserDocument } from '../types/auth.types';
// import { baseSchema } from '../database/schemas/base.schema';

const userSchema = new Schema(
  {
    // Basic Information
    email: {
      type: String,
      required: true,
      lowercase: true,
      trim: true,
    },
    password: {
      type: String,
      select: false, // Don't include in queries by default
    },
    googleId: {
      type: String,
    },
    firstName: {
      type: String,
      required: true,
      trim: true,
      maxlength: 50,
    },
    lastName: {
      type: String,
      required: true,
      trim: true,
      maxlength: 50,
    },
    avatar: {
      type: String,
    },

    // Role and Permissions
    role: {
      type: String,
      enum: ['admin', 'user', 'premium', 'enterprise'],
      default: 'user',
    },
    subscriptionTier: {
      type: String,
      enum: ['free', 'basic', 'premium', 'enterprise'],
      default: 'free',
    },

    // Account Status
    isVerified: {
      type: Boolean,
      default: false,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    isSuspended: {
      type: Boolean,
      default: false,
    },
    suspendedAt: Date,
    suspendedReason: String,
    isDeleted: {
      type: Boolean,
      default: false,
    },

    // Email Verification
    emailVerificationToken: String,
    emailVerificationExpires: Date,

    // Timestamps
    lastLoginAt: Date,
    passwordChangedAt: Date,

    // Profile Information
    profile: {
      bio: {
        type: String,
        maxlength: 1000,
      },
      phoneNumber: String,
      location: {
        country: String,
        state: String,
        city: String,
        zipCode: String,
        coordinates: {
          lat: Number,
          lng: Number,
        },
        remote: Boolean,
      },
      website: String,
      linkedin: String,
      github: String,
      portfolio: String,

      // Professional Information
      currentPosition: String,
      currentCompany: String,
      yearsOfExperience: Number,
      expectedSalary: {
        min: Number,
        max: Number,
        currency: {
          type: String,
          default: 'USD',
        },
      },

      // Personal Details
      dateOfBirth: Date,
      nationality: String,
      languages: [
        {
          language: String,
          proficiency: {
            type: String,
            enum: ['basic', 'conversational', 'fluent', 'native'],
          },
        },
      ],

      // Skills and Experience
      skills: [
        {
          name: String,
          level: {
            type: String,
            enum: ['beginner', 'intermediate', 'advanced', 'expert'],
          },
          verified: Boolean,
          yearsOfExperience: Number,
        },
      ],
      education: [
        {
          institution: String,
          degree: String,
          field: String,
          startDate: Date,
          endDate: Date,
          gpa: Number,
          description: String,
        },
      ],
      experience: [
        {
          company: String,
          position: String,
          startDate: Date,
          endDate: Date,
          current: Boolean,
          description: String,
          skills: [String],
          achievements: [String],
        },
      ],

      // Visibility Settings
      profileVisibility: {
        type: String,
        enum: ['public', 'private', 'connections'],
        default: 'private',
      },
      searchable: {
        type: Boolean,
        default: false,
      },
    },

    // User Preferences
    preferences: {
      notifications: {
        email: {
          jobAlerts: { type: Boolean, default: true },
          applicationUpdates: { type: Boolean, default: true },
          marketingEmails: { type: Boolean, default: false },
          weeklyDigest: { type: Boolean, default: true },
        },
        push: {
          jobAlerts: { type: Boolean, default: true },
          applicationUpdates: { type: Boolean, default: true },
          messages: { type: Boolean, default: true },
        },
        sms: {
          criticalUpdates: { type: Boolean, default: true },
          jobAlerts: { type: Boolean, default: false },
        },
      },
      jobSearch: {
        preferredJobTypes: [String],
        preferredLocations: [
          {
            country: String,
            state: String,
            city: String,
            remote: Boolean,
          },
        ],
        salaryRange: {
          min: Number,
          max: Number,
          currency: String,
        },
        remoteWork: Boolean,
        willingToRelocate: Boolean,
        availabilityDate: Date,
      },
      privacy: {
        showProfile: Boolean,
        showSalaryExpectations: Boolean,
        allowRecruiterContact: Boolean,
        showApplicationHistory: Boolean,
      },
      interface: {
        theme: {
          type: String,
          enum: ['light', 'dark', 'system'],
          default: 'light',
        },
        language: {
          type: String,
          default: 'en',
        },
        timezone: {
          type: String,
          default: 'UTC',
        },
        dateFormat: {
          type: String,
          default: 'MM/DD/YYYY',
        },
      },
    },

    // Analytics
    analytics: {
      profileViews: { type: Number, default: 0 },
      searchAppearances: { type: Number, default: 0 },
      applicationsSent: { type: Number, default: 0 },
      interviewsScheduled: { type: Number, default: 0 },
      offersReceived: { type: Number, default: 0 },
      loginStreak: { type: Number, default: 0 },
      totalLogins: { type: Number, default: 0 },
      averageSessionDuration: { type: Number, default: 0 },
      lastActiveAt: Date,
      featuresUsed: [String],
      premiumFeaturesUsed: [String],
      responseRate: { type: Number, default: 0 },
      interviewRate: { type: Number, default: 0 },
      offerRate: { type: Number, default: 0 },
    },
  },
  {
    timestamps: true,
    versionKey: false,
    toJSON: {
      transform(
        doc: unknown,
        ret: Record<string, unknown>
      ): Record<string, unknown> {
        ret.id = (ret._id as { toString: () => string }).toString();
        delete ret._id;
        delete ret.password;
        delete ret.emailVerificationToken;
        return ret;
      },
    },
  }
);

// Indexes
userSchema.index({ email: 1 }, { unique: true });
userSchema.index({ googleId: 1 }, { sparse: true, unique: true });
userSchema.index({ role: 1 });
userSchema.index({ subscriptionTier: 1 });
userSchema.index({ isDeleted: 1 });
userSchema.index({ isActive: 1 });
userSchema.index({ createdAt: -1 });
userSchema.index({ 'profile.skills.name': 1 });
userSchema.index({ 'profile.location.country': 1, 'profile.location.city': 1 });

// Soft delete middleware
userSchema.pre(
  /^find/,
  function (this: {
    getQuery: () => { includeDeleted?: boolean };
    find: (query: Record<string, unknown>) => void;
  }) {
    if (!this.getQuery().includeDeleted) {
      this.find({ isDeleted: { $ne: true } });
    }
  }
);

export const User = model<UserDocument>('User', userSchema);
