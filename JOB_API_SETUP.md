# Job Service API Integration Setup Guide

This guide will help you set up the job service with real job data from external APIs.

## 🚀 Quick Start

### 1. **Adzuna API (Recommended - Free)**
- **Website**: https://developer.adzuna.com/
- **Cost**: Free with good rate limits
- **Setup**:
  1. Go to https://developer.adzuna.com/
  2. Sign up for a free account
  3. Create a new application
  4. Get your `APP_ID` and `API_KEY`

### 2. **Indeed API (Free)**
- **Website**: https://ads.indeed.com/jobroll/xmlfeed
- **Cost**: Free with rate limits
- **Setup**:
  1. Go to https://ads.indeed.com/jobroll/xmlfeed
  2. Sign up for a publisher account
  3. Get your `PUBLISHER_ID`

### 3. **LinkedIn API (Paid)**
- **Website**: https://developer.linkedin.com/
- **Cost**: Paid service
- **Setup**:
  1. Go to https://developer.linkedin.com/
  2. Create a LinkedIn Developer account
  3. Create a new application
  4. Get your `CLIENT_ID` and `CLIENT_SECRET`

## 🔧 Environment Setup

### Step 1: Copy Environment Template
```bash
cp env-templates/job-service.env .env
```

### Step 2: Add Your API Keys
Edit your `.env` file and add your API keys:

```env
# Adzuna API (Free - Recommended)
ADZUNA_APP_ID=your-adzuna-app-id
ADZUNA_API_KEY=your-adzuna-api-key

# Indeed API (Free)
INDEED_PUBLISHER_ID=your-indeed-publisher-id

# LinkedIn API (Paid - Optional)
LINKEDIN_CLIENT_ID=your-linkedin-client-id
LINKEDIN_CLIENT_SECRET=your-linkedin-client-secret

# Glassdoor API (Paid - Optional)
GLASSDOOR_PARTNER_ID=your-glassdoor-partner-id
GLASSDOOR_API_KEY=your-glassdoor-api-key
```

## 🛠️ Manual Setup Instructions

### **Adzuna API Setup (Free)**

1. **Register**: Go to https://developer.adzuna.com/
2. **Create App**: Click "Create New Application"
3. **Fill Details**:
   - Application Name: "Job Platform"
   - Description: "Job search platform"
   - Website: Your website URL
4. **Get Credentials**: Copy your `APP_ID` and `API_KEY`
5. **Add to .env**:
   ```env
   ADZUNA_APP_ID=your-app-id-here
   ADZUNA_API_KEY=your-api-key-here
   ```

### **Indeed API Setup (Free)**

1. **Register**: Go to https://ads.indeed.com/jobroll/xmlfeed
2. **Sign Up**: Create a publisher account
3. **Get Publisher ID**: Your publisher ID will be provided
4. **Add to .env**:
   ```env
   INDEED_PUBLISHER_ID=your-publisher-id-here
   ```

### **LinkedIn API Setup (Paid)**

1. **Register**: Go to https://developer.linkedin.com/
2. **Create App**: Click "Create App"
3. **Fill Details**:
   - App Name: "Job Platform"
   - LinkedIn Page: Your company page
   - Privacy Policy: Your privacy policy URL
4. **Get Credentials**: Copy your `Client ID` and `Client Secret`
5. **Add to .env**:
   ```env
   LINKEDIN_CLIENT_ID=your-client-id-here
   LINKEDIN_CLIENT_SECRET=your-client-secret-here
   ```

## 🚀 Testing the Integration

### 1. **Start the Job Service**
```bash
cd services/job-service
npm run dev
```

### 2. **Test Manual Sync**
```bash
# Test Adzuna API
curl -X POST http://localhost:3003/sync \
  -H "Content-Type: application/json" \
  -d '{"query": "developer", "location": "us", "maxJobs": 10}'

# Test Indeed API
curl -X POST http://localhost:3003/sync \
  -H "Content-Type: application/json" \
  -d '{"query": "software engineer", "location": "us", "maxJobs": 10}'
```

### 3. **Check Sync Statistics**
```bash
curl http://localhost:3003/sync/stats
```

### 4. **View Jobs**
```bash
curl http://localhost:3003/jobs
```

## 📊 API Endpoints

### **Job Sync Endpoints**

| Method | Endpoint | Description |
|--------|----------|-------------|
| `POST` | `/sync` | Manual job sync |
| `POST` | `/sync/categories` | Sync jobs by categories |
| `GET` | `/sync/stats` | Get sync statistics |
| `DELETE` | `/sync/cleanup` | Clean up old jobs |

### **Job Endpoints**

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/jobs` | Get all jobs |
| `GET` | `/jobs/:id` | Get job by ID |
| `POST` | `/jobs` | Create new job |

## 🔄 Automatic Sync

The job service automatically syncs jobs every 6 hours:

- **Schedule**: Every 6 hours (`0 */6 * * *`)
- **Query**: "developer"
- **Location**: "us"
- **Max Jobs**: 50

## 🧹 Cleanup

Old external jobs are automatically cleaned up:

- **Schedule**: Daily at 2 AM (`0 2 * * *`)
- **Age**: Jobs older than 30 days
- **Status**: Active or paused jobs

## 🐛 Troubleshooting

### **Common Issues**

1. **"API credentials not configured"**
   - Check your `.env` file
   - Ensure API keys are correctly set
   - Restart the service

2. **"Rate limit exceeded"**
   - Wait for rate limit to reset
   - Reduce `maxJobs` parameter
   - Check API usage limits

3. **"No jobs found"**
   - Check API credentials
   - Try different query terms
   - Check API service status

### **Debug Mode**

Enable debug logging:
```env
LOG_LEVEL=debug
```

### **Test Individual APIs**

```bash
# Test Adzuna only
curl -X POST http://localhost:3003/sync \
  -H "Content-Type: application/json" \
  -d '{"query": "developer", "location": "us", "maxJobs": 5}'
```

## 📈 Monitoring

### **Check Service Health**
```bash
curl http://localhost:3003/health
```

### **View Sync Statistics**
```bash
curl http://localhost:3003/sync/stats
```

### **Check Job Count**
```bash
curl http://localhost:3003/jobs?limit=1
```

## 🔒 Security Notes

- Never commit API keys to version control
- Use environment variables for all secrets
- Rotate API keys regularly
- Monitor API usage for unusual activity

## 📚 Additional Resources

- [Adzuna API Documentation](https://developer.adzuna.com/docs)
- [Indeed API Documentation](https://ads.indeed.com/jobroll/xmlfeed)
- [LinkedIn API Documentation](https://developer.linkedin.com/docs)
- [Glassdoor API Documentation](https://www.glassdoor.com/developer/)

## 🆘 Support

If you encounter issues:

1. Check the logs: `docker logs job-service`
2. Verify API credentials
3. Test with smaller `maxJobs` values
4. Check API service status pages
