import { Router } from 'express';
import { database } from '../database/connection';
import { ResponseUtil } from '../utils/response';

const router = Router();

/**
 * Basic health check
 */
router.get('/', (req, res) => {
  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV,
    version: process.env.API_VERSION ?? '1.0.0',
    database: {
      connected: database.isHealthy(),
      state: database.getConnectionState(),
    },
    memory: process.memoryUsage(),
  };

  const isHealthy = health.database.connected;

  if (isHealthy) {
    return res.json(ResponseUtil.success(health, 'Auth Service is healthy'));
  } else {
    return res.status(503).json({
      success: false,
      message: 'Auth Service is unhealthy',
      data: health,
    });
  }
});

export { router as healthRoutes };
