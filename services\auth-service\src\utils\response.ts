export interface ApiResponse<T = unknown> {
  success: boolean;
  message: string;
  data?: T | undefined;
  errors?: string[] | undefined;
  timestamp: string;
  path?: string | undefined;
  statusCode: number;
}

export class ResponseUtil {
  static success<T>(
    data: T,
    message: string = 'Success',
    statusCode: number = 200
  ): ApiResponse<T> {
    return {
      success: true,
      message,
      data,
      timestamp: new Date().toISOString(),
      statusCode,
    };
  }

  static error(
    message: string = 'Error occurred',
    statusCode: number = 500,
    errors?: string[],
    path?: string
  ): ApiResponse {
    return {
      success: false,
      message,
      errors: errors ?? [],
      timestamp: new Date().toISOString(),
      path,
      statusCode,
    };
  }

  static created<T>(
    data: T,
    message: string = 'Resource created successfully'
  ): ApiResponse<T> {
    return this.success(data, message, 201);
  }

  static updated<T>(
    data: T,
    message: string = 'Resource updated successfully'
  ): ApiResponse<T> {
    return this.success(data, message, 200);
  }

  static deleted(
    message: string = 'Resource deleted successfully'
  ): ApiResponse {
    return this.success(null, message, 204);
  }
}
