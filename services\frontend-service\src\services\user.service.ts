import { apiService } from './api';
import { User } from '@/types/api';

class UserService {
  // Profile management
  async getProfile(): Promise<User> {
    return apiService.get<User>('/users/profile');
  }

  async updateProfile(profileData: any): Promise<User> {
    return apiService.put<User>('/users/profile', profileData);
  }

  async updateBasicInfo(userData: { firstName: string; lastName: string }): Promise<User> {
    return apiService.put<User>('/users/profile', userData);
  }

  async updateAvatar(avatarUrl: string): Promise<User> {
    return apiService.post<User>('/users/avatar', { avatarUrl });
  }

  async uploadAvatar(file: File): Promise<User> {
    const formData = new FormData();
    formData.append('avatar', file);
    
    return apiService.post<User>('/users/avatar/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }

  async getOptimizedAvatarUrl(size: number = 300): Promise<{ optimizedUrl: string; originalUrl: string }> {
    return apiService.get<{ optimizedUrl: string; originalUrl: string }>(`/users/avatar/optimized?size=${size}`);
  }

  async deleteAvatar(): Promise<void> {
    return apiService.delete('/users/avatar');
  }

  // Account management
  async deactivateAccount(): Promise<void> {
    return apiService.post('/users/deactivate');
  }

  async deleteAccount(): Promise<void> {
    return apiService.delete('/users/account');
  }

  // Privacy settings
  async updatePrivacySettings(settings: {
    profileVisibility: 'public' | 'private' | 'connections';
    allowMessages: boolean;
    allowJobAlerts: boolean;
    allowMarketingEmails: boolean;
  }): Promise<void> {
    return apiService.put('/users/privacy', settings);
  }

  // Notification preferences
  async updateNotificationPreferences(preferences: {
    email: {
      jobAlerts: boolean;
      applicationUpdates: boolean;
      marketing: boolean;
      weeklyDigest: boolean;
    };
    push: {
      jobAlerts: boolean;
      applicationUpdates: boolean;
      messages: boolean;
    };
    sms: {
      applicationUpdates: boolean;
      interviews: boolean;
    };
  }): Promise<void> {
    return apiService.put('/users/notifications', preferences);
  }

  // Skills management
  async addSkill(skill: {
    name: string;
    level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
    yearsOfExperience: number;
  }): Promise<void> {
    return apiService.post('/users/skills', skill);
  }

  async updateSkill(skillId: string, skill: {
    name?: string;
    level?: 'beginner' | 'intermediate' | 'advanced' | 'expert';
    yearsOfExperience?: number;
  }): Promise<void> {
    return apiService.put(`/users/skills/${skillId}`, skill);
  }

  async deleteSkill(skillId: string): Promise<void> {
    return apiService.delete(`/users/skills/${skillId}`);
  }

  async verifySkill(skillId: string): Promise<void> {
    return apiService.post(`/users/skills/${skillId}/verify`);
  }

  // Education management
  async addEducation(education: {
    institution: string;
    degree: string;
    field: string;
    startDate: string;
    endDate?: string;
    gpa?: number;
    description?: string;
  }): Promise<void> {
    return apiService.post('/users/education', education);
  }

  async updateEducation(educationId: string, education: {
    institution?: string;
    degree?: string;
    field?: string;
    startDate?: string;
    endDate?: string;
    gpa?: number;
    description?: string;
  }): Promise<void> {
    return apiService.put(`/users/education/${educationId}`, education);
  }

  async deleteEducation(educationId: string): Promise<void> {
    return apiService.delete(`/users/education/${educationId}`);
  }

  // Experience management
  async addExperience(experience: {
    company: string;
    position: string;
    startDate: string;
    endDate?: string;
    current: boolean;
    description: string;
    skills: string[];
    achievements: string[];
    location?: string;
  }): Promise<void> {
    return apiService.post('/users/experience', experience);
  }

  async updateExperience(experienceId: string, experience: {
    company?: string;
    position?: string;
    startDate?: string;
    endDate?: string;
    current?: boolean;
    description?: string;
    skills?: string[];
    achievements?: string[];
    location?: string;
  }): Promise<void> {
    return apiService.put(`/users/experience/${experienceId}`, experience);
  }

  async deleteExperience(experienceId: string): Promise<void> {
    return apiService.delete(`/users/experience/${experienceId}`);
  }

  // Search and discovery
  async searchUsers(params: {
    q?: string;
    skills?: string[];
    location?: string;
    experience?: string;
    page?: number;
    limit?: number;
  }): Promise<{
    users: User[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    return apiService.get('/users/search', { params });
  }

  // Analytics
  async getProfileAnalytics(): Promise<{
    profileViews: number;
    profileViewsThisWeek: number;
    profileCompleteness: number;
    skillsMatchingJobs: number;
    recommendedActions: string[];
  }> {
    return apiService.get('/users/analytics');
  }

  // Export data
  async exportData(): Promise<void> {
    return apiService.download('/users/export', 'user-data.json');
  }
}

export const userService = new UserService();
