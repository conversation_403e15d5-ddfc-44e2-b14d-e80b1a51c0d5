import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import morgan from 'morgan';
import axios from 'axios';
// Using DigitalOcean environment variables directly
import { database } from './database/connection';
import { logger } from './utils/logger';
import { errorHandler } from './middleware/error.middleware';
import { rateLimitMiddleware } from './middleware/rate-limit.middleware';
import { healthCheckRoutes } from './routes/health.routes';
import { metricsRoutes } from './routes/metrics.routes';
import { serviceRegistry } from './services/service-registry';

class ApiGateway {
  private app: express.Application;
  private server: ReturnType<express.Application['listen']> | null = null;

  constructor() {
    this.app = express();
    // Enable trust proxy for proper IP detection behind reverse proxy
    this.app.set('trust proxy', true);
    this.setupMiddleware();
    this.setupRoutes();
    this.setupProxies();
    this.setupErrorHandling();
  }

  private setupMiddleware(): void {
    // Security middleware
    this.app.use(
      helmet({
        contentSecurityPolicy: {
          directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", 'data:', 'https:'],
          },
        },
        crossOriginEmbedderPolicy: false,
      })
    );

    // CORS configuration
    this.app.use(
      cors({
        origin: process.env.NODE_ENV === 'development'
          ? true
          : (process.env.CORS_ORIGIN?.split(',') ?? '*'),
        credentials: true,
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
        allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
      })
    );

    // General middleware
    this.app.use(compression());
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Logging
    this.app.use(
      morgan('combined', {
        stream: { write: message => logger.info(message.trim()) },
      })
    );

    // Rate limiting
    this.app.use(rateLimitMiddleware);

    // Request ID middleware
    this.app.use((req, res, next) => {
      req.headers['x-request-id'] =
        req.headers['x-request-id'] ??
        Math.random().toString(36).substring(2, 15);
      res.setHeader('X-Request-ID', req.headers['x-request-id'] as string);
      next();
    });
  }

  private setupRoutes(): void {
    // Health check routes
    this.app.use('/health', healthCheckRoutes);

    // Metrics routes (admin only)
    this.app.use('/metrics', metricsRoutes);

    // Root route
    this.app.get('/', (req, res) => {
      res.json({
        name: 'Job Application Platform API Gateway',
        version: '1.0.0',
        environment: process.env.NODE_ENV || 'development',
        timestamp: new Date().toISOString(),
        message: 'API Gateway is running. Use /api/v1/* for API endpoints.',
        services: serviceRegistry.getServiceList(),
      });
    });

    // API version info
    this.app.get('/api', (req, res) => {
      res.json({
        name: 'Job Application Platform API Gateway',
        version: '1.0.0',
        environment: process.env.NODE_ENV || 'development',
        timestamp: new Date().toISOString(),
        services: serviceRegistry.getServiceList(),
      });
    });
  }

  private setupProxies(): void {
    // Simple, reliable forwarding using axios
    const createProxyHandler = (serviceUrl: string, serviceName: string) => {
      return async (req: express.Request, res: express.Response) => {
        try {
          // Remove /api/v1 prefix and forward to the service
          const forwardPath = req.originalUrl.replace('/api/v1', '');
          const targetUrl = `${serviceUrl}${forwardPath}`;

          logger.debug(
            `Forwarding ${req.method} ${req.originalUrl} to ${targetUrl}`
          );

          const response = await axios({
            method: req.method,
            url: targetUrl,
            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
            data: req.body,
            headers: {
              ...req.headers,
              host: undefined, // Remove host header to avoid conflicts
            },
            timeout: 20000, // 20 seconds for file uploads (reasonable)
            maxContentLength: 3 * 1024 * 1024, // 3MB max content length
            maxBodyLength: 3 * 1024 * 1024, // 3MB max body length
          });

          res.status(response.status).json(response.data);
        } catch (error: unknown) {
          logger.error(`${serviceName} service proxy error:`, error);
          if (error && typeof error === 'object' && 'response' in error) {
            const axiosError = error as {
              response: { status: number; data: unknown };
            };
            res.status(axiosError.response.status).json(axiosError.response.data);
          } else {
            const errorMessage =
              error instanceof Error ? error.message : 'Unknown error';
            res.status(500).json({
              success: false,
              message: `${serviceName} service unavailable`,
              error: errorMessage,
            });
          }
        }
      };
    };

    // Handle /v1/auth/* routes directly (for backward compatibility)
    this.app.use('/v1/auth', async (req: express.Request, res: express.Response) => {
      try {
        const serviceUrl = process.env.AUTH_SERVICE_URL || 'http://resume-automator-services-auth-s:3001';
        // Forward to auth service with /api/v1 prefix
        const forwardPath = `/api/v1${req.path}`;
        const targetUrl = `${serviceUrl}${forwardPath}`;

        logger.debug(
          `Forwarding ${req.method} ${req.originalUrl} to ${targetUrl}`
        );

        const response = await axios({
          method: req.method,
          url: targetUrl,
          // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
          data: req.body,
          headers: {
            ...req.headers,
            host: undefined, // Remove host header to avoid conflicts
          },
          timeout: 10000,
          maxRedirects: 0, // Don't follow redirects automatically
          validateStatus: (status) => status < 400 || status === 302, // Accept redirects
        });

        // Handle OAuth redirects (302 status codes)
        if (response.status === 302 && response.headers.location) {
          logger.debug(`OAuth redirect to: ${response.headers.location}`);
          return res.redirect(response.headers.location);
        }

        // For non-redirect responses, return JSON as usual
        res.status(response.status).json(response.data);
      } catch (error: unknown) {
        // Handle axios redirect errors (which are expected for OAuth)
        if (error && typeof error === 'object' && 'response' in error) {
          const axiosError = error as {
            response: { status: number; data: unknown; headers: { location?: string } };
          };

          // Handle OAuth redirects that axios treats as errors
          if (axiosError.response.status === 302 && axiosError.response.headers.location) {
            logger.debug(`OAuth redirect to: ${axiosError.response.headers.location}`);
            return res.redirect(axiosError.response.headers.location);
          }

          res.status(axiosError.response.status).json(axiosError.response.data);
        } else {
          logger.error('Auth service proxy error:', error);
          const errorMessage =
            error instanceof Error ? error.message : 'Unknown error';
          res.status(500).json({
            success: false,
            message: 'Auth service unavailable',
            error: errorMessage,
          });
        }
      }
    });

    // Handle /v1/applications/* routes (temporary fix for frontend)
    this.app.use('/v1/applications', async (req: express.Request, res: express.Response) => {
      try {
        const serviceUrl = process.env.JOB_SERVICE_URL || 'http://resume-automator-services-job-se:3003';
        // Forward to job service with /api/v1 prefix
        const forwardPath = `/api/v1${req.path}`;
        const targetUrl = `${serviceUrl}${forwardPath}`;

        logger.debug(
          `Forwarding ${req.method} ${req.originalUrl} to ${targetUrl}`
        );

        const response = await axios({
          method: req.method,
          url: targetUrl,
          // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
          data: req.body,
          headers: {
            ...req.headers,
            host: undefined, // Remove host header to avoid conflicts
          },
          timeout: 10000,
        });

        res.status(response.status).json(response.data);
      } catch (error: unknown) {
        logger.error('Job service proxy error:', error);
        if (error && typeof error === 'object' && 'response' in error) {
          const axiosError = error as {
            response: { status: number; data: unknown };
          };
          res.status(axiosError.response.status).json(axiosError.response.data);
        } else {
          const errorMessage =
            error instanceof Error ? error.message : 'Unknown error';
          res.status(500).json({
            success: false,
            message: 'Job service unavailable',
            error: errorMessage,
          });
        }
      }
    });

    // Handle /resumes/* routes
    this.app.use('/resumes', async (req: express.Request, res: express.Response) => {
      try {
        const serviceUrl = process.env.RESUME_SERVICE_URL || 'http://resume-automator-services-resume:3005';
        // Forward to resume service with /api/v1 prefix
        const forwardPath = `/api/v1${req.path}`;
        const targetUrl = `${serviceUrl}${forwardPath}`;

        logger.debug(
          `Forwarding ${req.method} ${req.originalUrl} to ${targetUrl}`
        );

        const response = await axios({
          method: req.method,
          url: targetUrl,
          // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
          data: req.body,
          headers: {
            ...req.headers,
            host: undefined, // Remove host header to avoid conflicts
          },
          timeout: 10000,
        });

        res.status(response.status).json(response.data);
      } catch (error: unknown) {
        logger.error('Resume service proxy error:', error);
        if (error && typeof error === 'object' && 'response' in error) {
          const axiosError = error as {
            response: { status: number; data: unknown };
          };
          res.status(axiosError.response.status).json(axiosError.response.data);
        } else {
          const errorMessage =
            error instanceof Error ? error.message : 'Unknown error';
          res.status(500).json({
            success: false,
            message: 'Resume service unavailable',
            error: errorMessage,
          });
        }
      }
    });

    // Handle /v1/resumes/* routes (backward compatibility)
    this.app.use('/v1/resumes', async (req: express.Request, res: express.Response) => {
      try {
        const serviceUrl = process.env.RESUME_SERVICE_URL || 'http://resume-automator-services-resume:3005';
        // Forward to resume service with /api/v1 prefix
        const forwardPath = `/api/v1${req.path}`;
        const targetUrl = `${serviceUrl}${forwardPath}`;

        logger.debug(
          `Forwarding ${req.method} ${req.originalUrl} to ${targetUrl}`
        );

        const response = await axios({
          method: req.method,
          url: targetUrl,
          // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
          data: req.body,
          headers: {
            ...req.headers,
            host: undefined, // Remove host header to avoid conflicts
          },
          timeout: 10000,
        });

        res.status(response.status).json(response.data);
      } catch (error: unknown) {
        logger.error('Resume service proxy error:', error);
        if (error && typeof error === 'object' && 'response' in error) {
          const axiosError = error as {
            response: { status: number; data: unknown };
          };
          res.status(axiosError.response.status).json(axiosError.response.data);
        } else {
          const errorMessage =
            error instanceof Error ? error.message : 'Unknown error';
          res.status(500).json({
            success: false,
            message: 'Resume service unavailable',
            error: errorMessage,
          });
        }
      }
    });

    // Auth Service - Special handler for OAuth redirects
    this.app.use('/api/v1/auth', async (req: express.Request, res: express.Response) => {
      try {
        const serviceUrl = process.env.AUTH_SERVICE_URL || 'http://resume-automator-services-auth-s:3001';
        // Remove /api/v1/auth prefix and forward to the service
        const forwardPath = req.originalUrl.replace('/api/v1/auth', '/api/v1');
        const targetUrl = `${serviceUrl}${forwardPath}`;

        logger.debug(
          `Forwarding ${req.method} ${req.originalUrl} to ${targetUrl}`
        );

        const response = await axios({
          method: req.method,
          url: targetUrl,
          // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
          data: req.body,
          headers: {
            ...req.headers,
            host: undefined, // Remove host header to avoid conflicts
          },
          timeout: 10000,
          maxRedirects: 0, // Don't follow redirects automatically
          validateStatus: (status) => status < 400 || status === 302, // Accept redirects
        });

        // Handle OAuth redirects (302 status codes)
        if (response.status === 302 && response.headers.location) {
          logger.debug(`OAuth redirect to: ${response.headers.location}`);
          return res.redirect(response.headers.location);
        }

        // For non-redirect responses, return JSON as usual
        res.status(response.status).json(response.data);
      } catch (error: unknown) {
        // Handle axios redirect errors (which are expected for OAuth)
        if (error && typeof error === 'object' && 'response' in error) {
          const axiosError = error as {
            response: { status: number; data: unknown; headers: { location?: string } };
          };

          // Handle OAuth redirects that axios treats as errors
          if (axiosError.response.status === 302 && axiosError.response.headers.location) {
            logger.debug(`OAuth redirect to: ${axiosError.response.headers.location}`);
            return res.redirect(axiosError.response.headers.location);
          }

          res.status(axiosError.response.status).json(axiosError.response.data);
        } else {
          logger.error('Auth service proxy error:', error);
          const errorMessage =
            error instanceof Error ? error.message : 'Unknown error';
          res.status(500).json({
            success: false,
            message: 'Auth service unavailable',
            error: errorMessage,
          });
        }
      }
    });

    // User Service
    this.app.use('/api/v1/users', createProxyHandler(
      process.env.USER_SERVICE_URL || 'http://resume-automator-services-user-s:3002', 'user-service'
    ));

    // Job Service
    this.app.use('/api/v1/jobs', createProxyHandler(
      process.env.JOB_SERVICE_URL || 'http://resume-automator-services-job-se:3003', 'job-service'
    ));
    this.app.use('/api/v1/applications', createProxyHandler(
      process.env.JOB_SERVICE_URL || 'http://resume-automator-services-job-se:3003', 'job-service'
    ));

    // Resume Service
    this.app.use('/api/v1/resumes', createProxyHandler(
      process.env.RESUME_SERVICE_URL || 'http://resume-automator-services-resume:3005', 'resume-service'
    ));

    // Analytics Service (if implemented)
    this.app.use('/api/v1/analytics', createProxyHandler(
      process.env.ANALYTICS_SERVICE_URL || 'http://analytics-service:3006', 'analytics-service'
    ));

    // Notification Service (if implemented)
    this.app.use('/api/v1/notifications', createProxyHandler(
      process.env.NOTIFICATION_SERVICE_URL || 'http://notification-service:3007', 'notification-service'
    ));

    // Integration Service (if implemented)
    this.app.use('/api/v1/integrations', createProxyHandler(
      process.env.INTEGRATION_SERVICE_URL || 'http://integration-service:3008', 'integration-service'
    ));

    // Payment Service (if implemented)
    this.app.use('/api/v1/payments', createProxyHandler(
      process.env.PAYMENT_SERVICE_URL || 'http://payment-service:3009', 'payment-service'
    ));

    // Catch-all for unknown API routes
    this.app.use('/api', (req, res) => {
      res.status(404).json({
        success: false,
        message: 'API endpoint not found',
        path: req.path,
        availableRoutes: [
          '/api/v1/auth/*',
          '/api/v1/users/*',
          '/api/v1/jobs/*',
          '/api/v1/applications/*',
          '/api/v1/resumes/*',
          '/api/v1/analytics/*',
          '/api/v1/notifications/*',
          '/api/v1/integrations/*',
          '/api/v1/payments/*',
        ],
      });
    });
  }

  private setupErrorHandling(): void {
    this.app.use(errorHandler);

    // 404 handler
    this.app.use((req, res) => {
      res.status(404).json({
        success: false,
        message: 'Route not found',
        path: req.path,
      });
    });
  }

  public async start(): Promise<void> {
    try {
      // Start server first to respond to health checks
      const port = parseInt(process.env.PORT || '3000');
      this.server = this.app.listen(port, '0.0.0.0', () => {
        logger.info(`🚀 API Gateway running on port ${port}`);
        logger.info(`Environment: ${process.env.NODE_ENV || 'development'}`);
        logger.info('Available routes:');
        logger.info('  GET  /health - Health check');
        logger.info('  GET  /metrics - Service metrics');
        logger.info('  GET  /api - API information');
        logger.info('  *    /api/v1/auth/* - Authentication');
        logger.info('  *    /api/v1/users/* - User management');
        logger.info('  *    /api/v1/jobs/* - Job management');
        logger.info('  *    /api/v1/applications/* - Application management');
        logger.info('  *    /api/v1/resumes/* - Resume management');
        logger.info('  *    /api/v1/analytics/* - Analytics');
        logger.info('  *    /api/v1/notifications/* - Notifications');
        logger.info('  *    /api/v1/integrations/* - Integrations');
        logger.info('  *    /api/v1/payments/* - Payments');
      });

      // Connect to database after server is running
      try {
        await database.connect();
        logger.info('Database connected successfully');
      } catch (dbError) {
        logger.warn('Database connection failed - service will run with limited functionality:', dbError);
        // Continue running without database - some features will be disabled
      }

      // Graceful shutdown
      process.on('SIGTERM', () => void this.gracefulShutdown());
      process.on('SIGINT', () => void this.gracefulShutdown());
    } catch (error) {
      logger.error('Failed to start API Gateway:', error);
      process.exit(1);
    }
  }

  private async gracefulShutdown(): Promise<void> {
    logger.info('Shutting down API Gateway gracefully...');

    if (this.server) {
      await new Promise<void>(resolve => {
        this.server!.close(() => {
          logger.info('HTTP server closed');
          resolve();
        });
      });
    }

    try {
      await database.disconnect();
      logger.info('Database disconnected');
    } catch (error) {
      logger.error('Error disconnecting from database:', error);
    }

    process.exit(0);
  }
}

// Start the API Gateway
const apiGateway = new ApiGateway();
apiGateway.start().catch(error => {
  logger.error('Failed to start API Gateway:', error);
  process.exit(1);
});

export default ApiGateway;
