# Job Application Automator - Frontend Service

A modern React frontend application for the Job Application Automator platform, built with TypeScript, Vite, and Tailwind CSS.

## 🚀 Features

- **Modern Tech Stack**: React 18, TypeScript, Vite, Tailwind CSS
- **Authentication**: JWT-based authentication with refresh tokens
- **Responsive Design**: Mobile-first design with modern UI components
- **Job Management**: Browse, search, and apply to jobs
- **Resume Management**: Upload, analyze, and optimize resumes
- **Application Tracking**: Track application status and progress
- **User Profiles**: Comprehensive user profile management
- **Real-time Updates**: Live notifications and updates

## 🛠️ Tech Stack

- **Frontend**: React 18, TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS
- **State Management**: React Query + Context API
- **Routing**: React Router v6
- **Forms**: React Hook Form
- **HTTP Client**: Axios
- **Icons**: Lucide React
- **Notifications**: React Hot Toast

## 📦 Installation

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Start development server**:
   ```bash
   npm run dev
   ```

3. **Build for production**:
   ```bash
   npm run build
   ```

## 🏗️ Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── auth/           # Authentication components
│   ├── layout/         # Layout components
│   └── ui/             # Base UI components
├── contexts/           # React contexts
├── hooks/              # Custom React hooks
├── pages/              # Page components
│   ├── auth/           # Authentication pages
│   ├── dashboard/      # Dashboard pages
│   ├── jobs/           # Job-related pages
│   ├── applications/   # Application pages
│   ├── resumes/        # Resume pages
│   ├── profile/        # Profile pages
│   └── settings/       # Settings pages
├── services/           # API services
├── types/              # TypeScript type definitions
├── utils/              # Utility functions
├── App.tsx             # Main app component
└── main.tsx            # App entry point
```

## 🔧 Configuration

### Environment Variables

Create a `.env` file in the root directory:

```env
VITE_API_BASE_URL=http://localhost:3000
VITE_APP_NAME=Job Application Automator
VITE_APP_VERSION=1.0.0
```

### API Integration

The frontend communicates with the backend API gateway at `http://localhost:3000`. All API calls are proxied through Vite's development server.

## 🎨 UI Components

The application uses a custom design system built with Tailwind CSS:

- **Colors**: Primary (blue), secondary (gray), success (green), warning (yellow), error (red)
- **Components**: Buttons, inputs, cards, badges, modals, etc.
- **Layout**: Responsive grid system with mobile-first approach

## 🔐 Authentication

- JWT-based authentication with automatic token refresh
- Protected routes with role-based access control
- Persistent login state with localStorage
- Google OAuth integration

## 📱 Responsive Design

- Mobile-first approach
- Responsive navigation with collapsible sidebar
- Optimized for all screen sizes
- Touch-friendly interface

## 🧪 Testing

```bash
# Run tests
npm run test

# Run tests with UI
npm run test:ui

# Run tests with coverage
npm run test:coverage
```

## 📝 Code Quality

```bash
# Lint code
npm run lint

# Fix linting issues
npm run lint:fix

# Type checking
npm run type-check
```

## 🚀 Deployment

### Production Build

```bash
npm run build
```

### Preview Production Build

```bash
npm run preview
```

### Docker Deployment

```dockerfile
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

## 🔗 API Integration

The frontend integrates with the following backend services:

- **Auth Service** (`/api/v1/auth/*`) - Authentication and authorization
- **User Service** (`/api/v1/users/*`) - User profile management
- **Job Service** (`/api/v1/jobs/*`) - Job listings and management
- **Application Service** (`/api/v1/applications/*`) - Application tracking
- **Resume Service** (`/api/v1/resumes/*`) - Resume management

## 📊 Performance

- Code splitting with React.lazy()
- Optimized bundle sizes
- Image optimization
- Caching strategies
- Progressive loading

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support, email <EMAIL> or join our Slack channel.

---

Built with ❤️ by the Job Application Automator Team
