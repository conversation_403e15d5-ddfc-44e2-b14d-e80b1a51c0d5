import React from 'react';
import { <PERSON>, Shield, User, CreditCard, Download, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Checkbox } from '@/components/ui/Checkbox';

export const SettingsPage: React.FC = () => {
  return (
    <div className="p-6">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Settings</h1>
          <p className="text-gray-600 mt-2">
            Manage your account settings and preferences
          </p>
        </div>

        <div className="space-y-6">
          {/* Account Settings */}
          <div className="card">
            <div className="card-header">
              <div className="flex items-center gap-2">
                <User className="w-5 h-5 text-gray-600" />
                <h2 className="text-lg font-semibold text-gray-900">Account Settings</h2>
              </div>
            </div>
            <div className="card-body space-y-4">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                <div className="min-w-0 flex-1">
                  <h3 className="font-medium text-gray-900">Email Address</h3>
                  <p className="text-sm text-gray-600 truncate"><EMAIL></p>
                </div>
                <Button variant="outline" size="sm" className="w-full sm:w-auto">Change</Button>
              </div>
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                <div className="min-w-0 flex-1">
                  <h3 className="font-medium text-gray-900">Password</h3>
                  <p className="text-sm text-gray-600">Last changed 30 days ago</p>
                </div>
                <Button variant="outline" size="sm" className="w-full sm:w-auto">Change</Button>
              </div>
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                <div className="min-w-0 flex-1">
                  <h3 className="font-medium text-gray-900">Two-Factor Authentication</h3>
                  <p className="text-sm text-gray-600">Add an extra layer of security</p>
                </div>
                <Button variant="outline" size="sm" className="w-full sm:w-auto">Enable</Button>
              </div>
            </div>
          </div>

          {/* Notification Settings */}
          <div className="card">
            <div className="card-header">
              <div className="flex items-center gap-2">
                <Bell className="w-5 h-5 text-gray-600" />
                <h2 className="text-lg font-semibold text-gray-900">Notification Settings</h2>
              </div>
            </div>
            <div className="card-body space-y-4">
              <div>
                <h3 className="font-medium text-gray-900 mb-3">Email Notifications</h3>
                <div className="space-y-3">
                  <Checkbox
                    id="jobAlerts"
                    label="Job Alerts"
                    description="Receive notifications about new job opportunities"
                    defaultChecked
                  />
                  <Checkbox
                    id="applicationUpdates"
                    label="Application Updates"
                    description="Get notified when your application status changes"
                    defaultChecked
                  />
                  <Checkbox
                    id="weeklyDigest"
                    label="Weekly Digest"
                    description="Weekly summary of your job search activity"
                  />
                  <Checkbox
                    id="marketingEmails"
                    label="Marketing Emails"
                    description="Receive tips and product updates"
                  />
                </div>
              </div>
              
              <div>
                <h3 className="font-medium text-gray-900 mb-3">Push Notifications</h3>
                <div className="space-y-3">
                  <Checkbox
                    id="pushJobAlerts"
                    label="Job Alerts"
                    description="Push notifications for new job matches"
                    defaultChecked
                  />
                  <Checkbox
                    id="pushApplications"
                    label="Application Updates"
                    description="Push notifications for application status changes"
                    defaultChecked
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Privacy Settings */}
          <div className="card">
            <div className="card-header">
              <div className="flex items-center gap-2">
                <Shield className="w-5 h-5 text-gray-600" />
                <h2 className="text-lg font-semibold text-gray-900">Privacy Settings</h2>
              </div>
            </div>
            <div className="card-body space-y-4">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                <div className="min-w-0 flex-1">
                  <label htmlFor="profile-visibility" className="font-medium text-gray-900">Profile Visibility</label>
                  <p id="profile-visibility-description" className="text-sm text-gray-600">Control who can see your profile</p>
                </div>
                <select
                  id="profile-visibility"
                  name="profileVisibility"
                  className="w-full sm:w-auto border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-primary-500 focus:border-primary-500"
                  title="Select who can view your profile"
                  aria-describedby="profile-visibility-description"
                >
                  <option value="public">Public</option>
                  <option value="private">Private</option>
                  <option value="connections">Connections only</option>
                </select>
              </div>
              <div className="space-y-3">
                <Checkbox
                  id="allowMessages"
                  label="Allow messages from recruiters"
                  description="Let recruiters contact you directly"
                  defaultChecked
                />
                <Checkbox
                  id="showSalary"
                  label="Show salary expectations"
                  description="Display your salary range on your profile"
                />
                <Checkbox
                  id="anonymousMode"
                  label="Anonymous job browsing"
                  description="Browse jobs without companies seeing your profile"
                />
              </div>
            </div>
          </div>

          {/* Subscription */}
          <div className="card">
            <div className="card-header">
              <div className="flex items-center gap-2">
                <CreditCard className="w-5 h-5 text-gray-600" />
                <h2 className="text-lg font-semibold text-gray-900">Subscription</h2>
              </div>
            </div>
            <div className="card-body">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                <div className="min-w-0 flex-1">
                  <h3 className="font-medium text-gray-900">Current Plan</h3>
                  <p className="text-sm text-gray-600">Free Plan - Basic features included</p>
                </div>
                <Button className="w-full sm:w-auto">Upgrade to Premium</Button>
              </div>
              
              <div className="mt-6 p-4 bg-primary-50 rounded-lg border border-primary-200">
                <h4 className="font-medium text-primary-900 mb-2">Premium Benefits</h4>
                <ul className="text-sm text-primary-800 space-y-1">
                  <li>• Unlimited resume uploads and optimization</li>
                  <li>• Advanced job matching and alerts</li>
                  <li>• Priority customer support</li>
                  <li>• Detailed application analytics</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Data & Privacy */}
          <div className="card">
            <div className="card-header">
              <div className="flex items-center gap-2">
                <Download className="w-5 h-5 text-gray-600" />
                <h2 className="text-lg font-semibold text-gray-900">Data & Privacy</h2>
              </div>
            </div>
            <div className="card-body space-y-4">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                <div className="min-w-0 flex-1">
                  <h3 className="font-medium text-gray-900">Download Your Data</h3>
                  <p className="text-sm text-gray-600">Get a copy of all your data</p>
                </div>
                <Button variant="outline" size="sm" className="w-full sm:w-auto">
                  <Download className="w-4 h-4 mr-2" />
                  Download
                </Button>
              </div>
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                <div className="min-w-0 flex-1">
                  <h3 className="font-medium text-gray-900">Delete Account</h3>
                  <p className="text-sm text-gray-600">Permanently delete your account and all data</p>
                </div>
                <Button variant="outline" size="sm" className="w-full sm:w-auto text-error-600 border-error-600 hover:bg-error-50 hover:text-error-600">
                  <Trash2 className="w-4 h-4 mr-2" />
                  Delete
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Save changes */}
        <div className="flex flex-col sm:flex-row justify-end gap-2 mt-8">
          <Button variant="outline" className="w-full sm:w-auto">Cancel</Button>
          <Button className="w-full sm:w-auto">Save Changes</Button>
        </div>
      </div>
    </div>
  );
};
