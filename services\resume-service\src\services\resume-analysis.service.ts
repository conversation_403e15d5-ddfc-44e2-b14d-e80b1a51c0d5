import { logger } from '../utils/logger';
import { ParsedResumeContent } from './resume-parser.service';

export interface ResumeAnalysis {
  overallScore: number;
  atsScore: number;
  readabilityScore: number;
  keywordMatch: number;
  skillsMatch: number;
  experienceMatch: number;
  educationMatch: number;
  recommendedImprovements: string[];
  strengths: string[];
  weaknesses: string[];
  missingKeywords: string[];
  lastAnalyzed: string;
}

export interface JobDescription {
  title: string;
  requirements: string[];
  skills: string[];
  experience: string;
  education: string;
}

export class ResumeAnalysisService {
  private readonly atsKeywords = [
    'teamwork', 'leadership', 'communication', 'problem solving', 'analytical',
    'detail oriented', 'self motivated', 'results driven', 'innovative',
    'collaborative', 'strategic thinking', 'time management', 'project management'
  ];

  private readonly strongActionVerbs = [
    'achieved', 'accomplished', 'developed', 'implemented', 'managed', 'led',
    'created', 'designed', 'built', 'improved', 'increased', 'reduced',
    'optimized', 'streamlined', 'launched', 'delivered', 'executed', 'coordinated'
  ];

  private readonly weakWords = [
    'assisted', 'helped', 'supported', 'participated', 'involved', 'contributed',
    'worked on', 'was responsible for', 'duties included'
  ];

  /**
   * Analyze resume content and generate comprehensive analysis
   */
  public async analyzeResume(
    content: ParsedResumeContent, 
    jobDescription?: JobDescription
  ): Promise<ResumeAnalysis> {
    try {
      logger.info('Starting resume analysis...');

      const analysis: ResumeAnalysis = {
        overallScore: 0,
        atsScore: 0,
        readabilityScore: 0,
        keywordMatch: 0,
        skillsMatch: 0,
        experienceMatch: 0,
        educationMatch: 0,
        recommendedImprovements: [],
        strengths: [],
        weaknesses: [],
        missingKeywords: [],
        lastAnalyzed: new Date().toISOString()
      };

      // Calculate ATS score
      analysis.atsScore = this.calculateATSScore(content);
      
      // Calculate readability score
      analysis.readabilityScore = this.calculateReadabilityScore(content);
      
      // Calculate keyword match if job description provided
      if (jobDescription) {
        analysis.keywordMatch = this.calculateKeywordMatch(content, jobDescription);
        analysis.skillsMatch = this.calculateSkillsMatch(content, jobDescription);
        analysis.experienceMatch = this.calculateExperienceMatch(content, jobDescription);
        analysis.educationMatch = this.calculateEducationMatch(content, jobDescription);
      }

      // Calculate overall score
      analysis.overallScore = this.calculateOverallScore(analysis);

      // Generate recommendations
      analysis.recommendedImprovements = this.generateRecommendations(content, analysis);
      analysis.strengths = this.identifyStrengths(content, analysis);
      analysis.weaknesses = this.identifyWeaknesses(content, analysis);
      
      if (jobDescription) {
        analysis.missingKeywords = this.identifyMissingKeywords(content, jobDescription);
      }

      logger.info('Resume analysis completed', {
        overallScore: analysis.overallScore,
        atsScore: analysis.atsScore,
        readabilityScore: analysis.readabilityScore
      });

      return analysis;
    } catch (error) {
      logger.error('Resume analysis failed:', error);
      throw new Error(`Resume analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Calculate ATS compatibility score
   */
  private calculateATSScore(content: ParsedResumeContent): number {
    let score = 0;
    const maxScore = 100;

    // Check for essential sections
    if (content.personalInfo.email) score += 10;
    if (content.personalInfo.phone) score += 10;
    if (content.experience.length > 0) score += 20;
    if (content.education.length > 0) score += 15;
    if (content.skills.length > 0) score += 15;

    // Check for ATS keywords
    const allText = this.getAllText(content).toLowerCase();
    const foundKeywords = this.atsKeywords.filter(keyword => 
      allText.includes(keyword.toLowerCase())
    );
    score += Math.min(foundKeywords.length * 2, 20);

    // Check for strong action verbs
    const foundActionVerbs = this.strongActionVerbs.filter(verb => 
      allText.includes(verb.toLowerCase())
    );
    score += Math.min(foundActionVerbs.length * 1.5, 10);

    return Math.min(score, maxScore);
  }

  /**
   * Calculate readability score
   */
  private calculateReadabilityScore(content: ParsedResumeContent): number {
    let score = 0;
    const maxScore = 100;

    // Check for clear structure
    if (content.personalInfo.firstName && content.personalInfo.lastName) score += 15;
    if (content.summary && content.summary.length > 50) score += 15;
    if (content.experience.length > 0) score += 20;
    if (content.education.length > 0) score += 15;
    if (content.skills.length > 0) score += 15;

    // Check for quantified achievements
    const allText = this.getAllText(content);
    const hasNumbers = /\d+/.test(allText);
    if (hasNumbers) score += 10;

    // Check for action verbs
    const hasActionVerbs = this.strongActionVerbs.some(verb => 
      allText.toLowerCase().includes(verb.toLowerCase())
    );
    if (hasActionVerbs) score += 10;

    return Math.min(score, maxScore);
  }

  /**
   * Calculate keyword match score
   */
  private calculateKeywordMatch(content: ParsedResumeContent, jobDescription: JobDescription): number {
    const resumeText = this.getAllText(content).toLowerCase();
    const jobText = [
      jobDescription.title,
      ...jobDescription.requirements,
      ...jobDescription.skills
    ].join(' ').toLowerCase();

    const jobKeywords = jobText.split(/\W+/).filter(word => word.length > 3);
    const matchedKeywords = jobKeywords.filter(keyword => 
      resumeText.includes(keyword.toLowerCase())
    );

    return jobKeywords.length > 0 ? (matchedKeywords.length / jobKeywords.length) * 100 : 0;
  }

  /**
   * Calculate skills match score
   */
  private calculateSkillsMatch(content: ParsedResumeContent, jobDescription: JobDescription): number {
    const resumeSkills = content.skills.map(skill => skill.toLowerCase());
    const jobSkills = jobDescription.skills.map(skill => skill.toLowerCase());

    const matchedSkills = jobSkills.filter(jobSkill => 
      resumeSkills.some(resumeSkill => 
        resumeSkill.includes(jobSkill) || jobSkill.includes(resumeSkill)
      )
    );

    return jobSkills.length > 0 ? (matchedSkills.length / jobSkills.length) * 100 : 0;
  }

  /**
   * Calculate experience match score
   */
  private calculateExperienceMatch(content: ParsedResumeContent, jobDescription: JobDescription): number {
    if (content.experience.length === 0) return 0;

    // Simple experience matching based on keywords
    const experienceText = content.experience.map(exp => 
      `${exp.position} ${exp.description} ${exp.company}`
    ).join(' ').toLowerCase();

    const jobText = jobDescription.title.toLowerCase();
    const hasRelevantExperience = experienceText.includes(jobText) || 
      jobDescription.requirements.some(req => experienceText.includes(req.toLowerCase()));

    return hasRelevantExperience ? 80 : 40;
  }

  /**
   * Calculate education match score
   */
  private calculateEducationMatch(content: ParsedResumeContent, jobDescription: JobDescription): number {
    if (content.education.length === 0) return 0;

    const educationText = content.education.map(edu => 
      `${edu.degree} ${edu.field} ${edu.institution}`
    ).join(' ').toLowerCase();

    const jobEducation = jobDescription.education.toLowerCase();
    const hasRelevantEducation = educationText.includes(jobEducation) || 
      jobEducation.includes('degree') && educationText.includes('degree');

    return hasRelevantEducation ? 90 : 60;
  }

  /**
   * Calculate overall score
   */
  private calculateOverallScore(analysis: ResumeAnalysis): number {
    const weights = {
      ats: 0.3,
      readability: 0.2,
      keywordMatch: 0.2,
      skillsMatch: 0.15,
      experienceMatch: 0.1,
      educationMatch: 0.05
    };

    return Math.round(
      analysis.atsScore * weights.ats +
      analysis.readabilityScore * weights.readability +
      analysis.keywordMatch * weights.keywordMatch +
      analysis.skillsMatch * weights.skillsMatch +
      analysis.experienceMatch * weights.experienceMatch +
      analysis.educationMatch * weights.educationMatch
    );
  }

  /**
   * Generate improvement recommendations
   */
  private generateRecommendations(content: ParsedResumeContent, analysis: ResumeAnalysis): string[] {
    const recommendations: string[] = [];

    if (analysis.atsScore < 70) {
      recommendations.push('Add more ATS-friendly keywords and action verbs');
      recommendations.push('Ensure all sections are clearly labeled and structured');
    }

    if (analysis.readabilityScore < 70) {
      recommendations.push('Add a professional summary section');
      recommendations.push('Include quantified achievements with specific numbers');
    }

    if (content.skills.length < 5) {
      recommendations.push('Add more relevant technical and soft skills');
    }

    if (content.experience.length === 0) {
      recommendations.push('Add work experience section with detailed descriptions');
    }

    if (content.education.length === 0) {
      recommendations.push('Include education background');
    }

    if (analysis.keywordMatch < 50) {
      recommendations.push('Tailor keywords to match job requirements');
    }

    if (!content.personalInfo.email || !content.personalInfo.phone) {
      recommendations.push('Ensure contact information is complete and professional');
    }

    return recommendations;
  }

  /**
   * Identify resume strengths
   */
  private identifyStrengths(content: ParsedResumeContent, analysis: ResumeAnalysis): string[] {
    const strengths: string[] = [];

    if (content.skills.length > 10) {
      strengths.push('Comprehensive skill set');
    }

    if (content.experience.length > 3) {
      strengths.push('Extensive work experience');
    }

    if (content.summary && content.summary.length > 100) {
      strengths.push('Strong professional summary');
    }

    if (analysis.atsScore > 80) {
      strengths.push('ATS-optimized format');
    }

    if (content.certifications && content.certifications.length > 0) {
      strengths.push('Professional certifications');
    }

    return strengths;
  }

  /**
   * Identify resume weaknesses
   */
  private identifyWeaknesses(content: ParsedResumeContent, analysis: ResumeAnalysis): string[] {
    const weaknesses: string[] = [];

    if (content.skills.length < 5) {
      weaknesses.push('Limited skill set');
    }

    if (content.experience.length === 0) {
      weaknesses.push('No work experience listed');
    }

    if (!content.summary) {
      weaknesses.push('Missing professional summary');
    }

    if (analysis.readabilityScore < 60) {
      weaknesses.push('Poor readability and structure');
    }

    if (!content.personalInfo.email) {
      weaknesses.push('Missing contact information');
    }

    return weaknesses;
  }

  /**
   * Identify missing keywords
   */
  private identifyMissingKeywords(content: ParsedResumeContent, jobDescription: JobDescription): string[] {
    const resumeText = this.getAllText(content).toLowerCase();
    const jobKeywords = [
      ...jobDescription.skills,
      ...jobDescription.requirements
    ].map(keyword => keyword.toLowerCase());

    return jobKeywords.filter(keyword => 
      !resumeText.includes(keyword.toLowerCase())
    );
  }

  /**
   * Get all text content from resume
   */
  private getAllText(content: ParsedResumeContent): string {
    const parts: string[] = [];

    if (content.summary) parts.push(content.summary);
    
    content.experience.forEach(exp => {
      parts.push(exp.position, exp.company, exp.description);
      parts.push(...exp.achievements);
    });

    content.education.forEach(edu => {
      parts.push(edu.degree, edu.field, edu.institution);
    });

    parts.push(...content.skills);

    return parts.join(' ');
  }
}
