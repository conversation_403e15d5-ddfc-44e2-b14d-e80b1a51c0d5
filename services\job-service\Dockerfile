# Multi-stage build for production optimization
FROM node:22-alpine AS builder

# Set working directory
WORKDIR /app

# Copy package files and TypeScript config (build context is service directory)
COPY package*.json ./
COPY tsconfig.json ./

# Install dependencies for building
RUN npm install

# Copy source code
COPY . ./

# Build the application
RUN npm run build

# Production stage
FROM node:22-alpine AS production

# Install security updates and curl for health checks
RUN apk update && apk upgrade && apk add --no-cache dumb-init curl

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodeuser -u 1001

# Set working directory
WORKDIR /app

# Copy package.json and package-lock.json for production dependencies
COPY --from=builder --chown=nodeuser:nodejs /app/package*.json ./
RUN npm install --only=production && npm cache clean --force

# Copy built application
COPY --from=builder --chown=nodeuser:nodejs /app/dist ./dist

# Create logs directory
RUN mkdir -p logs && chown nodeuser:nodejs logs

# Switch to non-root user
USER nodeuser

# Expose port 8080 for DigitalOcean App Platform
EXPOSE 8080

# Health check for DigitalOcean compatibility
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD curl -f http://localhost:8080/health || exit 1

# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Start the application
CMD ["node", "dist/index.js"]
