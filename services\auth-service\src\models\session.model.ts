import { Schema, model, Document, Types } from 'mongoose';

export interface ISession extends Document {
  _id: Types.ObjectId;
  userId: Types.ObjectId;
  sessionId: string;
  deviceInfo: {
    userAgent: string;
    ip: string;
    deviceType: string;
    browser: string;
    os: string;
  };
  isActive: boolean;
  expiresAt: Date;
  createdAt: Date;
  updatedAt: Date;
}

const sessionSchema = new Schema<ISession>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      required: true,
    },
    sessionId: {
      type: String,
      required: true,
    },
    deviceInfo: {
      userAgent: { type: String, default: '' },
      ip: { type: String, required: true },
      deviceType: { type: String, default: 'unknown' },
      browser: { type: String, default: 'unknown' },
      os: { type: String, default: 'unknown' },
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    expiresAt: {
      type: Date,
      required: true,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  }
);

// Indexes
sessionSchema.index({ userId: 1, isActive: 1 });
sessionSchema.index({ sessionId: 1, isActive: 1 });
sessionSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 }); // TTL index for automatic cleanup

export const SessionModel = model<ISession>('Session', sessionSchema);
