(node:5019) [MONGOOSE] Warning: Duplicate schema index on {"email":1} found. This is often due to declaring an index using both "index: true" and "schema.index()". Please remove the duplicate index definition.
(Use `node --trace-warnings ...` to show where the warning was created)
(node:5019) [MONGOOSE] Warning: Duplicate schema index on {"googleId":1} found. This is often due to declaring an index using both "index: true" and "schema.index()". Please remove the duplicate index definition.
(node:5019) [MONGOOSE] Warning: Duplicate schema index on {"role":1} found. This is often due to declaring an index using both "index: true" and "schema.index()". Please remove the duplicate index definition.
(node:5019) [MONGOOSE] Warning: Duplicate schema index on {"subscriptionTier":1} found. This is often due to declaring an index using both "index: true" and "schema.index()". Please remove the duplicate index definition.
(node:5019) [MONGOOSE] Warning: Duplicate schema index on {"isActive":1} found. This is often due to declaring an index using both "index: true" and "schema.index()". Please remove the duplicate index definition.
(node:5019) [MONGOOSE] Warning: Duplicate schema index on {"expiresAt":1} found. This is often due to declaring an index using both "index: true" and "schema.index()". Please remove the duplicate index definition.
(node:5019) [MONGOOSE] Warning: Duplicate schema index on {"eventType":1} found. This is often due to declaring an index using both "index: true" and "schema.index()". Please remove the duplicate index definition.
info: MongoDB connected successfully {"service":"auth-service","timestamp":"2025-09-21T10:47:51.970Z"}
info: Redis client connected {"service":"auth-service","timestamp":"2025-09-21T10:47:51.981Z"}
info: Redis client ready {"service":"auth-service","timestamp":"2025-09-21T10:47:51.983Z"}
info: Redis connection established {"service":"auth-service","timestamp":"2025-09-21T10:47:51.983Z"}
info: 🔐 Auth Service running on port 3001 {"service":"auth-service","timestamp":"2025-09-21T10:47:51.985Z"}
info: 📝 Environment: development {"service":"auth-service","timestamp":"2025-09-21T10:47:51.985Z"}
info: 🔗 Health Check: http://localhost:3001/health {"service":"auth-service","timestamp":"2025-09-21T10:47:51.985Z"}
info: 🔄 Graceful shutdown initiated... {"service":"auth-service","timestamp":"2025-09-21T10:51:15.988Z"}
info: ✅ HTTP server closed {"service":"auth-service","timestamp":"2025-09-21T10:51:15.989Z"}
warn: MongoDB disconnected {"service":"auth-service","timestamp":"2025-09-21T10:51:16.006Z"}
info: MongoDB disconnected successfully {"service":"auth-service","timestamp":"2025-09-21T10:51:16.007Z"}
info: Redis disconnected {"service":"auth-service","timestamp":"2025-09-21T10:51:16.008Z"}
info: ✅ Graceful shutdown completed {"service":"auth-service","timestamp":"2025-09-21T10:51:16.008Z"}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 http: ::1 - - [21/Sep/2025:10:48:17 +0000] "GET /health HTTP/1.1" 200 406 "-" "node" {"service":"auth-service","timestamp":"2025-09-21T10:48:17.737Z"}
http: ::1 - - [21/Sep/2025:10:48:47 +0000] "GET /health HTTP/1.1" 200 406 "-" "node" {"service":"auth-service","timestamp":"2025-09-21T10:48:47.721Z"}
http: ::1 - - [21/Sep/2025:10:49:08 +0000] "POST /api/v1/register HTTP/1.1" 400 194 "-" "curl/8.7.1" {"service":"auth-service","timestamp":"2025-09-21T10:49:08.852Z"}
http: ::1 - - [21/Sep/2025:10:49:17 +0000] "GET /health HTTP/1.1" 200 406 "-" "node" {"service":"auth-service","timestamp":"2025-09-21T10:49:17.722Z"}
http: ::1 - - [21/Sep/2025:10:49:47 +0000] "GET /health HTTP/1.1" 200 406 "-" "node" {"service":"auth-service","timestamp":"2025-09-21T10:49:47.721Z"}
http: ::1 - - [21/Sep/2025:10:50:17 +0000] "GET /health HTTP/1.1" 200 406 "-" "node" {"service":"auth-service","timestamp":"2025-09-21T10:50:17.724Z"}
http: ::1 - - [21/Sep/2025:10:50:47 +0000] "GET /health HTTP/1.1" 200 405 "-" "node" {"service":"auth-service","timestamp":"2025-09-21T10:50:47.726Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:51:30.333Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:51:30.333Z"}
info: Redis client connected {"service":"auth-service","timestamp":"2025-09-21T10:51:30.386Z"}
error: Redis client error: write EPIPE {"code":"EPIPE","errno":-32,"service":"auth-service","stack":"Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:966:11)\n    at Socket._write (node:net:978:8)\n    at writeOrBuffer (node:internal/streams/writable:572:12)\n    at _write (node:internal/streams/writable:501:10)\n    at Writable.write (node:internal/streams/writable:510:10)\n    at EventEmitter.sendCommand (/Users/<USER>/Developer/JobApplicationAutomator/Backend/node_modules/ioredis/built/Redis.js:395:29)\n    at EventEmitter.info (/Users/<USER>/Developer/JobApplicationAutomator/Backend/node_modules/ioredis/built/utils/Commander.js:90:25)\n    at EventEmitter._readyCheck (/Users/<USER>/Developer/JobApplicationAutomator/Backend/node_modules/ioredis/built/Redis.js:653:14)","syscall":"write","timestamp":"2025-09-21T10:51:30.387Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:51:30.387Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:51:30.387Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:51:30.488Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:51:30.488Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:51:30.488Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:51:30.640Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:51:30.641Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:51:30.641Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:51:30.844Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:51:30.845Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:51:30.846Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:51:31.098Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:51:31.098Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:51:31.099Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:51:31.401Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:51:31.402Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:51:31.402Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:51:31.755Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:51:31.756Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:51:31.756Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:51:32.158Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:51:32.159Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:51:32.159Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:51:32.610Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:51:32.611Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:51:32.611Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:51:33.112Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:51:33.112Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:51:33.112Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:51:33.667Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:51:33.668Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:51:33.669Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:51:34.271Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:51:34.272Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:51:34.272Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:51:34.926Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:51:34.927Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:51:34.927Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:51:35.629Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:51:35.629Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:51:35.629Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:51:36.382Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:51:36.383Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:51:36.383Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:51:37.185Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:51:37.186Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:51:37.186Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:51:38.041Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:51:38.042Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:51:38.042Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:51:38.945Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:51:38.945Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:51:38.945Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:51:39.897Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:51:39.898Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:51:39.898Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:51:40.901Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:51:40.902Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:51:40.903Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:51:41.953Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:51:41.953Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:51:41.953Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:51:43.058Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:51:43.059Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:51:43.059Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:51:44.213Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:51:44.214Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:51:44.214Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:51:45.416Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:51:45.417Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:51:45.417Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:51:46.669Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:51:46.669Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:51:46.670Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:51:47.972Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:51:47.973Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:51:47.973Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:51:49.326Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:51:49.327Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:51:49.327Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:51:50.728Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:51:50.728Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:51:50.728Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:51:52.180Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:51:52.180Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:51:52.180Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:51:53.682Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:51:53.682Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:51:53.682Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:51:55.234Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:51:55.235Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:51:55.235Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:51:56.836Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:51:56.837Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:51:56.837Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:51:58.488Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:51:58.488Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:51:58.488Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:52:00.192Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:52:00.192Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:52:00.192Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:52:01.944Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:52:01.944Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:52:01.945Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:52:03.746Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:52:03.746Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:52:03.746Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:52:05.598Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:52:05.599Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:52:05.599Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:52:07.500Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:52:07.501Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:52:07.501Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:52:09.454Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:52:09.455Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:52:09.455Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:52:11.457Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:52:11.458Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:52:11.458Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:52:13.461Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:52:13.462Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:52:13.462Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:52:15.464Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:52:15.464Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:52:15.464Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:52:17.467Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:52:17.467Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:52:17.467Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:52:19.469Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:52:19.470Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:52:19.470Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:52:21.472Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:52:21.473Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:52:21.473Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:52:23.475Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:52:23.476Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:52:23.476Z"}
http: ::1 - - [21/Sep/2025:10:52:23 +0000] "GET /health HTTP/1.1" 200 405 "-" "node" {"service":"auth-service","timestamp":"2025-09-21T10:52:23.478Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:52:25.478Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:52:25.479Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:52:25.479Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:52:27.482Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:52:27.482Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:52:27.483Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:52:29.485Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:52:29.486Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:52:29.486Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:52:31.488Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:52:31.489Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:52:31.490Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:52:33.491Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:52:33.491Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:52:33.491Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:52:35.495Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:52:35.496Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:52:35.496Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:52:37.498Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:52:37.498Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:52:37.498Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:52:39.500Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:52:39.501Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:52:39.501Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:52:41.503Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:52:41.503Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:52:41.503Z"}
error: Redis client error: connect ECONNREFUSED 1********:6379 {"address":"1********","code":"ECONNREFUSED","errno":-61,"port":6379,"service":"auth-service","stack":"Error: connect ECONNREFUSED 1********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1637:16)","syscall":"connect","timestamp":"2025-09-21T10:52:43.506Z"}
warn: Redis client connection closed {"service":"auth-service","timestamp":"2025-09-21T10:52:43.507Z"}
info: Redis client reconnecting {"service":"auth-service","timestamp":"2025-09-21T10:52:43.507Z"}
