import { Schema, model } from 'mongoose';

const resumeSchema = new Schema(
  {
    userId: {
      type: String,
      required: true,
    },
    title: {
      type: String,
      required: true,
    },
    fileName: {
      type: String,
      required: true,
    },
    filePath: {
      type: String,
      required: true,
    },
    fileSize: {
      type: Number,
      required: true,
    },
    fileType: {
      type: String,
      required: true,
    },
    content: {
      personalInfo: {
        firstName: String,
        lastName: String,
        email: String,
        phone: String,
        address: String,
        linkedin: String,
        github: String,
        website: String,
      },
      summary: String,
      experience: [
        {
          company: String,
          position: String,
          startDate: Date,
          endDate: Date,
          current: Boolean,
          description: String,
          achievements: [String],
        },
      ],
      education: [
        {
          institution: String,
          degree: String,
          field: String,
          startDate: Date,
          endDate: Date,
          gpa: Number,
          description: String,
        },
      ],
      skills: [String],
      certifications: [
        {
          name: String,
          issuer: String,
          date: Date,
          expiryDate: Date,
        },
      ],
    },
    analysis: {
      overallScore: Number,
      atsScore: Number,
      keywordMatch: Number,
      skillsMatch: Number,
      experienceMatch: Number,
      educationMatch: Number,
      recommendedImprovements: [String],
      lastAnalyzed: Date,
    },
    isPublic: {
      type: Boolean,
      default: false,
    },
    isDefault: {
      type: Boolean,
      default: false,
    },
    tags: [String],
    downloadCount: {
      type: Number,
      default: 0,
    },
    viewCount: {
      type: Number,
      default: 0,
    },
    lastUsedAt: Date,
  },
  {
    timestamps: true,
    versionKey: false,
    toJSON: {
      transform(doc: unknown, ret: Record<string, unknown>): Record<string, unknown> {
        ret.id = (ret._id as { toString: () => string }).toString();
        delete ret._id;
        return ret;
      },
    },
  }
);

// Indexes
resumeSchema.index({ userId: 1 });
resumeSchema.index({ isDefault: 1 });
resumeSchema.index({ createdAt: -1 });

export const Resume = model('Resume', resumeSchema);
