import pdfParse from 'pdf-parse';
import { logger } from '../utils/logger';

export interface ParsedResumeContent {
  personalInfo: {
    firstName?: string;
    lastName?: string;
    email?: string;
    phone?: string;
    location?: string;
    website?: string;
    linkedin?: string;
    github?: string;
  };
  summary: string;
  experience: Array<{
    company: string;
    position: string;
    startDate: string;
    endDate?: string;
    current: boolean;
    description: string;
    achievements: string[];
    skills: string[];
  }>;
  education: Array<{
    institution: string;
    degree: string;
    field: string;
    startDate: string;
    endDate?: string;
    gpa?: number;
    description?: string;
  }>;
  skills: string[];
  certifications: Array<{
    name: string;
    issuer: string;
    date: string;
    expiryDate?: string;
  }>;
  languages: Array<{
    language: string;
    proficiency: 'basic' | 'conversational' | 'fluent' | 'native';
  }>;
  projects: Array<{
    name: string;
    description: string;
    technologies: string[];
    startDate: string;
    endDate?: string;
    url?: string;
  }>;
}

export class ResumeParserService {
  private getCurrentDateString(): string {
    return new Date().toISOString().split('T')[0] || '2024-01-01';
  }
  private readonly skillKeywords = [
    // Programming Languages
    'javascript', 'typescript', 'python', 'java', 'c++', 'c#', 'php', 'ruby', 'go', 'rust', 'swift', 'kotlin',
    'html', 'css', 'sql', 'r', 'matlab', 'scala', 'perl', 'bash', 'powershell',
    
    // Web Technologies
    'react', 'angular', 'vue', 'node.js', 'express', 'django', 'flask', 'spring', 'laravel', 'rails',
    'next.js', 'nuxt.js', 'svelte', 'jquery', 'bootstrap', 'tailwind', 'sass', 'less',
    
    // Databases
    'mysql', 'postgresql', 'mongodb', 'redis', 'elasticsearch', 'cassandra', 'dynamodb', 'sqlite',
    'oracle', 'sql server', 'mariadb', 'neo4j', 'couchdb',
    
    // Cloud & DevOps
    'aws', 'azure', 'gcp', 'docker', 'kubernetes', 'jenkins', 'gitlab', 'github', 'terraform',
    'ansible', 'chef', 'puppet', 'vagrant', 'nginx', 'apache', 'linux', 'ubuntu', 'centos',
    
    // Mobile
    'ios', 'android', 'react native', 'flutter', 'xamarin', 'ionic', 'cordova',
    
    // Data Science & AI
    'machine learning', 'deep learning', 'tensorflow', 'pytorch', 'scikit-learn', 'pandas', 'numpy',
    'jupyter', 'tableau', 'power bi', 'apache spark', 'hadoop', 'kafka',
    
    // Other Technologies
    'git', 'svn', 'mercurial', 'jira', 'confluence', 'slack', 'teams', 'zoom', 'figma', 'sketch',
    'photoshop', 'illustrator', 'premiere', 'after effects', 'blender', 'maya'
  ];

  private readonly experienceKeywords = [
    'experience', 'work', 'employment', 'career', 'professional', 'background',
    'employment history', 'work history', 'professional experience'
  ];

  private readonly educationKeywords = [
    'education', 'academic', 'university', 'college', 'degree', 'diploma',
    'certification', 'qualification', 'training', 'course'
  ];

  /**
   * Parse PDF resume content
   */
  public async parsePDF(buffer: Buffer): Promise<ParsedResumeContent> {
    try {
      logger.info('Starting PDF resume parsing...');
      
      const pdfData = await pdfParse(buffer);
      const text = pdfData.text;
      
      logger.info(`PDF parsed successfully. Text length: ${text.length} characters`);
      
      return this.extractContentFromText(text);
    } catch (error) {
      logger.error('PDF parsing failed:', error);
      throw new Error(`PDF parsing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Parse DOC/DOCX resume content (placeholder for future implementation)
   */
  public async parseDocument(buffer: Buffer, mimeType: string): Promise<ParsedResumeContent> {
    try {
      logger.info(`Starting document parsing for type: ${mimeType}`);
      
      // For now, return basic structure
      // In production, you would use libraries like mammoth for DOCX parsing
      return this.getEmptyResumeContent();
    } catch (error) {
      logger.error('Document parsing failed:', error);
      throw new Error(`Document parsing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Extract content from text using pattern matching
   */
  private extractContentFromText(text: string): ParsedResumeContent {
    const lines = text.split('\n').map(line => line.trim()).filter(line => line.length > 0);
    
    return {
      personalInfo: this.extractPersonalInfo(lines),
      summary: this.extractSummary(lines) || '',
      experience: this.extractExperience(lines),
      education: this.extractEducation(lines),
      skills: this.extractSkills(lines),
      certifications: this.extractCertifications(lines),
      languages: this.extractLanguages(lines),
      projects: this.extractProjects(lines)
    };
  }

  /**
   * Extract personal information
   */
  private extractPersonalInfo(lines: string[]): ParsedResumeContent['personalInfo'] {
    const personalInfo: ParsedResumeContent['personalInfo'] = {};
    
    for (const line of lines) {
      // Email extraction
      const emailMatch = line.match(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/);
      if (emailMatch) {
        personalInfo.email = emailMatch[0];
      }
      
      // Phone extraction
      const phoneMatch = line.match(/(\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})/);
      if (phoneMatch) {
        personalInfo.phone = line.trim();
      }
      
      // LinkedIn extraction
      if (line.toLowerCase().includes('linkedin.com') || line.toLowerCase().includes('linkedin')) {
        personalInfo.linkedin = line.trim();
      }
      
      // GitHub extraction
      if (line.toLowerCase().includes('github.com') || line.toLowerCase().includes('github')) {
        personalInfo.github = line.trim();
      }
      
      // Website extraction
      if (line.match(/^https?:\/\/\w+/) && !line.includes('linkedin') && !line.includes('github')) {
        personalInfo.website = line.trim();
      }
    }
    
    // Extract name (usually first line or first two lines)
    if (lines.length > 0) {
      const firstLine = lines[0];
      if (firstLine) {
        const nameParts = firstLine.split(' ');
        if (nameParts.length >= 2) {
          personalInfo.firstName = nameParts[0] || '';
          personalInfo.lastName = nameParts.slice(1).join(' ') || '';
        }
      }
    }
    
    return personalInfo;
  }

  /**
   * Extract summary/objective
   */
  private extractSummary(lines: string[]): string | undefined {
    const summaryKeywords = ['summary', 'objective', 'profile', 'about', 'overview'];
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i]?.toLowerCase();
      if (line && summaryKeywords.some(keyword => line.includes(keyword))) {
        // Get the next few lines as summary
        const summaryLines = [];
        for (let j = i + 1; j < Math.min(i + 5, lines.length); j++) {
          const nextLine = lines[j];
          if (nextLine && nextLine.length > 10) {
            summaryLines.push(nextLine);
          }
        }
        return summaryLines.join(' ').trim();
      }
    }
    
    return undefined;
  }

  /**
   * Extract work experience
   */
  private extractExperience(lines: string[]): ParsedResumeContent['experience'] {
    const experience: ParsedResumeContent['experience'] = [];
    let inExperienceSection = false;
    let currentExperience: any = null;
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      if (!line) continue;
      
      const lowerLine = line.toLowerCase();
      
      // Check if we're entering experience section
      if (this.experienceKeywords.some(keyword => lowerLine.includes(keyword))) {
        inExperienceSection = true;
        continue;
      }
      
      // Check if we're leaving experience section
      if (inExperienceSection && this.educationKeywords.some(keyword => lowerLine.includes(keyword))) {
        break;
      }
      
      if (inExperienceSection) {
        // Look for company/position patterns
        if (this.isCompanyPositionLine(line)) {
          if (currentExperience) {
            experience.push(currentExperience);
          }
          currentExperience = this.parseExperienceLine(line);
        } else if (currentExperience && line.length > 10) {
          // Add description or achievement
          if (line.startsWith('•') || line.startsWith('-') || line.startsWith('*')) {
            currentExperience.achievements.push(line.substring(1).trim());
          } else {
            currentExperience.description += ' ' + line;
          }
        }
      }
    }
    
    if (currentExperience) {
      experience.push(currentExperience);
    }
    
    return experience;
  }

  /**
   * Extract education
   */
  private extractEducation(lines: string[]): ParsedResumeContent['education'] {
    const education: ParsedResumeContent['education'] = [];
    let inEducationSection = false;
    let currentEducation: any = null;
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      if (!line) continue;
      
      const lowerLine = line.toLowerCase();
      
      // Check if we're entering education section
      if (this.educationKeywords.some(keyword => lowerLine.includes(keyword))) {
        inEducationSection = true;
        continue;
      }
      
      if (inEducationSection) {
        // Look for education patterns
        if (this.isEducationLine(line)) {
          if (currentEducation) {
            education.push(currentEducation);
          }
          currentEducation = this.parseEducationLine(line);
        } else if (currentEducation && line.length > 5) {
          currentEducation.description = (currentEducation.description || '') + ' ' + line;
        }
      }
    }
    
    if (currentEducation) {
      education.push(currentEducation);
    }
    
    return education;
  }

  /**
   * Extract skills
   */
  private extractSkills(lines: string[]): string[] {
    const skills: string[] = [];
    const text = lines.join(' ').toLowerCase();
    
    for (const skill of this.skillKeywords) {
      if (text.includes(skill.toLowerCase())) {
        skills.push(skill);
      }
    }
    
    return [...new Set(skills)]; // Remove duplicates
  }

  /**
   * Extract certifications
   */
  private extractCertifications(lines: string[]): ParsedResumeContent['certifications'] {
    const certifications: ParsedResumeContent['certifications'] = [];
    const certKeywords = ['certified', 'certification', 'certificate', 'license', 'credential'];
    
    for (const line of lines) {
      const lowerLine = line.toLowerCase();
      if (certKeywords.some(keyword => lowerLine.includes(keyword))) {
        certifications.push({
          name: line.trim() || 'Unknown Certification',
          issuer: 'Unknown',
          date: this.getCurrentDateString()
        });
      }
    }
    
    return certifications;
  }

  /**
   * Extract languages
   */
  private extractLanguages(lines: string[]): ParsedResumeContent['languages'] {
    const languages: ParsedResumeContent['languages'] = [];
    const languageKeywords = ['language', 'languages', 'fluent', 'native', 'conversational'];
    
    for (const line of lines) {
      const lowerLine = line.toLowerCase();
      if (languageKeywords.some(keyword => lowerLine.includes(keyword))) {
        // Simple language extraction
        const words = line.split(' ');
        for (const word of words) {
          if (word.length > 2 && /^[a-zA-Z]+$/.test(word)) {
            languages.push({
              language: word,
              proficiency: 'conversational'
            });
          }
        }
      }
    }
    
    return languages;
  }

  /**
   * Extract projects
   */
  private extractProjects(lines: string[]): ParsedResumeContent['projects'] {
    const projects: ParsedResumeContent['projects'] = [];
    const projectKeywords = ['project', 'projects', 'portfolio', 'work'];
    
    for (const line of lines) {
      const lowerLine = line.toLowerCase();
      if (projectKeywords.some(keyword => lowerLine.includes(keyword))) {
        projects.push({
          name: line.trim() || 'Unknown Project',
          description: '',
          technologies: [],
          startDate: this.getCurrentDateString()
        });
      }
    }
    
    return projects;
  }

  /**
   * Helper methods
   */
  private isCompanyPositionLine(line: string): boolean {
    // Look for patterns like "Company Name - Position" or "Position at Company"
    return line.includes(' - ') || line.includes(' at ') || line.includes('@');
  }

  private isEducationLine(line: string): boolean {
    const educationIndicators = ['university', 'college', 'institute', 'school', 'degree', 'bachelor', 'master', 'phd', 'doctorate'];
    const lowerLine = line.toLowerCase();
    return educationIndicators.some(indicator => lowerLine.includes(indicator));
  }

  private parseExperienceLine(line: string): any {
    const parts = line.split(' - ');
    return {
      company: (parts[0]?.trim() || 'Unknown Company'),
      position: (parts[1]?.trim() || 'Unknown Position'),
      startDate: this.getCurrentDateString(),
      endDate: undefined,
      current: true,
      description: '',
      achievements: [],
      skills: []
    };
  }

  private parseEducationLine(line: string): any {
    return {
      institution: line.trim(),
      degree: 'Unknown Degree',
      field: 'Unknown Field',
      startDate: this.getCurrentDateString(),
      endDate: undefined,
      gpa: undefined,
      description: ''
    };
  }

  private getEmptyResumeContent(): ParsedResumeContent {
    return {
      personalInfo: {},
      summary: '',
      experience: [],
      education: [],
      skills: [],
      certifications: [],
      languages: [],
      projects: []
    };
  }
}
