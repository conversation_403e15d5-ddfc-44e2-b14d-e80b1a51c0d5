# Resume Service Environment Variables

# Service Configuration
NODE_ENV=production
PORT=3005

# Database Configuration
MONGODB_URI=mongodb+srv://ResumeAdmin:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0ResumePlatform

# JWT Configuration (must match other services)
JWT_SECRET=your-super-secret-jwt-key-change-in-production-must-be-32-chars-minimum
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-in-production-must-be-32-chars

# AWS S3 Configuration
AWS_ACCESS_KEY_ID=your-aws-access-key-id
AWS_SECRET_ACCESS_KEY=your-aws-secret-access-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=job-platform-resumes

# CORS Configuration
CORS_ORIGIN=https://jobs-app-ydwim.ondigitalocean.app

# Logging
LOG_LEVEL=info

# File Upload Limits
MAX_FILE_SIZE=10485760  # 10MB in bytes
ALLOWED_FILE_TYPES=application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,text/plain

# Resume Processing
ENABLE_RESUME_PARSING=true
ENABLE_RESUME_ANALYSIS=true
ENABLE_S3_STORAGE=true

# Analysis Configuration
ATS_KEYWORDS=teamwork,leadership,communication,problem solving,analytical,detail oriented,self motivated,results driven,innovative,collaborative,strategic thinking,time management,project management

# Performance
RESUME_PROCESSING_TIMEOUT=30000  # 30 seconds
ANALYSIS_TIMEOUT=60000  # 60 seconds
