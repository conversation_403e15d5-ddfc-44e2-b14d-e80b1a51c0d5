import { Router } from 'express';
import { database } from '../database/connection';
import { ResponseUtil } from '../utils/response';
import { logger } from '../utils/logger';

const router = Router();

router.get('/', (req, res) => {
  try {
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV,
      version: process.env.API_VERSION ?? '1.0.0',
      database: {
        connected: database.isHealthy(),
        state: database.getConnectionState(),
      },
      memory: process.memoryUsage(),
      cloudinary: {
        configured: !!(process.env.CLOUDINARY_CLOUD_NAME && 
                      process.env.CLOUDINARY_API_KEY && 
                      process.env.CLOUDINARY_API_SECRET),
      },
    };

    // Service is healthy if it's running, even if database is not connected
    // Database connection issues should not make the service completely unhealthy
    const isHealthy = true; // Always return healthy for basic service availability

    return res.json(ResponseUtil.success(health, 'User Service is healthy'));
  } catch (error) {
    logger.error('Health check error:', error);
    return res.status(500).json({
      success: false,
      message: 'Health check failed',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

export { router as healthRoutes };
