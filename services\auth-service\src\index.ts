import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import morgan from 'morgan';
import passport from 'passport';
// Using DigitalOcean environment variables directly
import { database } from './database/connection';
import { logger } from './utils/logger';
import { errorHandler } from './middleware/error.middleware';
import { rateLimitMiddleware } from './middleware/rate-limit.middleware';
import { setupPassport } from './config/passport.config';
import { authRoutes } from './routes/auth.routes';
import { healthRoutes } from './routes/health.routes';
// import { RedisClient } from './services/redis.service';

class AuthService {
  private app: express.Application;
  private server: ReturnType<express.Application['listen']> | null = null;
  // private redisClient: RedisClient;

  constructor() {
    this.app = express();
    // Enable trust proxy for proper IP detection behind reverse proxy
    // Configure trust proxy to only trust specific proxies to avoid rate limiting issues
    this.app.set('trust proxy', 1);
    // this.redisClient = new RedisClient();
    this.setupMiddleware();
    this.setupPassport();
    this.setupRoutes();
    this.setupErrorHandling();
  }

  private setupMiddleware(): void {
    // Security middleware
    this.app.use(
      helmet({
        contentSecurityPolicy: {
          directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", 'data:', 'https:'],
          },
        },
        crossOriginEmbedderPolicy: false,
      })
    );

    // CORS configuration
    this.app.use(
      cors({
        origin: process.env.NODE_ENV === 'development'
          ? true
          : (process.env.CORS_ORIGIN?.split(',') ?? '*'),
        credentials: true,
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
        allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
      })
    );

    // Compression
    this.app.use(compression());

    // Body parsing
    this.app.use(express.json({ limit: '1mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '1mb' }));

    // Logging
    this.app.use(
      morgan(process.env.NODE_ENV === 'development' ? 'dev' : 'combined', {
        stream: {
          write: (message: string) => logger.http(message.trim()),
        },
      })
    );

    // Rate limiting
    this.app.use(rateLimitMiddleware);

    // Passport initialization
    this.app.use(passport.initialize());

    // Request ID middleware
    this.app.use((req, res, next) => {
      req.headers['x-request-id'] =
        req.headers['x-request-id'] ??
        Math.random().toString(36).substring(2, 15);
      res.setHeader('X-Request-ID', req.headers['x-request-id']);
      next();
    });
  }

  private setupPassport(): void {
    setupPassport();
  }

  private setupRoutes(): void {
    // Health check routes
    this.app.use('/health', healthRoutes);

    // Authentication routes - support both /auth and /api/v1 for API Gateway compatibility
    this.app.use('/auth', authRoutes);
    this.app.use('/api/v1', authRoutes);

    // Service info
    this.app.get('/api/v1', (req, res) => {
      res.json({
        name: 'Job Platform Authentication Service',
        version: process.env.API_VERSION || '1.0.0',
        environment: process.env.NODE_ENV || 'development',
        timestamp: new Date().toISOString(),
        features: [
          'JWT Authentication',
          'Google OAuth2',
          'Session Management',
          'Two-Factor Authentication',
          'Password Reset',
          'Account Security',
        ],
      });
    });
  }

  private setupErrorHandling(): void {
    // 404 handler
    this.app.use((req, res) => {
      res.status(404).json({
        success: false,
        message: `Route ${req.originalUrl} not found`,
        timestamp: new Date().toISOString(),
      });
    });

    // Global error handler
    this.app.use(errorHandler);
  }

  public async start(): Promise<void> {
    try {
      // Start server first to respond to health checks
      const port = parseInt(process.env.PORT || '3001');
      this.server = this.app.listen(port, '0.0.0.0', () => {
        logger.info(`🔐 Auth Service running on port ${port}`);
        logger.info(`📝 Environment: ${process.env.NODE_ENV || 'development'}`);
        logger.info(`🔗 Health Check: http://localhost:${port}/health`);
      });

      // Connect to database after server is running
      try {
        await database.connect();
        logger.info('Database connected successfully');
      } catch (dbError) {
        logger.warn('Database connection failed - service will run with limited functionality:', dbError);
        // Continue running without database - some features will be disabled
      }

      // Connect to Redis (optional - service can run without Redis but with reduced functionality)
      // try {
      //   await this.redisClient.connect();
      //   logger.info('Redis connected successfully');
      // } catch (redisError) {
      //   logger.warn('Redis connection failed - service will run with reduced functionality:', redisError);
      //   // Continue without Redis - some features like rate limiting and caching will be disabled
      // }
      logger.info('Redis connection disabled - running without Redis');

      // Graceful shutdown
      process.on('SIGTERM', () => void this.shutdown());
      process.on('SIGINT', () => void this.shutdown());
    } catch (error) {
      logger.error('Failed to start Auth Service:', error);
      process.exit(1);
    }
  }

  private async shutdown(): Promise<void> {
    logger.info('🔄 Graceful shutdown initiated...');

    if (this.server) {
      await new Promise<void>(resolve => {
        this.server!.close(() => {
          logger.info('✅ HTTP server closed');

          // Close database connection
          void database.disconnect();

          // Close Redis connection
          // void this.redisClient.disconnect();  

          logger.info('✅ Graceful shutdown completed');
          process.exit(0);
        });
        resolve();
      });
    }
  }
}

// Start the Auth Service
const authService = new AuthService();
authService.start().catch(error => {
  logger.error('Failed to start Auth Service:', error);
  process.exit(1);
});
