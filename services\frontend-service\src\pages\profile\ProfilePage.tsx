import React, { useState, useEffect } from 'react';
import { Camera, MapPin, Mail, Save, Edit3, X } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { userService } from '@/services/user.service';
import { toast } from 'react-hot-toast';
import { validateProfileData, sanitizeProfileData, ValidationError } from '@/utils/validation';

interface ProfileFormData {
  // Basic Info
  bio?: string;
  phoneNumber?: string;
  location?: {
    country?: string;
    state?: string;
    city?: string;
    zipCode?: string;
    remote?: boolean;
  };
  dateOfBirth?: string;
  nationality?: string;
  
  // Professional Info
  currentPosition?: string;
  currentCompany?: string;
  yearsOfExperience?: number;
  expectedSalary?: {
    min?: number;
    max?: number;
    currency?: string;
  };
  
  // Contact Info
  website?: string;
  linkedin?: string;
  github?: string;
  portfolio?: string;
  
  // Education
  education?: Array<{
    institution: string;
    degree: string;
    field: string;
    startDate?: string;
    endDate?: string;
    gpa?: number;
    description?: string;
  }>;
  
  // Experience
  experience?: Array<{
    company: string;
    position: string;
    startDate?: string;
    endDate?: string;
    current?: boolean;
    description?: string;
    skills?: string[];
    achievements?: string[];
  }>;
  
  // Skills
  skills?: Array<{
    name: string;
    level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
    verified?: boolean;
    yearsOfExperience?: number;
  }>;
  
  // Languages
  languages?: Array<{
    language: string;
    proficiency: 'basic' | 'conversational' | 'fluent' | 'native';
  }>;
  
  // Preferences
  preferences?: {
    jobAlerts?: boolean;
    emailNotifications?: boolean;
    smsNotifications?: boolean;
    profileVisibility?: 'public' | 'private' | 'connections';
    resumeVisibility?: 'public' | 'private' | 'connections';
    salaryVisibility?: boolean;
    locationSharing?: boolean;
    dataSharing?: boolean;
    marketingEmails?: boolean;
    timezone?: string;
    language?: string;
    currency?: string;
  };
}

type TabType = 'basic' | 'professional' | 'education' | 'skills' | 'contact' | 'preferences';

export const ProfilePage: React.FC = () => {
  const { user, updateUser } = useAuth();
  const [activeTab, setActiveTab] = useState<TabType>('basic');
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState<ProfileFormData>({});
  const [originalData, setOriginalData] = useState<ProfileFormData>({});
  const [validationErrors, setValidationErrors] = useState<ValidationError[]>([]);

  // Load user profile data
  useEffect(() => {
    if (user) {
      const profileData: ProfileFormData = {
        bio: user.profile?.bio,
        phoneNumber: user.profile?.phoneNumber,
        location: user.profile?.location,
        dateOfBirth: user.profile?.dateOfBirth,
        nationality: user.profile?.nationality,
        currentPosition: user.profile?.currentPosition,
        currentCompany: user.profile?.currentCompany,
        yearsOfExperience: user.profile?.yearsOfExperience,
        expectedSalary: user.profile?.expectedSalary,
        website: user.profile?.website,
        linkedin: user.profile?.linkedin,
        github: user.profile?.github,
        portfolio: user.profile?.portfolio,
        education: user.profile?.education,
        experience: user.profile?.experience,
        skills: user.profile?.skills,
        languages: user.profile?.languages,
        preferences: user.profile?.preferences, 
      };
      setFormData(profileData);
      setOriginalData(profileData);
    }
  }, [user]);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleNestedInputChange = (parentField: string, index: number | undefined, field: string, value: any) => {
    setFormData(prev => {
      const currentParent = prev[parentField as keyof ProfileFormData] as any;
      
      if (index !== undefined && Array.isArray(currentParent)) {
        // Handle array updates (for education, skills, languages, experience)
        const newArray = [...currentParent];
        newArray[index] = {
          ...newArray[index],
          [field]: value
        };
        return {
          ...prev,
          [parentField]: newArray
        };
      } else {
        // Handle object updates (for location, expectedSalary, preferences)
        return {
          ...prev,
          [parentField]: {
            ...(currentParent || {}),
            [field]: value
          }
        };
      }
    });
  };

  const handleSave = async () => {
    try {
      setIsLoading(true);
      setValidationErrors([]);
      
      // Sanitize and validate data
      const sanitizedData = sanitizeProfileData(formData);
      const validation = validateProfileData(sanitizedData, activeTab);
      
      if (!validation.isValid) {
        setValidationErrors(validation.errors);
        toast.error('Please fix the validation errors before saving');
        return;
      }
      
      // Filter out undefined values and prepare data for API
      const dataToSave = Object.fromEntries(
        Object.entries(sanitizedData).filter(([_, value]) => value !== undefined)
      );

      // Wrap profile data in the expected structure
      const profileUpdateData = {
        profile: dataToSave
      };

      const updatedUser = await userService.updateProfile(profileUpdateData);
      updateUser(updatedUser);
      
      setOriginalData(formData);
      setIsEditing(false);
      setValidationErrors([]);
      toast.success('Profile updated successfully!');
    } catch (error: any) {
      console.error('Error updating profile:', error);
      toast.error(error.response?.data?.message || 'Failed to update profile');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setFormData(originalData);
    setIsEditing(false);
    setValidationErrors([]);
  };

  const handleAvatarUpload = async (file: File) => {
    try {
      setIsLoading(true);
      
      // Validate file
      if (!file.type.startsWith('image/')) {
        toast.error('Please select a valid image file');
        return;
      }
      
      if (file.size > 2 * 1024 * 1024) { // 2MB limit
        toast.error('File size must be less than 2MB');
        return;
      }
      
      // Show upload progress
      toast.loading('Uploading image...', { id: 'avatar-upload' });
      
      const updatedUser = await userService.uploadAvatar(file);
      updateUser(updatedUser);
      
      toast.dismiss('avatar-upload');
      toast.success('Profile photo updated successfully!');
    } catch (error: any) {
      console.error('Error updating avatar:', error);
      toast.dismiss('avatar-upload');
      
      // Handle specific error messages
      if (error.response?.status === 413) {
        toast.error('File is too large. Please choose a smaller image (max 2MB).');
      } else if (error.response?.status === 408) {
        toast.error('Upload timed out. Please try with a smaller image.');
      } else if (error.response?.status === 503) {
        toast.error('Image upload service is temporarily unavailable.');
      } else if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
        toast.error('Upload timed out. Please try with a smaller image (max 2MB).');
      } else {
        toast.error(error.response?.data?.message || 'Failed to update profile photo');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const getFieldError = (field: string): string | undefined => {
    return validationErrors.find(error => error.field === field)?.message;
  };

  const tabs = [
    { id: 'basic' as TabType, label: 'Basic Info', icon: '👤' },
    { id: 'professional' as TabType, label: 'Professional', icon: '💼' },
    { id: 'education' as TabType, label: 'Education', icon: '🎓' },
    { id: 'skills' as TabType, label: 'Skills', icon: '🛠️' },
    { id: 'contact' as TabType, label: 'Contact', icon: '📞' },
    { id: 'preferences' as TabType, label: 'Preferences', icon: '⚙️' },
  ];

  const renderBasicInfo = () => (
    <div className="space-y-6">
      <div>
        <label htmlFor="bio" className="form-label">Bio</label>
        <textarea
          id="bio"
          value={formData.bio || ''}
          onChange={(e) => handleInputChange('bio', e.target.value)}
          className={`block w-full px-3 py-2 border rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-1 sm:text-sm ${
            getFieldError('bio') 
              ? 'border-red-500 focus:ring-red-500 focus:border-red-500' 
              : 'border-gray-300 focus:ring-primary-500 focus:border-primary-500'
          }`}
          rows={4}
          placeholder="Tell us about yourself..."
          disabled={!isEditing}
        />
        {getFieldError('bio') && (
          <p className="mt-1 text-sm text-red-600">{getFieldError('bio')}</p>
        )}
      </div>

      <div>
        <label htmlFor="phoneNumber" className="form-label">Phone Number</label>
        <Input
          id="phoneNumber"
          type="tel"
          value={formData.phoneNumber || ''}
          onChange={(e) => handleInputChange('phoneNumber', e.target.value)}
          placeholder="+****************"
          disabled={!isEditing}
          className={getFieldError('phoneNumber') ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : ''}
        />
        {getFieldError('phoneNumber') && (
          <p className="mt-1 text-sm text-red-600">{getFieldError('phoneNumber')}</p>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label htmlFor="country" className="form-label">Country</label>
          <Input
            id="country"
            value={formData.location?.country || ''}
            onChange={(e) => handleNestedInputChange('location', undefined, 'country', e.target.value)}
            placeholder="United States"
            disabled={!isEditing}
          />
        </div>
        <div>
          <label htmlFor="state" className="form-label">State/Province</label>
          <Input
            id="state"
            value={formData.location?.state || ''}
            onChange={(e) => handleNestedInputChange('location', undefined, 'state', e.target.value)}
            placeholder="California"
            disabled={!isEditing}
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label htmlFor="city" className="form-label">City</label>
          <Input
            id="city"
            value={formData.location?.city || ''}
            onChange={(e) => handleNestedInputChange('location', undefined, 'city', e.target.value)}
            placeholder="San Francisco"
            disabled={!isEditing}
          />
        </div>
        <div>
          <label htmlFor="zipCode" className="form-label">ZIP Code</label>
          <Input
            id="zipCode"
            value={formData.location?.zipCode || ''}
            onChange={(e) => handleNestedInputChange('location', undefined, 'zipCode', e.target.value)}
            placeholder="94105"
            disabled={!isEditing}
          />
        </div>
      </div>

      <div className="flex items-center">
        <input
          type="checkbox"
          id="remote"
          checked={formData.location?.remote || false}
          onChange={(e) => handleNestedInputChange('location', undefined, 'remote', e.target.checked)}
          disabled={!isEditing}
          className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
        />
        <label htmlFor="remote" className="ml-2 block text-sm text-gray-900">
          Open to remote work
        </label>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label htmlFor="dateOfBirth" className="form-label">Date of Birth</label>
          <Input
            id="dateOfBirth"
            type="date"
            value={formData.dateOfBirth || ''}
            onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
            disabled={!isEditing}
          />
        </div>
        <div>
          <label htmlFor="nationality" className="form-label">Nationality</label>
          <Input
            id="nationality"
            value={formData.nationality || ''}
            onChange={(e) => handleInputChange('nationality', e.target.value)}
            placeholder="American"
            disabled={!isEditing}
          />
        </div>
      </div>
    </div>
  );

  const renderProfessionalInfo = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label htmlFor="currentPosition" className="form-label">Current Position</label>
          <Input
            id="currentPosition"
            value={formData.currentPosition || ''}
            onChange={(e) => handleInputChange('currentPosition', e.target.value)}
            placeholder="Senior Software Engineer"
            disabled={!isEditing}
          />
        </div>
        <div>
          <label htmlFor="currentCompany" className="form-label">Current Company</label>
          <Input
            id="currentCompany"
            value={formData.currentCompany || ''}
            onChange={(e) => handleInputChange('currentCompany', e.target.value)}
            placeholder="TechCorp Inc."
            disabled={!isEditing}
          />
        </div>
      </div>

      <div>
        <label htmlFor="yearsOfExperience" className="form-label">Years of Experience</label>
        <Input
          id="yearsOfExperience"
          type="number"
          min="0"
          max="50"
          value={formData.yearsOfExperience || ''}
          onChange={(e) => handleInputChange('yearsOfExperience', parseInt(e.target.value) || 0)}
          placeholder="5"
          disabled={!isEditing}
        />
      </div>

      <div>
        <label className="form-label">Expected Salary Range</label>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label htmlFor="salaryMin" className="block text-sm font-medium text-gray-700 mb-1">Minimum</label>
            <Input
              id="salaryMin"
              type="number"
              value={formData.expectedSalary?.min || ''}
              onChange={(e) => handleNestedInputChange('expectedSalary', undefined, 'min', parseInt(e.target.value) || undefined)}
              placeholder="80000"
              disabled={!isEditing}
            />
          </div>
          <div>
            <label htmlFor="salaryMax" className="block text-sm font-medium text-gray-700 mb-1">Maximum</label>
            <Input
              id="salaryMax"
              type="number"
              value={formData.expectedSalary?.max || ''}
              onChange={(e) => handleNestedInputChange('expectedSalary', undefined, 'max', parseInt(e.target.value) || undefined)}
              placeholder="120000"
              disabled={!isEditing}
            />
          </div>
          <div>
            <label htmlFor="salaryCurrency" className="block text-sm font-medium text-gray-700 mb-1">Currency</label>
            <select
              id="salaryCurrency"
              value={formData.expectedSalary?.currency || 'USD'}
              onChange={(e) => handleNestedInputChange('expectedSalary', undefined, 'currency', e.target.value)}
              disabled={!isEditing}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            >
              <option value="USD">USD</option>
              <option value="EUR">EUR</option>
              <option value="GBP">GBP</option>
              <option value="CAD">CAD</option>
              <option value="AUD">AUD</option>
              <option value="JPY">JPY</option>
              <option value="CNY">CNY</option>
              <option value="INR">INR</option>
              <option value="BDT">BDT</option>
              <option value="BRL">BRL</option>
              <option value="CHF">CHF</option>
              <option value="CLP">CLP</option>
              <option value="COP">COP</option>
              <option value="CZK">CZK</option>
              <option value="DKK">DKK</option>
              <option value="HKD">HKD</option>
              <option value="HUF">HUF</option>
              <option value="ILS">ILS</option>
              <option value="MXN">MXN</option>
              <option value="MYR">MYR</option>
              <option value="NZD">NZD</option>
              <option value="NOK">NOK</option>
              <option value="PHP">PHP</option>
              <option value="PLN">PLN</option>
              <option value="SEK">SEK</option>
              <option value="SGD">SGD</option>
              <option value="THB">THB</option>
              <option value="TRY">TRY</option>
              <option value="TWD">TWD</option>
              <option value="ZAR">ZAR</option>
            </select>
          </div>
        </div>
      </div>
    </div>
  );

  const renderContactInfo = () => (
    <div className="space-y-6">
      <div>
        <label htmlFor="website" className="form-label">Website</label>
        <Input
          id="website"
          type="url"
          value={formData.website || ''}
          onChange={(e) => handleInputChange('website', e.target.value)}
          placeholder="https://yourwebsite.com"
          disabled={!isEditing}
        />
      </div>

      <div>
        <label htmlFor="linkedin" className="form-label">LinkedIn</label>
        <Input
          id="linkedin"
          type="url"
          value={formData.linkedin || ''}
          onChange={(e) => handleInputChange('linkedin', e.target.value)}
          placeholder="https://linkedin.com/in/yourprofile"
          disabled={!isEditing}
        />
      </div>

      <div>
        <label htmlFor="github" className="form-label">GitHub</label>
        <Input
          id="github"
          type="url"
          value={formData.github || ''}
          onChange={(e) => handleInputChange('github', e.target.value)}
          placeholder="https://github.com/yourusername"
          disabled={!isEditing}
        />
      </div>

      <div>
        <label htmlFor="portfolio" className="form-label">Portfolio</label>
        <Input
          id="portfolio"
          type="url"
          value={formData.portfolio || ''}
          onChange={(e) => handleInputChange('portfolio', e.target.value)}
          placeholder="https://yourportfolio.com"
          disabled={!isEditing}
        />
      </div>
    </div>
  );

  const renderEducation = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Education</h3>
        {isEditing && (
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={() => {
              const newEducation = [...(formData.education || [])];
              newEducation.push({
                institution: '',
                degree: '',
                field: '',
                startDate: '',
                endDate: '',
                gpa: undefined,
                description: ''
              });
              setFormData({ ...formData, education: newEducation });
            }}
          >
            Add Education
          </Button>
        )}
      </div>
      
      {formData.education?.map((edu, index) => (
        <div key={index} className="border rounded-lg p-4 space-y-4">
          <div className="flex justify-between items-center">
            <h4 className="font-medium">Education Entry {index + 1}</h4>
            {isEditing && (
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => {
                  const newEducation = formData.education?.filter((_, i) => i !== index) || [];
                  setFormData({ ...formData, education: newEducation });
                }}
              >
                Remove
              </Button>
            )}
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="form-label">Institution *</label>
              <Input
                value={edu.institution || ''}
                onChange={(e) => handleNestedInputChange('education', index, 'institution', e.target.value)}
                placeholder="University Name"
                disabled={!isEditing}
              />
            </div>
            
            <div>
              <label className="form-label">Degree *</label>
              <Input
                value={edu.degree || ''}
                onChange={(e) => handleNestedInputChange('education', index, 'degree', e.target.value)}
                placeholder="Bachelor's, Master's, etc."
                disabled={!isEditing}
              />
            </div>
            
            <div>
              <label className="form-label">Field of Study *</label>
              <Input
                value={edu.field || ''}
                onChange={(e) => handleNestedInputChange('education', index, 'field', e.target.value)}
                placeholder="Computer Science, Business, etc."
                disabled={!isEditing}
              />
            </div>
            
            <div>
              <label className="form-label">GPA</label>
              <Input
                type="number"
                step="0.01"
                min="0"
                max="4"
                value={edu.gpa || ''}
                onChange={(e) => handleNestedInputChange('education', index, 'gpa', parseFloat(e.target.value) || undefined)}
                placeholder="3.5"
                disabled={!isEditing}
              />
            </div>
            
            <div>
              <label className="form-label">Start Date</label>
              <Input
                type="month"
                value={edu.startDate || ''}
                onChange={(e) => handleNestedInputChange('education', index, 'startDate', e.target.value)}
                disabled={!isEditing}
              />
            </div>
            
            <div>
              <label className="form-label">End Date</label>
              <Input
                type="month"
                value={edu.endDate || ''}
                onChange={(e) => handleNestedInputChange('education', index, 'endDate', e.target.value)}
                disabled={!isEditing}
              />
            </div>
          </div>
          
          <div>
            <label className="form-label">Description</label>
            <textarea
              value={edu.description || ''}
              onChange={(e) => handleNestedInputChange('education', index, 'description', e.target.value)}
              placeholder="Describe your education, achievements, relevant coursework..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              rows={3}
              disabled={!isEditing}
            />
          </div>
        </div>
      ))}
      
      {(!formData.education || formData.education.length === 0) && (
        <div className="text-center py-8 text-gray-500">
          {isEditing ? 'Click "Add Education" to get started' : 'No education information added yet'}
        </div>
      )}
    </div>
  );

  const renderSkills = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Skills & Languages</h3>
        {isEditing && (
          <div className="flex gap-2">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => {
                const newSkills = [...(formData.skills || [])];
                newSkills.push({
                  name: '',
                  level: 'beginner',
                  verified: false,
                  yearsOfExperience: undefined
                });
                setFormData({ ...formData, skills: newSkills });
              }}
            >
              Add Skill
            </Button>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => {
                const newLanguages = [...(formData.languages || [])];
                newLanguages.push({
                  language: '',
                  proficiency: 'basic'
                });
                setFormData({ ...formData, languages: newLanguages });
              }}
            >
              Add Language
            </Button>
          </div>
        )}
      </div>
      
      {/* Skills Section */}
      <div>
        <h4 className="text-md font-medium mb-4">Technical Skills</h4>
        {formData.skills?.map((skill, index) => (
          <div key={index} className="border rounded-lg p-4 mb-4 space-y-4">
            <div className="flex justify-between items-center">
              <h5 className="font-medium">Skill {index + 1}</h5>
              {isEditing && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const newSkills = formData.skills?.filter((_, i) => i !== index) || [];
                    setFormData({ ...formData, skills: newSkills });
                  }}
                >
                  Remove
                </Button>
              )}
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="form-label">Skill Name *</label>
                <Input
                  value={skill.name || ''}
                  onChange={(e) => handleNestedInputChange('skills', index, 'name', e.target.value)}
                  placeholder="JavaScript, Python, React, etc."
                  disabled={!isEditing}
                />
              </div>
              
              <div>
                <label className="form-label">Proficiency Level *</label>
                <select
                  value={skill.level || 'beginner'}
                  onChange={(e) => handleNestedInputChange('skills', index, 'level', e.target.value as any)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  disabled={!isEditing}
                >
                  <option value="beginner">Beginner</option>
                  <option value="intermediate">Intermediate</option>
                  <option value="advanced">Advanced</option>
                  <option value="expert">Expert</option>
                </select>
              </div>
              
              <div>
                <label className="form-label">Years of Experience</label>
                <Input
                  type="number"
                  min="0"
                  max="50"
                  value={skill.yearsOfExperience || ''}
                  onChange={(e) => handleNestedInputChange('skills', index, 'yearsOfExperience', parseInt(e.target.value) || undefined)}
                  placeholder="3"
                  disabled={!isEditing}
                />
              </div>
              
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id={`skill-verified-${index}`}
                  checked={skill.verified || false}
                  onChange={(e) => handleNestedInputChange('skills', index, 'verified', e.target.checked)}
                  disabled={!isEditing}
                  className="rounded"
                />
                <label htmlFor={`skill-verified-${index}`} className="text-sm">
                  Verified/Certified
                </label>
              </div>
            </div>
          </div>
        ))}
        
        {(!formData.skills || formData.skills.length === 0) && (
          <div className="text-center py-4 text-gray-500">
            {isEditing ? 'Click "Add Skill" to get started' : 'No skills added yet'}
          </div>
        )}
      </div>
      
      {/* Languages Section */}
      <div>
        <h4 className="text-md font-medium mb-4">Languages</h4>
        {formData.languages?.map((language, index) => (
          <div key={index} className="border rounded-lg p-4 mb-4 space-y-4">
            <div className="flex justify-between items-center">
              <h5 className="font-medium">Language {index + 1}</h5>
              {isEditing && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const newLanguages = formData.languages?.filter((_, i) => i !== index) || [];
                    setFormData({ ...formData, languages: newLanguages });
                  }}
                >
                  Remove
                </Button>
              )}
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="form-label">Language *</label>
                <Input
                  value={language.language || ''}
                  onChange={(e) => handleNestedInputChange('languages', index, 'language', e.target.value)}
                  placeholder="English, Spanish, French, etc."
                  disabled={!isEditing}
                />
              </div>
              
              <div>
                <label className="form-label">Proficiency Level *</label>
                <select
                  value={language.proficiency || 'basic'}
                  onChange={(e) => handleNestedInputChange('languages', index, 'proficiency', e.target.value as any)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  disabled={!isEditing}
                >
                  <option value="basic">Basic</option>
                  <option value="conversational">Conversational</option>
                  <option value="fluent">Fluent</option>
                  <option value="native">Native</option>
                </select>
              </div>
            </div>
          </div>
        ))}
        
        {(!formData.languages || formData.languages.length === 0) && (
          <div className="text-center py-4 text-gray-500">
            {isEditing ? 'Click "Add Language" to get started' : 'No languages added yet'}
          </div>
        )}
      </div>
    </div>
  );

  const renderPreferences = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-6">Account Preferences</h3>
      </div>
      
      {/* Notification Preferences */}
      <div className="border rounded-lg p-6">
        <h4 className="text-md font-medium mb-4">Notification Settings</h4>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium">Job Alerts</label>
              <p className="text-sm text-gray-500">Receive notifications about new job opportunities</p>
            </div>
            <input
              type="checkbox"
              checked={formData.preferences?.jobAlerts || false}
              onChange={(e) => handleNestedInputChange('preferences', undefined, 'jobAlerts', e.target.checked)}
              disabled={!isEditing}
              className="rounded"
            />
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium">Email Notifications</label>
              <p className="text-sm text-gray-500">Receive important updates via email</p>
            </div>
            <input
              type="checkbox"
              checked={formData.preferences?.emailNotifications || false}
              onChange={(e) => handleNestedInputChange('preferences', undefined, 'emailNotifications', e.target.checked)}
              disabled={!isEditing}
              className="rounded"
            />
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium">SMS Notifications</label>
              <p className="text-sm text-gray-500">Receive urgent updates via SMS</p>
            </div>
            <input
              type="checkbox"
              checked={formData.preferences?.smsNotifications || false}
              onChange={(e) => handleNestedInputChange('preferences', undefined, 'smsNotifications', e.target.checked)}
              disabled={!isEditing}
              className="rounded"
            />
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium">Marketing Emails</label>
              <p className="text-sm text-gray-500">Receive promotional content and updates</p>
            </div>
            <input
              type="checkbox"
              checked={formData.preferences?.marketingEmails || false}
              onChange={(e) => handleNestedInputChange('preferences', undefined, 'marketingEmails', e.target.checked)}
              disabled={!isEditing}
              className="rounded"
            />
          </div>
        </div>
      </div>
      
      {/* Privacy Settings */}
      <div className="border rounded-lg p-6">
        <h4 className="text-md font-medium mb-4">Privacy Settings</h4>
        <div className="space-y-4">
          <div>
            <label className="form-label">Profile Visibility</label>
            <select
              value={formData.preferences?.profileVisibility || 'public'}
              onChange={(e) => handleNestedInputChange('preferences', undefined, 'profileVisibility', e.target.value as any)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={!isEditing}
            >
              <option value="public">Public - Visible to everyone</option>
              <option value="connections">Connections - Visible to your network</option>
              <option value="private">Private - Only visible to you</option>
            </select>
          </div>
          
          <div>
            <label className="form-label">Resume Visibility</label>
            <select
              value={formData.preferences?.resumeVisibility || 'public'}
              onChange={(e) => handleNestedInputChange('preferences', undefined, 'resumeVisibility', e.target.value as any)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={!isEditing}
            >
              <option value="public">Public - Visible to employers</option>
              <option value="connections">Connections - Visible to your network</option>
              <option value="private">Private - Only visible to you</option>
            </select>
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium">Salary Visibility</label>
              <p className="text-sm text-gray-500">Show salary expectations on your profile</p>
            </div>
            <input
              type="checkbox"
              checked={formData.preferences?.salaryVisibility || false}
              onChange={(e) => handleNestedInputChange('preferences', undefined, 'salaryVisibility', e.target.checked)}
              disabled={!isEditing}
              className="rounded"
            />
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium">Location Sharing</label>
              <p className="text-sm text-gray-500">Share your location with employers</p>
            </div>
            <input
              type="checkbox"
              checked={formData.preferences?.locationSharing || false}
              onChange={(e) => handleNestedInputChange('preferences', undefined, 'locationSharing', e.target.checked)}
              disabled={!isEditing}
              className="rounded"
            />
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium">Data Sharing</label>
              <p className="text-sm text-gray-500">Allow data sharing for job matching</p>
            </div>
            <input
              type="checkbox"
              checked={formData.preferences?.dataSharing || false}
              onChange={(e) => handleNestedInputChange('preferences', undefined, 'dataSharing', e.target.checked)}
              disabled={!isEditing}
              className="rounded"
            />
          </div>
        </div>
      </div>
      
      {/* Regional Settings */}
      <div className="border rounded-lg p-6">
        <h4 className="text-md font-medium mb-4">Regional Settings</h4>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="form-label">Timezone</label>
            <select
              value={formData.preferences?.timezone || 'UTC'}
              onChange={(e) => handleNestedInputChange('preferences', undefined, 'timezone', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={!isEditing}
            >
              <option value="UTC">UTC</option>
              <option value="America/New_York">Eastern Time</option>
              <option value="America/Chicago">Central Time</option>
              <option value="America/Denver">Mountain Time</option>
              <option value="America/Los_Angeles">Pacific Time</option>
              <option value="Europe/London">London</option>
              <option value="Europe/Paris">Paris</option>
              <option value="Asia/Tokyo">Tokyo</option>
              <option value="Asia/Shanghai">Shanghai</option>
            </select>
          </div>
          
          <div>
            <label className="form-label">Language</label>
            <select
              value={formData.preferences?.language || 'en'}
              onChange={(e) => handleNestedInputChange('preferences', undefined, 'language', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={!isEditing}
            >
              <option value="en">English</option>
              <option value="es">Spanish</option>
              <option value="fr">French</option>
              <option value="de">German</option>
              <option value="it">Italian</option>
              <option value="pt">Portuguese</option>
              <option value="ru">Russian</option>
              <option value="zh">Chinese</option>
              <option value="ja">Japanese</option>
            </select>
          </div>
          
          <div>
            <label className="form-label">Currency</label>
            <select
              value={formData.preferences?.currency || 'USD'}
              onChange={(e) => handleNestedInputChange('preferences', undefined, 'currency', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={!isEditing}
            >
              <option value="USD">USD ($)</option>
              <option value="EUR">EUR (€)</option>
              <option value="GBP">GBP (£)</option>
              <option value="CAD">CAD (C$)</option>
              <option value="AUD">AUD (A$)</option>
              <option value="JPY">JPY (¥)</option>
              <option value="CNY">CNY (¥)</option>
            </select>
          </div>
        </div>
      </div>
    </div>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'basic':
        return renderBasicInfo();
      case 'professional':
        return renderProfessionalInfo();
      case 'contact':
        return renderContactInfo();
      case 'education':
        return renderEducation();
      case 'skills':
        return renderSkills();
      case 'preferences':
        return renderPreferences();
      default:
        return null;
    }
  };

  return (
    <div className="p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
          <h1 className="text-3xl font-bold text-gray-900">My Profile</h1>
          <p className="text-gray-600 mt-2">
            Manage your profile information and preferences
          </p>
            </div>
            <div className="flex gap-3">
              {isEditing ? (
                <>
                  <Button
                    variant="outline"
                    onClick={handleCancel}
                    disabled={isLoading}
                  >
                    <X className="w-4 h-4 mr-2" />
                    Cancel
                  </Button>
                  <Button
                    onClick={handleSave}
                    disabled={isLoading}
                  >
                    <Save className="w-4 h-4 mr-2" />
                    {isLoading ? 'Saving...' : 'Save Changes'}
                  </Button>
                </>
              ) : (
                <Button onClick={() => setIsEditing(true)}>
                  <Edit3 className="w-4 h-4 mr-2" />
                  Edit Profile
                </Button>
              )}
            </div>
          </div>
        </div>

        {/* Profile Header Card */}
        <div className="card mb-6">
          <div className="card-body">
            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 sm:gap-6">
              <div className="relative flex-shrink-0">
                <img
                  className="w-20 h-20 sm:w-24 sm:h-24 rounded-full"
                  src={user?.avatar || `https://ui-avatars.com/api/?name=${user?.firstName}+${user?.lastName}&background=3B82F6&color=fff&size=96`}
                  alt={`${user?.firstName} ${user?.lastName}`}
                />
                {isEditing && (
                  <label className="absolute bottom-0 right-0 w-7 h-7 sm:w-8 sm:h-8 bg-primary-600 text-white rounded-full flex items-center justify-center hover:bg-primary-700 cursor-pointer">
                  <Camera className="w-3 h-3 sm:w-4 sm:h-4" />
                    <input
                      type="file"
                      accept="image/*"
                      onChange={(e) => {
                        const file = e.target.files?.[0];
                        if (file) {
                          handleAvatarUpload(file);
                        }
                      }}
                      className="hidden"
                    />
                  </label>
                )}
              </div>
              <div className="flex-1 min-w-0">
                <h2 className="text-xl sm:text-2xl font-bold text-gray-900 truncate">
                  {user?.firstName} {user?.lastName}
                </h2>
                <p className="text-gray-600 truncate">{user?.email}</p>
                <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 mt-2 text-sm text-gray-500">
                  <span className="flex items-center gap-1">
                    <MapPin className="w-4 h-4 flex-shrink-0" />
                    <span className="truncate">
                      {formData.location?.city && formData.location?.state 
                        ? `${formData.location.city}, ${formData.location.state}`
                        : 'Location not set'
                      }
                    </span>
                  </span>
                  <span className="flex items-center gap-1">
                    <Mail className="w-4 h-4 flex-shrink-0" />
                    <span className="truncate">
                      {formData.currentPosition 
                        ? formData.currentPosition
                        : 'Position not set'
                      }
                    </span>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs */}
            <div className="card">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8 px-6" aria-label="Tabs">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`${
                    activeTab === tab.id
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2`}
                >
                  <span>{tab.icon}</span>
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>

          <div className="p-6">
            {renderTabContent()}
          </div>
        </div>
      </div>
    </div>
  );
};
