import { Request, Response } from 'express';
import { BaseError } from '../utils/errors';
import { ResponseUtil } from '../utils/response';
import { logger } from '../utils/logger';

export const errorHandler = (
  error: Error,
  req: Request,
  res: Response
  // _next: NextFunction
): void => {
  logger.error('Error occurred:', {
    message: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
  });

  // Handle known operational errors
  if (error instanceof BaseError) {
    res
      .status(error.statusCode)
      .json(
        ResponseUtil.error(
          error.message,
          error.statusCode,
          [error.message],
          req.path
        )
      );
    return;
  }

  // Handle Mongoose validation errors
  if (error.name === 'ValidationError') {
    const mongooseError = error as unknown as {
      errors: Record<string, { message: string }>;
    };
    const validationErrors = Object.values(mongooseError.errors).map(
      err => err.message
    );
    res
      .status(400)
      .json(
        ResponseUtil.error('Validation failed', 400, validationErrors, req.path)
      );
    return;
  }

  // Handle Mongoose cast errors
  if (error.name === 'CastError') {
    res
      .status(400)
      .json(
        ResponseUtil.error(
          'Invalid data format',
          400,
          ['Invalid ID format'],
          req.path
        )
      );
    return;
  }

  // Handle JWT errors
  if (error.name === 'JsonWebTokenError') {
    res
      .status(401)
      .json(
        ResponseUtil.error('Invalid token', 401, ['Token is invalid'], req.path)
      );
    return;
  }

  if (error.name === 'TokenExpiredError') {
    res
      .status(401)
      .json(
        ResponseUtil.error(
          'Token expired',
          401,
          ['Token has expired'],
          req.path
        )
      );
    return;
  }

  // Handle MongoDB duplicate key errors
  const mongoError = error as {
    code?: number;
    keyValue?: Record<string, unknown>;
  };
  if (mongoError.code === 11000) {
    const field = mongoError.keyValue
      ? Object.keys(mongoError.keyValue)[0]
      : 'field';
    res
      .status(409)
      .json(
        ResponseUtil.error(
          'Resource already exists',
          409,
          [`${field} already exists`],
          req.path
        )
      );
    return;
  }

  // Handle unknown errors
  res
    .status(500)
    .json(
      ResponseUtil.error(
        'Internal server error',
        500,
        process.env.NODE_ENV === 'development'
          ? [error.message]
          : ['Something went wrong'],
        req.path
      )
    );
};
