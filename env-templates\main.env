# Job Application Platform - Main Environment Configuration
# Copy this to .env in the root directory

# ================================
# APPLICATION CONFIGURATION
# ================================
NODE_ENV=development
API_VERSION=1.0.0
PORT=3000

# ================================
# DATABASE CONFIGURATION
# ================================
MONGODB_URI=*****************************************************************************

# ================================
# REDIS CONFIGURATION
# ================================
REDIS_URL=redis://:jobplatform2024@localhost:6379

# ================================
# JWT CONFIGURATION
# ================================
# IMPORTANT: Change these in production!
JWT_SECRET=job-platform-super-secret-jwt-key-for-development-change-in-production-must-be-32-chars-minimum
JWT_REFRESH_SECRET=job-platform-super-secret-refresh-key-for-development-change-in-production-must-be-32-chars
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# ================================
# GOOGLE OAUTH CONFIGURATION
# ================================
# Get these from Google Cloud Console
GOOGLE_CLIENT_ID=your-google-client-id-from-console
GOOGLE_CLIENT_SECRET=your-google-client-secret-from-console
GOOGLE_CALLBACK_URL=http://localhost:3000/api/v1/auth/google/callback

# ================================
# CLOUDINARY CONFIGURATION
# ================================
CLOUDINARY_CLOUD_NAME=your-cloudinary-cloud-name
CLOUDINARY_API_KEY=your-cloudinary-api-key
CLOUDINARY_API_SECRET=your-cloudinary-api-secret

# ================================
# AWS CONFIGURATION
# ================================
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-aws-access-key-id
AWS_SECRET_ACCESS_KEY=your-aws-secret-access-key
AWS_S3_BUCKET=job-platform-files-dev

# ================================
# EMAIL CONFIGURATION (SendGrid)
# ================================
SENDGRID_API_KEY=SG.your-sendgrid-api-key-here
FROM_EMAIL=<EMAIL>

# ================================
# STRIPE CONFIGURATION
# ================================
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key-here
STRIPE_WEBHOOK_SECRET=whsec_your-webhook-secret-here

# ================================
# EXTERNAL API CONFIGURATION
# ================================
# LinkedIn API
LINKEDIN_CLIENT_ID=your-linkedin-client-id
LINKEDIN_CLIENT_SECRET=your-linkedin-client-secret

# Indeed API
INDEED_API_KEY=your-indeed-api-key

# Glassdoor API
GLASSDOOR_API_KEY=your-glassdoor-api-key

# ================================
# RATE LIMITING CONFIGURATION
# ================================
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
PREMIUM_RATE_LIMIT_MAX_REQUESTS=1000

# ================================
# FILE UPLOAD CONFIGURATION
# ================================
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=pdf,doc,docx

# ================================
# SECURITY CONFIGURATION
# ================================
BCRYPT_ROUNDS=12
SESSION_SECRET=job-platform-session-secret-for-development-change-in-production-must-be-32-chars-minimum
CORS_ORIGIN=http://localhost:3000,http://localhost:3001,http://localhost:8080

# ================================
# LOGGING CONFIGURATION
# ================================
LOG_LEVEL=debug

# ================================
# HEALTH CHECK CONFIGURATION
# ================================
HEALTH_CHECK_INTERVAL=30000

# ================================
# SERVICE URLS (for development)
# ================================
AUTH_SERVICE_URL=http://localhost:3001
USER_SERVICE_URL=http://localhost:3002
JOB_SERVICE_URL=http://localhost:3003
RESUME_SERVICE_URL=http://localhost:3004
ANALYTICS_SERVICE_URL=http://localhost:3005
NOTIFICATION_SERVICE_URL=http://localhost:3006
INTEGRATION_SERVICE_URL=http://localhost:3007
PAYMENT_SERVICE_URL=http://localhost:3008

# ================================
# FRONTEND CONFIGURATION
# ================================
FRONTEND_URL=http://localhost:3000
ADMIN_EMAIL=<EMAIL>

# ================================
# DEVELOPMENT FLAGS
# ================================
ENABLE_SWAGGER=true
ENABLE_DEBUG_LOGS=true
MOCK_EXTERNAL_SERVICES=true