// Form validation utilities
export interface ValidationError {
  field: string;
  message: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

// Email validation
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Phone number validation (supports international formats)
export const validatePhoneNumber = (phone: string): boolean => {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
  return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
};

// URL validation
export const validateUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

// LinkedIn URL validation
export const validateLinkedInUrl = (url: string): boolean => {
  if (!url) return true; // Optional field
  const linkedinRegex = /^https?:\/\/(www\.)?linkedin\.com\/in\/[a-zA-Z0-9\-]+\/?$/;
  return linkedinRegex.test(url);
};

// GitHub URL validation
export const validateGitHubUrl = (url: string): boolean => {
  if (!url) return true; // Optional field
  const githubRegex = /^https?:\/\/(www\.)?github\.com\/[a-zA-Z0-9\-]+\/?$/;
  return githubRegex.test(url);
};

// Date validation
export const validateDate = (date: string): boolean => {
  if (!date) return true; // Optional field
  const dateObj = new Date(date);
  return !isNaN(dateObj.getTime());
};

// Date range validation (start date before end date)
export const validateDateRange = (startDate: string, endDate: string): boolean => {
  if (!startDate || !endDate) return true; // Optional fields
  return new Date(startDate) <= new Date(endDate);
};

// Salary range validation
export const validateSalaryRange = (min: number, max: number): boolean => {
  if (min === undefined || max === undefined) return true; // Optional fields
  return min <= max;
};

// Text length validation
export const validateTextLength = (text: string, minLength: number, maxLength: number): boolean => {
  if (!text) return true; // Optional field
  return text.length >= minLength && text.length <= maxLength;
};

// Profile validation functions
export const validateBasicInfo = (data: any): ValidationResult => {
  const errors: ValidationError[] = [];

  // Phone number validation
  if (data.phoneNumber && !validatePhoneNumber(data.phoneNumber)) {
    errors.push({
      field: 'phoneNumber',
      message: 'Please enter a valid phone number'
    });
  }

  // Bio length validation
  if (data.bio && !validateTextLength(data.bio, 10, 1000)) {
    errors.push({
      field: 'bio',
      message: 'Bio must be between 10 and 1000 characters'
    });
  }

  // Date of birth validation
  if (data.dateOfBirth && !validateDate(data.dateOfBirth)) {
    errors.push({
      field: 'dateOfBirth',
      message: 'Please enter a valid date of birth'
    });
  }

  // Nationality validation
  if (data.nationality && !validateTextLength(data.nationality, 2, 50)) {
    errors.push({
      field: 'nationality',
      message: 'Nationality must be between 2 and 50 characters'
    });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

export const validateProfessionalInfo = (data: any): ValidationResult => {
  const errors: ValidationError[] = [];

  // Position validation
  if (data.currentPosition && !validateTextLength(data.currentPosition, 2, 100)) {
    errors.push({
      field: 'currentPosition',
      message: 'Position must be between 2 and 100 characters'
    });
  }

  // Company validation
  if (data.currentCompany && !validateTextLength(data.currentCompany, 2, 100)) {
    errors.push({
      field: 'currentCompany',
      message: 'Company must be between 2 and 100 characters'
    });
  }

  // Years of experience validation
  if (data.yearsOfExperience !== undefined) {
    if (data.yearsOfExperience < 0 || data.yearsOfExperience > 50) {
      errors.push({
        field: 'yearsOfExperience',
        message: 'Years of experience must be between 0 and 50'
      });
    }
  }

  // Salary range validation
  if (data.expectedSalary) {
    const { min, max } = data.expectedSalary;
    if (min !== undefined && max !== undefined && !validateSalaryRange(min, max)) {
      errors.push({
        field: 'expectedSalary',
        message: 'Minimum salary must be less than or equal to maximum salary'
      });
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

export const validateContactInfo = (data: any): ValidationResult => {
  const errors: ValidationError[] = [];

  // Website validation
  if (data.website && !validateUrl(data.website)) {
    errors.push({
      field: 'website',
      message: 'Please enter a valid website URL'
    });
  }

  // LinkedIn validation
  if (data.linkedin && !validateLinkedInUrl(data.linkedin)) {
    errors.push({
      field: 'linkedin',
      message: 'Please enter a valid LinkedIn profile URL'
    });
  }

  // GitHub validation
  if (data.github && !validateGitHubUrl(data.github)) {
    errors.push({
      field: 'github',
      message: 'Please enter a valid GitHub profile URL'
    });
  }

  // Portfolio validation
  if (data.portfolio && !validateUrl(data.portfolio)) {
    errors.push({
      field: 'portfolio',
      message: 'Please enter a valid portfolio URL'
    });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// Combined validation for all profile sections
export const validateProfileData = (data: any, section?: string): ValidationResult => {
  let errors: ValidationError[] = [];

  switch (section) {
    case 'basic':
      return validateBasicInfo(data);
    case 'professional':
      return validateProfessionalInfo(data);
    case 'contact':
      return validateContactInfo(data);
    default:
      // Validate all sections
      const basicResult = validateBasicInfo(data);
      const professionalResult = validateProfessionalInfo(data);
      const contactResult = validateContactInfo(data);
      
      errors = [
        ...basicResult.errors,
        ...professionalResult.errors,
        ...contactResult.errors
      ];

      return {
        isValid: errors.length === 0,
        errors
      };
  }
};

// Sanitize input data
export const sanitizeProfileData = (data: any): any => {
  const sanitized = { ...data };

  // Trim string fields
  const stringFields = ['bio', 'phoneNumber', 'currentPosition', 'currentCompany', 'website', 'linkedin', 'github', 'portfolio', 'nationality'];
  stringFields.forEach(field => {
    if (sanitized[field] && typeof sanitized[field] === 'string') {
      sanitized[field] = sanitized[field].trim();
    }
  });

  // Sanitize location data
  if (sanitized.location) {
    const locationFields = ['country', 'state', 'city', 'zipCode'];
    locationFields.forEach(field => {
      if (sanitized.location[field] && typeof sanitized.location[field] === 'string') {
        sanitized.location[field] = sanitized.location[field].trim();
      }
    });
  }

  // Sanitize URLs
  const urlFields = ['website', 'linkedin', 'github', 'portfolio'];
  urlFields.forEach(field => {
    if (sanitized[field] && typeof sanitized[field] === 'string') {
      // Remove trailing slashes and normalize
      sanitized[field] = sanitized[field].replace(/\/+$/, '');
    }
  });

  return sanitized;
};
