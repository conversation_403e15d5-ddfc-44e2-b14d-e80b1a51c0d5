import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import morgan from 'morgan';
import cron from 'node-cron';   
// Using DigitalOcean environment variables directly
import { database } from './database/connection';
import { logger } from './utils/logger';
import { errorHandler } from './middleware/error.middleware';
import { jobRoutes } from './routes/job.routes';
import { applicationRoutes } from './routes/application.routes';
import { healthRoutes } from './routes/health.routes';
import { syncRoutes } from './routes/sync.routes';
import { dashboardRoutes } from './routes/dashboard.routes';
import { JobCleanupService } from './services/job-cleanup.service';
import { JobSyncService } from './services/job-sync.service';

class JobService {
  private app: express.Application;
  private server: ReturnType<express.Application['listen']> | null = null;
  private jobCleanupService: JobCleanupService;
  private jobSyncService: JobSyncService;

  constructor() {
    this.app = express();
    // Enable trust proxy for proper IP detection behind reverse proxy
    this.app.set('trust proxy', true);
    this.jobCleanupService = new JobCleanupService();
    this.jobSyncService = new JobSyncService();
    this.setupMiddleware();
    this.setupRoutes();
    this.setupCronJobs();
    this.setupErrorHandling();
  }

  private setupMiddleware(): void {
    this.app.use(helmet());
    this.app.use(cors({ origin: true, credentials: true }));
    this.app.use(compression());
    this.app.use(express.json({ limit: '5mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '5mb' }));
    this.app.use(
      morgan('combined', {
        stream: { write: message => logger.info(message.trim()) },
      })
    );
    this.app.use((req, res, next) => {
      req.headers['x-request-id'] =
        req.headers['x-request-id'] ??
        Math.random().toString(36).substring(2, 15);
      res.setHeader('X-Request-ID', req.headers['x-request-id'] as string);
      next();
    });
  }

  private setupRoutes(): void {
    this.app.use('/health', healthRoutes);
    // Support both /jobs and /api/v1/jobs for API Gateway compatibility
    this.app.use('/jobs', jobRoutes);
    this.app.use('/api/v1/jobs', jobRoutes);
    this.app.use('/applications', applicationRoutes);
    this.app.use('/api/v1/applications', applicationRoutes);
    this.app.use('/sync', syncRoutes);
    this.app.use('/api/v1/sync', syncRoutes);
    this.app.use('/dashboard', dashboardRoutes);
    this.app.use('/api/v1/dashboard', dashboardRoutes);
    this.app.get('/api/v1', (req, res) => {
      res.json({
        name: 'Job Platform Job Service',
        version: process.env.API_VERSION || '1.0.0',
        environment: process.env.NODE_ENV || 'development',
        timestamp: new Date().toISOString(),
        features: [
          'Job Management',
          'Application Tracking',
          'Job Recommendations',
        ],
      });
    });
  }

  private setupCronJobs(): void {
    // Clean up expired jobs every hour
    cron.schedule('0 * * * *', async () => {
      try {
        await this.jobCleanupService.cleanupExpiredJobs();
      } catch (error) {
        logger.error('Job cleanup failed:', error);
      }
    });

    // Sync jobs from external APIs every 6 hours
    cron.schedule('0 */6 * * *', async () => {
      try {
        logger.info('Starting scheduled job sync...');
        const result = await this.jobSyncService.syncJobs('developer', 'us', 50);
        logger.info(`Scheduled job sync completed: ${result.synced} synced, ${result.skipped} skipped, ${result.errors} errors`);
      } catch (error) {
        logger.error('Scheduled job sync failed:', error);
      }
    });

    // Clean up old external jobs every day at 2 AM
    cron.schedule('0 2 * * *', async () => {
      try {
        const deletedCount = await this.jobSyncService.cleanupOldJobs(30);
        logger.info(`Cleaned up ${deletedCount} old external jobs`);
      } catch (error) {
        logger.error('Old job cleanup failed:', error);
      }
    });
  }

  private setupErrorHandling(): void {
    this.app.use((req, res) => {
      res.status(404).json({
        success: false,
        message: `Route ${req.originalUrl} not found`,
      });
    });
    this.app.use(errorHandler);
  }

  public async start(): Promise<void> {
    try {
      // Start server first to respond to health checks
      const port = parseInt(process.env.PORT || '3003');
      this.server = this.app.listen(port, '0.0.0.0', () => {
        logger.info(`💼 Job Service running on port ${port}`);
        logger.info(`📝 Environment: ${process.env.NODE_ENV || 'development'}`);
        logger.info(`🔗 Health Check: http://localhost:${port}/health`);
      });

      // Connect to database after server is running
      try {
        await database.connect();
        logger.info('Database connected successfully');
      } catch (dbError) {
        logger.warn('Database connection failed - service will run with limited functionality:', dbError);
        // Continue running without database - some features will be disabled
      }

      process.on('SIGTERM', () => void this.shutdown());
      process.on('SIGINT', () => void this.shutdown());
    } catch (error) {
      logger.error('Failed to start Job Service:', error);
      process.exit(1);
    }
  }

  private async shutdown(): Promise<void> {
    logger.info('🔄 Job Service shutting down...');
    if (this.server) {
      await new Promise<void>(resolve => {
        this.server!.close(() => {
          void database.disconnect().then(() => {
            logger.info('✅ Job Service shutdown completed');
            process.exit(0);
          });
        });
        resolve();
      });
    }
  }
}

const jobService = new JobService();
jobService.start().catch(error => {
  logger.error('Failed to start Job Service:', error);
  process.exit(1);
});
