import { Router } from 'express';
import { ResponseUtil } from '../utils/response';
import { JobSyncService } from '../services/job-sync.service';
import { MonitoringService } from '../services/monitoring.service';

const router = Router();
const jobSyncService = new JobSyncService();
const monitoringService = MonitoringService.getInstance();

// Manual job sync endpoint
router.post('/sync', async (req, res) => {
  try {
    const { query = 'developer', location = 'us', maxJobs = 50 } = req.body;
    
    // Input validation
    if (typeof query !== 'string' || query.length > 100) {
      const response = ResponseUtil.error('Invalid query parameter', 400);
      return res.status(response.statusCode).json(response);
    }
    
    if (typeof location !== 'string' || location.length > 50) {
      const response = ResponseUtil.error('Invalid location parameter', 400);
      return res.status(response.statusCode).json(response);
    }
    
    if (typeof maxJobs !== 'number' || maxJobs < 1 || maxJobs > 100) {
      const response = ResponseUtil.error('Invalid maxJobs parameter (must be 1-100)', 400);
      return res.status(response.statusCode).json(response);
    }
    
    const result = await jobSyncService.syncJobs(query, location, maxJobs);
    
    const response = ResponseUtil.success(
      {
        synced: result.synced,
        skipped: result.skipped,
        errors: result.errors,
        total: result.synced + result.skipped + result.errors,
      },
      'Job sync completed successfully'
    );

    return res.status(response.statusCode).json(response);
  } catch (error) {
    const response = ResponseUtil.error('Job sync failed', 500);
    return res.status(response.statusCode).json(response);
  }
});

// Sync jobs by category
router.post('/sync/categories', async (req, res) => {
  try {
    await jobSyncService.syncJobsByCategory();
    
    const response = ResponseUtil.success(
      null,
      'Category-based job sync completed successfully'
    );

    res.status(response.statusCode).json(response);
  } catch (error) {
    const response = ResponseUtil.error('Category sync failed', 500);
    res.status(response.statusCode).json(response);
  }
});

// Get sync statistics
router.get('/stats', async (req, res) => {
  try {
    const stats = await jobSyncService.getSyncStats();
    
    const response = ResponseUtil.success(stats, 'Sync statistics retrieved successfully');
    res.status(response.statusCode).json(response);
  } catch (error) {
    const response = ResponseUtil.error('Failed to retrieve sync statistics', 500);
    res.status(response.statusCode).json(response);
  }
});

// Get system health and metrics
router.get('/health', async (req, res) => {
  try {
    const health = await monitoringService.getSystemHealth();
    
    const response = ResponseUtil.success(health, 'System health retrieved successfully');
    res.status(response.statusCode).json(response);
  } catch (error) {
    const response = ResponseUtil.error('Failed to retrieve system health', 500);
    res.status(response.statusCode).json(response);
  }
});

// Get monitoring metrics
router.get('/metrics', async (req, res) => {
  try {
    const metrics = monitoringService.getMetrics();
    
    const response = ResponseUtil.success(metrics, 'Monitoring metrics retrieved successfully');
    res.status(response.statusCode).json(response);
  } catch (error) {
    const response = ResponseUtil.error('Failed to retrieve monitoring metrics', 500);
    res.status(response.statusCode).json(response);
  }
});

// Cleanup old jobs
router.delete('/cleanup', async (req, res) => {
  try {
    const { daysOld = 30 } = req.query;
    const deletedCount = await jobSyncService.cleanupOldJobs(Number(daysOld));
    
    const response = ResponseUtil.success(
      { deletedCount },
      `Cleaned up ${deletedCount} old external jobs`
    );

    res.status(response.statusCode).json(response);
  } catch (error) {
    const response = ResponseUtil.error('Job cleanup failed', 500);
    res.status(response.statusCode).json(response);
  }
});

export { router as syncRoutes };
