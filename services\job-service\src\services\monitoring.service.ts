import { logger } from '../utils/logger';
import { Job } from '../models/job.model';
import { database } from '../database/connection';

export class MonitoringService {
  private static instance: MonitoringService;
  private metrics: {
    totalSyncs: number;
    successfulSyncs: number;
    failedSyncs: number;
    lastSyncTime?: Date;
    averageSyncTime: number;
    totalJobsProcessed: number;
  } = {
    totalSyncs: 0,
    successfulSyncs: 0,
    failedSyncs: 0,
    averageSyncTime: 0,
    totalJobsProcessed: 0,
  };

  private constructor() {}

  public static getInstance(): MonitoringService {
    if (!MonitoringService.instance) {
      MonitoringService.instance = new MonitoringService();
    }
    return MonitoringService.instance;
  }

  /**
   * Record sync metrics
   */
  public recordSyncMetrics(
    success: boolean,
    syncTime: number,
    jobsProcessed: number
  ): void {
    this.metrics.totalSyncs++;
    this.metrics.totalJobsProcessed += jobsProcessed;
    
    if (success) {
      this.metrics.successfulSyncs++;
    } else {
      this.metrics.failedSyncs++;
    }
    
    this.metrics.lastSyncTime = new Date();
    
    // Calculate rolling average sync time
    this.metrics.averageSyncTime = 
      (this.metrics.averageSyncTime * (this.metrics.totalSyncs - 1) + syncTime) / 
      this.metrics.totalSyncs;
    
    logger.info('Sync metrics updated:', {
      totalSyncs: this.metrics.totalSyncs,
      successRate: (this.metrics.successfulSyncs / this.metrics.totalSyncs * 100).toFixed(2) + '%',
      averageSyncTime: this.metrics.averageSyncTime.toFixed(2) + 'ms',
      totalJobsProcessed: this.metrics.totalJobsProcessed,
    });
  }

  /**
   * Get current metrics
   */
  public getMetrics() {
    return {
      ...this.metrics,
      successRate: this.metrics.totalSyncs > 0 
        ? (this.metrics.successfulSyncs / this.metrics.totalSyncs * 100).toFixed(2) + '%'
        : '0%',
    };
  }

  /**
   * Get database health status
   */
  public async getDatabaseHealth(): Promise<{
    connected: boolean;
    responseTime: number;
    jobCount: number;
    error?: string;
  }> {
    const startTime = Date.now();
    
    try {
      // Test database connection
      if (!database.isHealthy()) {
        throw new Error('Database connection is not healthy');
      }
      
      // Get job count
      const jobCount = await Job.countDocuments();
      
      const responseTime = Date.now() - startTime;
      
      return {
        connected: true,
        responseTime,
        jobCount,
      };
    } catch (error) {
      return {
        connected: false,
        responseTime: Date.now() - startTime,
        jobCount: 0,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Get system health status
   */
  public async getSystemHealth(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    services: {
      database: any;
      memory: any;
      uptime: number;
    };
    metrics: any;
  }> {
    const databaseHealth = await this.getDatabaseHealth();
    const memoryUsage = process.memoryUsage();
    const uptime = process.uptime();
    
    let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
    
    if (!databaseHealth.connected) {
      status = 'unhealthy';
    } else if (databaseHealth.responseTime > 5000) {
      status = 'degraded';
    }
    
    return {
      status,
      services: {
        database: databaseHealth,
        memory: {
          rss: Math.round(memoryUsage.rss / 1024 / 1024) + ' MB',
          heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024) + ' MB',
          heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024) + ' MB',
          external: Math.round(memoryUsage.external / 1024 / 1024) + ' MB',
        },
        uptime: Math.round(uptime),
      },
      metrics: this.getMetrics(),
    };
  }

  /**
   * Reset metrics (for testing)
   */
  public resetMetrics(): void {
    this.metrics = {
      totalSyncs: 0,
      successfulSyncs: 0,
      failedSyncs: 0,
      averageSyncTime: 0,
      totalJobsProcessed: 0,
    };
    logger.info('Metrics reset');
  }
}
