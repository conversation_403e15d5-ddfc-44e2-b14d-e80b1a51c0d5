import { cloudinary, isCloudinaryConfigured } from '../config/cloudinary';
import { logger } from '../utils/logger';

export interface ImageUploadOptions {
  folder: string;
  transformation?: any;
  publicId?: string;
  overwrite?: boolean;
  resourceType?: 'image' | 'video' | 'raw' | 'auto';
}

export interface ImageUploadResult {
  publicId: string;
  secureUrl: string;
  width: number;
  height: number;
  format: string;
  bytes: number;
}

export class ImageService {
  /**
   * Upload image to Cloudinary with optimization
   */
  async uploadImage(
    file: Buffer,
    options: ImageUploadOptions
  ): Promise<ImageUploadResult> {
    if (!isCloudinaryConfigured) {
      throw new Error('Cloudinary is not configured. Please set up Cloudinary credentials.');
    }

    try {
      // Detect MIME type from buffer
      const mimeType = this.detectMimeType(file);
      
      const uploadOptions: any = {
        folder: options.folder,
        overwrite: options.overwrite || false,
        resource_type: options.resourceType || 'image',
        transformation: {
          quality: 'auto',
          fetch_format: 'auto',
          ...options.transformation,
        },
        // Add timeout for upload
        timeout: 60000, // Increased timeout to 60 seconds
      };

      // Only add public_id if it's provided
      if (options.publicId) {
        uploadOptions.public_id = options.publicId;
      }

      // Create a promise with timeout
      const uploadPromise = cloudinary.uploader.upload(
        `data:${mimeType};base64,${file.toString('base64')}`,
        uploadOptions
      );

      // Add timeout wrapper
      const timeoutPromise = new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Upload timeout after 60 seconds')), 60000)
      );

      const uploadResult = await Promise.race([uploadPromise, timeoutPromise]) as any;

      logger.info('Image uploaded successfully', {
        publicId: uploadResult.public_id,
        url: uploadResult.secure_url,
        folder: options.folder,
        size: file.length,
      });

      return {
        publicId: uploadResult.public_id,
        secureUrl: uploadResult.secure_url,
        width: uploadResult.width,
        height: uploadResult.height,
        format: uploadResult.format,
        bytes: uploadResult.bytes,
      };
    } catch (error: any) {
      logger.error('Image upload failed:', error);
      
      // Handle specific error types
      if (error.message?.includes('timeout')) {
        throw new Error('Upload timeout. Please try with a smaller image (max 2MB).');
      }
      
      if (error.message?.includes('ECONNRESET') || error.message?.includes('aborted')) {
        throw new Error('Connection lost during upload. Please try again.');
      }
      
      if (error.message?.includes('File too large')) {
        throw new Error('File is too large. Please choose a smaller image (max 2MB).');
      }
      
      throw new Error(`Failed to upload image to Cloudinary: ${error.message || 'Unknown error'}`);
    }
  }

  /**
   * Detect MIME type from buffer
   */
  private detectMimeType(buffer: Buffer): string {
    // Check file signatures
    if (buffer[0] === 0xFF && buffer[1] === 0xD8) return 'image/jpeg';
    if (buffer[0] === 0x89 && buffer[1] === 0x50 && buffer[2] === 0x4E && buffer[3] === 0x47) return 'image/png';
    if (buffer[0] === 0x47 && buffer[1] === 0x49 && buffer[2] === 0x46) return 'image/gif';
    if (buffer[0] === 0x52 && buffer[1] === 0x49 && buffer[2] === 0x46 && buffer[3] === 0x46) return 'image/webp';
    
    // Default to JPEG
    return 'image/jpeg';
  }

  /**
   * Upload avatar with specific transformations
   */
  async uploadAvatar(
    file: Buffer,
    userId: string,
    username: string
  ): Promise<ImageUploadResult> {
    const folder = `avatars/${username}`;
    const publicId = `avatar_${userId}_${Date.now()}`;

    // Optimized transformation - remove expensive face detection
    const transformation = {
      width: 300,
      height: 300,
      crop: 'fill',
      gravity: 'center', // Much faster than 'face'
      quality: 'auto',
      fetch_format: 'webp',
    };

    return this.uploadImage(file, {
      folder,
      publicId,
      transformation,
      overwrite: true,
    });
  }

  /**
   * Delete image from Cloudinary
   */
  async deleteImage(publicId: string): Promise<boolean> {
    if (!isCloudinaryConfigured) {
      logger.warn('Cloudinary not configured. Cannot delete image:', publicId);
      return false;
    }

    try {
      const result = await cloudinary.uploader.destroy(publicId);
      
      if (result.result === 'ok') {
        logger.info('Image deleted successfully', { publicId });
        return true;
      } else {
        logger.warn('Image deletion failed', { publicId, result });
        return false;
      }
    } catch (error) {
      logger.error('Image deletion failed:', error);
      return false;
    }
  }

  /**
   * Get optimized image URL with transformations
   */
  getOptimizedUrl(
    publicId: string,
    transformations: any = {}
  ): string {
    if (!isCloudinaryConfigured) {
      logger.warn('Cloudinary not configured. Cannot generate optimized URL for:', publicId);
      return '';
    }

    return cloudinary.url(publicId, {
      quality: 'auto',
      fetch_format: 'auto',
      ...transformations,
    });
  }

  /**
   * Get avatar URL with specific transformations
   */
  getAvatarUrl(publicId: string, size: number = 300): string {
    if (!isCloudinaryConfigured) {
      logger.warn('Cloudinary not configured. Cannot generate avatar URL for:', publicId);
      return '';
    }

    return cloudinary.url(publicId, {
      width: size,
      height: size,
      crop: 'fill',
      gravity: 'face',
      quality: 'auto',
      fetch_format: 'webp',
    });
  }

  /**
   * Validate image file
   */
  validateImage(file: {
    buffer: Buffer;
    mimetype: string;
    size: number;
  }): { isValid: boolean; error?: string } {
    // Check file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.mimetype)) {
      return {
        isValid: false,
        error: 'Invalid file type. Only JPEG, PNG, and WebP images are allowed.',
      };
    }

    // Check file size (5MB limit)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      return {
        isValid: false,
        error: 'File size too large. Maximum size is 5MB.',
      };
    }

    // Check if file has content
    if (!file.buffer || file.buffer.length === 0) {
      return {
        isValid: false,
        error: 'File is empty or corrupted.',
      };
    }

    return { isValid: true };
  }

  /**
   * Generate signed upload URL for direct frontend uploads
   */
  async generateSignedUploadUrl(
    folder: string,
    publicId: string,
    transformations: any = {}
  ): Promise<{
    uploadUrl: string;
    signature: string;
    timestamp: number;
    cloudName: string;
    apiKey: string;
  }> {
    try {
      const timestamp = Math.round(new Date().getTime() / 1000);
      
      const uploadParams = {
        folder,
        public_id: publicId,
        transformation: {
          quality: 'auto',
          fetch_format: 'auto',
          ...transformations,
        },
        timestamp,
      };

      const signature = cloudinary.utils.api_sign_request(
        uploadParams,
        process.env.CLOUDINARY_API_SECRET!
      );

      return {
        uploadUrl: `https://api.cloudinary.com/v1_1/${process.env.CLOUDINARY_CLOUD_NAME}/image/upload`,
        signature,
        timestamp,
        cloudName: process.env.CLOUDINARY_CLOUD_NAME!,
        apiKey: process.env.CLOUDINARY_API_KEY!,
      };
    } catch (error) {
      logger.error('Failed to generate signed upload URL:', error);
      throw new Error('Failed to generate upload URL');
    }
  }
}

export const imageService = new ImageService();
