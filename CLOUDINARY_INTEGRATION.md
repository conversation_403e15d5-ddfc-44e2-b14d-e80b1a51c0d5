# Cloudinary Integration Documentation

## Overview

This document describes the complete Cloudinary integration for the Job Application Platform, providing production-ready image upload, optimization, and management capabilities.

## Features

- **Image Upload**: Direct file upload to Cloudinary with validation
- **Automatic Optimization**: Images are automatically converted to WebP format
- **Smart Cropping**: Avatar images are cropped to 300x300 with face detection
- **Folder Organization**: Images are organized by username in Cloudinary
- **Security**: File type and size validation, secure uploads
- **Cleanup**: Automatic deletion of old images when new ones are uploaded

## Configuration

### Environment Variables

Add these variables to your `.env` file:

```env
CLOUDINARY_CLOUD_NAME=your-cloudinary-cloud-name
CLOUDINARY_API_KEY=your-cloudinary-api-key
CLOUDINARY_API_SECRET=your-cloudinary-api-secret
```

### Getting Cloudinary Credentials

1. Sign up for a free Cloudinary account at [cloudinary.com](https://cloudinary.com)
2. Go to your Dashboard
3. Copy the Cloud Name, API Key, and API Secret from the "Product Environment Credentials" section

## Architecture

### Backend Components

#### 1. Cloudinary Configuration (`src/config/cloudinary.ts`)
- Validates environment variables
- Configures Cloudinary SDK
- Ensures secure connections

#### 2. Image Service (`src/services/image.service.ts`)
- Handles all Cloudinary operations
- Provides image optimization methods
- Manages file validation
- Handles cleanup operations

#### 3. Upload Middleware (`src/middleware/upload.middleware.ts`)
- Validates file types and sizes
- Handles multipart form data
- Provides security checks

#### 4. User Service Integration (`src/services/user.service.ts`)
- Avatar upload/update/delete methods
- Automatic old image cleanup
- Optimized URL generation

### Frontend Components

#### 1. User Service (`src/services/user.service.ts`)
- File upload API calls
- Optimized avatar URL fetching
- Error handling

#### 2. Profile Page (`src/pages/profile/ProfilePage.tsx`)
- Real file upload implementation
- Client-side validation
- Progress feedback

## API Endpoints

### Upload Avatar
```
POST /api/v1/users/avatar/upload
Content-Type: multipart/form-data

Body: FormData with 'avatar' field containing image file
```

### Update Avatar (URL)
```
POST /api/v1/users/avatar
Content-Type: application/json

Body: { "avatarUrl": "https://..." }
```

### Delete Avatar
```
DELETE /api/v1/users/avatar
```

### Get Optimized Avatar URL
```
GET /api/v1/users/avatar/optimized?size=300
```

## Image Transformations

### Avatar Images
- **Size**: 300x300 pixels
- **Crop**: Fill with face detection
- **Format**: WebP (automatic)
- **Quality**: Auto-optimized
- **Folder**: `avatars/{username}/`

### General Images
- **Format**: Auto (WebP when supported)
- **Quality**: Auto-optimized
- **Folder**: Organized by user

## Security Features

### File Validation
- **Allowed Types**: JPEG, PNG, WebP
- **Max Size**: 5MB
- **Content Validation**: Ensures file is actually an image

### Upload Security
- **Authenticated Uploads**: All uploads require valid JWT
- **User Isolation**: Images are organized by username
- **Automatic Cleanup**: Old images are deleted when new ones are uploaded

## Usage Examples

### Frontend Upload
```typescript
const handleAvatarUpload = async (file: File) => {
  try {
    // Validate file
    if (!file.type.startsWith('image/')) {
      throw new Error('Please select a valid image file');
    }
    
    if (file.size > 5 * 1024 * 1024) {
      throw new Error('File size must be less than 5MB');
    }
    
    // Upload to Cloudinary
    const updatedUser = await userService.uploadAvatar(file);
    updateUser(updatedUser);
    
    toast.success('Profile photo updated successfully!');
  } catch (error) {
    toast.error('Failed to update profile photo');
  }
};
```

### Backend Upload
```typescript
// Upload with automatic optimization
const uploadResult = await imageService.uploadAvatar(
  fileBuffer, 
  userId, 
  username
);

// Get optimized URL
const optimizedUrl = userService.getOptimizedAvatarUrl(
  avatarUrl, 
  300 // size
);
```

## Error Handling

### Common Errors
- **Invalid File Type**: Only images are allowed
- **File Too Large**: Maximum 5MB
- **Upload Failed**: Network or Cloudinary issues
- **Authentication**: Invalid or expired tokens

### Error Responses
```json
{
  "success": false,
  "message": "File size too large. Maximum size is 5MB.",
  "statusCode": 400
}
```

## Performance Optimizations

### Automatic Optimizations
- **Format Conversion**: Images are converted to WebP for better compression
- **Quality Optimization**: Automatic quality adjustment based on content
- **Responsive Images**: Different sizes for different use cases
- **CDN Delivery**: Images are served from Cloudinary's global CDN

### Caching
- **Browser Caching**: Images are cached by browsers
- **CDN Caching**: Cloudinary's CDN provides fast global delivery
- **Optimized URLs**: URLs include transformation parameters for caching

## Monitoring and Logging

### Logged Events
- Image upload success/failure
- File validation results
- Cleanup operations
- Error details

### Metrics to Monitor
- Upload success rate
- Average file size
- Storage usage
- CDN performance

## Development vs Production

### Development
- Uses development Cloudinary account
- Logs detailed information
- Allows larger file sizes for testing

### Production
- Uses production Cloudinary account
- Optimized for performance
- Strict file size limits
- Enhanced security

## Troubleshooting

### Common Issues

1. **Upload Fails**
   - Check Cloudinary credentials
   - Verify file size and type
   - Check network connectivity

2. **Images Not Displaying**
   - Verify Cloudinary URL format
   - Check CORS settings
   - Validate image permissions

3. **Slow Uploads**
   - Check file size
   - Verify network connection
   - Consider image compression

### Debug Steps
1. Check server logs for errors
2. Verify environment variables
3. Test with smaller files
4. Check Cloudinary dashboard for uploads

## Cost Optimization

### Free Tier Limits
- **Storage**: 25GB
- **Bandwidth**: 25GB/month
- **Transformations**: 25,000/month

### Optimization Tips
- Use WebP format for better compression
- Implement lazy loading
- Use appropriate image sizes
- Clean up unused images regularly

## Security Best Practices

1. **Validate All Uploads**: Check file type and size
2. **Use HTTPS**: All uploads should be over HTTPS
3. **Authenticate Users**: Require valid authentication
4. **Organize by User**: Keep user images separate
5. **Regular Cleanup**: Remove unused images
6. **Monitor Usage**: Track upload patterns

## Future Enhancements

### Planned Features
- **Bulk Upload**: Multiple image uploads
- **Image Editing**: Basic crop/resize in browser
- **Advanced Transformations**: More Cloudinary features
- **Analytics**: Upload and usage analytics
- **Backup**: Automatic image backups

### Integration Opportunities
- **AI Features**: Automatic tagging and categorization
- **Content Moderation**: Inappropriate content detection
- **Advanced Optimization**: Machine learning-based compression
- **Video Support**: Video upload and processing

## Support

For issues related to:
- **Cloudinary**: Check [Cloudinary Documentation](https://cloudinary.com/documentation)
- **Platform Integration**: Check server logs and API responses
- **Frontend Issues**: Check browser console and network tab

## Conclusion

This Cloudinary integration provides a robust, scalable, and secure solution for image management in the Job Application Platform. It handles all aspects of image upload, optimization, and delivery while maintaining security and performance standards.
