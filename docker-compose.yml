version: '3.8'

services:
  # API Gateway
  api-gateway:
    build:
      context: .
      dockerfile: services/api-gateway/Dockerfile
    container_name: job-platform-api-gateway
    restart: unless-stopped
    ports:
      - "8080:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
      - MONGODB_URI=mongodb+srv://ResumeAdmin:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0ResumePlatform
      - REDIS_URL=redis://:jobplatform2024@redis:6379
      - AUTH_SERVICE_URL=http://resume-automator-services-auth-s:3001
      - USER_SERVICE_URL=http://resume-automator-services-user-s:3002
      - JOB_SERVICE_URL=http://resume-automator-services-job-se:3003
      - RESUME_SERVICE_URL=http://resume-automator-services-resume:3005
      - JWT_SECRET=${JWT_SECRET}
      - JWT_REFRESH_SECRET=${JWT_REFRESH_SECRET}
      - CORS_ORIGIN=*
    depends_on:
      - redis
    networks:
      - job-platform-network

  # Auth Service
  auth-service:
    build:
      context: .
      dockerfile: services/auth-service/Dockerfile
    container_name: job-platform-auth-service
    restart: unless-stopped
    ports:
      - "8081:3001"
    environment:
      - NODE_ENV=development
      - PORT=3001
      - MONGODB_URI=mongodb+srv://ResumeAdmin:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0ResumePlatform
      - REDIS_URL=redis://:jobplatform2024@redis:6379
      - JWT_SECRET=${JWT_SECRET}
      - JWT_REFRESH_SECRET=${JWT_REFRESH_SECRET}
      - GOOGLE_CALLBACK_URL=https://jobs-app-ydwim.ondigitalocean.app/api/v1/auth/google/callback
      - FRONTEND_URL=https://jobs-app-ydwim.ondigitalocean.app/
      - USER_SERVICE_URL=http://resume-automator-services-user-s:3002
      
    depends_on:
      - redis
    networks:
      - job-platform-network

  # User Service
  user-service:
    build:
      context: .
      dockerfile: services/user-service/Dockerfile
    container_name: job-platform-user-service
    restart: unless-stopped
    ports:
      - "8080:3002"
    environment:
      - NODE_ENV=development
      - PORT=3002
      - MONGODB_URI=mongodb+srv://ResumeAdmin:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0ResumePlatform
      - JWT_SECRET=${JWT_SECRET}
      - JWT_REFRESH_SECRET=${JWT_REFRESH_SECRET}
      - AUTH_SERVICE_URL=http://resume-automator-services-auth-s:3001
    depends_on:
      - redis
    networks:
      - job-platform-network

  # Job Service
  job-service:
    build:
      context: .
      dockerfile: services/job-service/Dockerfile
    container_name: job-platform-job-service
    restart: unless-stopped
    ports:
      - "8080:3003"
    environment:
      - NODE_ENV=development
      - PORT=3003
      - MONGODB_URI=mongodb+srv://ResumeAdmin:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0ResumePlatform
      - JWT_SECRET=${JWT_SECRET}
      - JWT_REFRESH_SECRET=${JWT_REFRESH_SECRET}
      - USER_SERVICE_URL=http://resume-automator-services-user-s:3002
    depends_on:
      - redis
    networks:
      - job-platform-network

  # Resume Service
  resume-service:
    build:
      context: .
      dockerfile: services/resume-service/Dockerfile
    container_name: job-platform-resume-service
    restart: unless-stopped
    ports:
      - "8080:3005"
    environment:
      - NODE_ENV=development
      - PORT=3005
      - MONGODB_URI=mongodb+srv://ResumeAdmin:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0ResumePlatform
      - JWT_SECRET=${JWT_SECRET}
      - JWT_REFRESH_SECRET=${JWT_REFRESH_SECRET}   
      - AWS_REGION=us-east-1
      - AWS_S3_BUCKET=job-platform-resumes-dev
      - USER_SERVICE_URL=http://resume-automator-services-user-s:3002
    depends_on:
      - redis
    networks:
      - job-platform-network

  # Analytics Service
  analytics-service:
    build:
      context: .
      dockerfile: services/analytics-service/Dockerfile
    container_name: job-platform-analytics-service
    restart: unless-stopped
    ports:
      - "8080:3006"
    environment:
      - NODE_ENV=development
      - PORT=3006
      - MONGODB_URI=mongodb+srv://ResumeAdmin:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0ResumePlatform
      - JWT_SECRET=${JWT_SECRET}
      - JWT_REFRESH_SECRET=${JWT_REFRESH_SECRET}
    depends_on:
      - redis
    networks:
      - job-platform-network
    volumes:
      - ./services/analytics-service:/app
      - ./shared:/shared
      - /app/node_modules

  # Notification Service
  notification-service:
    build:
      context: .
      dockerfile: services/notification-service/Dockerfile
    container_name: job-platform-notification-service
    restart: unless-stopped
    ports:
      - "8080:3006"
    environment:
      - NODE_ENV=development
      - PORT=3006
      - MONGODB_URI=mongodb+srv://ResumeAdmin:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0ResumePlatform
      - JWT_SECRET=${JWT_SECRET}
      - JWT_REFRESH_SECRET=${JWT_REFRESH_SECRET}
    depends_on:
      - redis
    networks:
      - job-platform-network
    volumes:
      - ./services/notification-service:/app
      - ./shared:/shared
      - /app/node_modules

  # Integration Service
  integration-service:
    build:
      context: .
      dockerfile: services/integration-service/Dockerfile
    container_name: job-platform-integration-service
    restart: unless-stopped
    ports:
      - "8080:3007"
    environment:
      - NODE_ENV=development
      - PORT=3007
      - MONGODB_URI=mongodb+srv://ResumeAdmin:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0ResumePlatform
      - JWT_SECRET=${JWT_SECRET}
      - JWT_REFRESH_SECRET=${JWT_REFRESH_SECRET}
    depends_on:
      - redis
    networks:
      - job-platform-network
    volumes:
      - ./services/integration-service:/app
      - ./shared:/shared
      - /app/node_modules

  # Payment Service
  payment-service:
    build:
      context: .
      dockerfile: services/payment-service/Dockerfile
    container_name: job-platform-payment-service
    restart: unless-stopped
    ports:
      - "8080:3008"
    environment:
      - NODE_ENV=development
      - PORT=3008
      - MONGODB_URI=mongodb+srv://ResumeAdmin:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0ResumePlatform
      - JWT_SECRET=${JWT_SECRET}
      - JWT_REFRESH_SECRET=${JWT_REFRESH_SECRET}
    depends_on:
      - redis
    networks:
      - job-platform-network
    volumes:
      - ./services/payment-service:/app
      - ./shared:/shared
      - /app/node_modules

  # Frontend Service
  frontend-service:
    build:
      context: .
      dockerfile: services/frontend-service/Dockerfile
    container_name: job-platform-frontend
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=development
      - VITE_API_BASE_URL=http://localhost:3000/api/v1
      - VITE_APP_NAME=Job Application Platform
      - VITE_APP_VERSION=1.0.0
      - VITE_APP_ENV=development
    depends_on:
      - api-gateway
    networks:
      - job-platform-network

volumes:
  redis_data:

networks:
  job-platform-network:
    driver: bridge