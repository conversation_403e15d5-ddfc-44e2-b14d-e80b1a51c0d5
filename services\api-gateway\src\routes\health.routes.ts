import { Router } from 'express';
import { database } from '../database/connection';
import { ResponseUtil } from '../utils/response';
import { serviceRegistry } from '../services/service-registry';
import * as os from 'os';

const router = Router();

/**
 * Gateway health check
 */
router.get('/', (req, res) => {
  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV,
    version: process.env.API_VERSION ?? '1.0.0',
    database: {
      connected: database.isHealthy(),
      state: 'connected', // Simplified for now
    },
    services: serviceRegistry.getServiceHealth(),
  };

  const isHealthy =
    health.database.connected &&
    Object.values(health.services).some(service => service.isHealthy);

  if (isHealthy) {
    return res.json(ResponseUtil.success(health, 'API Gateway is healthy'));
  } else {
    return res.status(503).json({
      success: false,
      message: 'API Gateway is unhealthy',
      data: health,
    });
  }
});

/**
 * Detailed health check
 */
router.get('/detailed', (req, res) => {
  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV,
    version: process.env.API_VERSION ?? '1.0.0',
    system: {
      memory: {
        used: process.memoryUsage(),
        free: os.freemem(),
        total: os.totalmem(),
      },
      cpu: {
        usage: process.cpuUsage(),
        loadavg: os.loadavg(),
      },
      platform: process.platform,
      nodeVersion: process.version,
    },
    database: {
      connected: database.isHealthy(),
      state: 'connected', // Simplified for now
    },
    services: serviceRegistry.getServiceHealth(),
  };

  const healthyServices = serviceRegistry.getHealthyServices().length;
  const totalServices = serviceRegistry.getServiceList().length;

  health.status =
    database.isHealthy() && healthyServices > 0 ? 'healthy' : 'unhealthy';

  const statusCode = health.status === 'healthy' ? 200 : 503;

  return res.status(statusCode).json({
    success: health.status === 'healthy',
    message: `API Gateway is ${health.status}. ${healthyServices}/${totalServices} services healthy.`,
    data: health,
  });
});

/**
 * Readiness check
 */
router.get('/ready', (req, res) => {
  const isReady =
    database.isHealthy() && serviceRegistry.getHealthyServices().length > 0;

  if (isReady) {
    return res.json(
      ResponseUtil.success({ ready: true }, 'API Gateway is ready')
    );
  } else {
    return res.status(503).json({
      success: false,
      message: 'API Gateway is not ready',
      data: { ready: false },
    });
  }
});

/**
 * Liveness check
 */
router.get('/live', (req, res) => {
  return res.json(
    ResponseUtil.success({ alive: true }, 'API Gateway is alive')
  );
});

// eslint-disable-next-line @typescript-eslint/no-unused-vars
function getConnectionStateDescription(state: number): string {
  const states = {
    0: 'disconnected',
    1: 'connected',
    2: 'connecting',
    3: 'disconnecting',
    99: 'uninitialized',
  };
  return states[state as keyof typeof states] || 'unknown';
}

export { router as healthCheckRoutes };
