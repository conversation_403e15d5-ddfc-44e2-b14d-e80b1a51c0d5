#!/bin/bash

# Job Application Platform Deployment Script
# This script handles the complete deployment of the job application platform

set -e  # Exit on any error

echo "🚀 Starting Job Application Platform Deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
check_docker() {
    print_status "Checking Docker installation..."
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi

    if ! docker info &> /dev/null; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi

    print_success "Docker is running"
}

# Check if Docker Compose is available
check_docker_compose() {
    print_status "Checking Docker Compose..."
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        print_error "Docker Compose is not available. Please install Docker Compose."
        exit 1
    fi
    print_success "Docker Compose is available"
}

# Check Node.js version
check_node() {
    print_status "Checking Node.js version..."
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 22.x or higher."
        exit 1
    fi

    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        print_error "Node.js version $NODE_VERSION is too old. Please install Node.js 18.x or higher."
        exit 1
    fi

    print_success "Node.js version $(node --version) is compatible"
}

# Environment setup
setup_environment() {
    print_status "Setting up environment..."
    
    if [ ! -f .env ]; then
        print_warning ".env file not found. Creating from .env.example..."
        if [ -f .env.example ]; then
            cp .env.example .env
            print_warning "Please update .env file with your configuration before continuing."
            print_warning "Press Enter to continue after updating .env file..."
            read
        else
            print_error ".env.example file not found. Please create .env file manually."
            exit 1
        fi
    fi

    print_success "Environment configuration ready"
}

# Install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    
    # Install root dependencies
    npm install
    
    # Install shared dependencies
    cd shared && npm install && cd ..
    
    # Install service dependencies
    for service in services/*/; do
        if [ -f "$service/package.json" ]; then
            print_status "Installing dependencies for $(basename "$service")"
            cd "$service" && npm install && cd ../..
        fi
    done
    
    print_success "All dependencies installed"
}

# Build TypeScript projects
build_projects() {
    print_status "Building TypeScript projects..."
    
    # Build shared library
    cd shared && npm run build && cd ..
    
    # Build all services
    for service in services/*/; do
        if [ -f "$service/package.json" ]; then
            print_status "Building $(basename "$service")"
            cd "$service" && npm run build && cd ../..
        fi
    done
    
    print_success "All projects built successfully"
}

# Run tests
run_tests() {
    print_status "Running tests..."
    
    # Run tests for shared library
    cd shared && npm test && cd ..
    
    # Run tests for all services
    for service in services/*/; do
        if [ -f "$service/package.json" ]; then
            print_status "Testing $(basename "$service")"
            cd "$service" && npm test && cd ../..
        fi
    done
    
    print_success "All tests passed"
}

# Docker deployment
deploy_docker() {
    local environment=${1:-development}
    
    print_status "Deploying with Docker (Environment: $environment)..."
    
    if [ "$environment" = "production" ]; then
        # Production deployment
        print_status "Building production images..."
        docker-compose -f docker-compose.prod.yml build --no-cache
        
        print_status "Starting production services..."
        docker-compose -f docker-compose.prod.yml up -d
        
        print_status "Waiting for services to be healthy..."
        sleep 30
        
        # Health check
        check_service_health "production"
    else
        # Development deployment
        print_status "Starting development services..."
        docker-compose up -d
        
        print_status "Waiting for services to start..."
        sleep 20
        
        # Health check
        check_service_health "development"
    fi
    
    print_success "Docker deployment completed"
}

# Health check for services
check_service_health() {
    local environment=${1:-development}
    local base_url="http://localhost"
    
    print_status "Performing health checks..."
    
    services=(
        "3000:API Gateway"
        "3001:Auth Service"
        "3002:User Service"
        "3003:Job Service"
        "3004:Resume Service"
        "3005:Analytics Service"
        "3006:Notification Service"
        "3007:Integration Service"
        "3008:Payment Service"
    )
    
    for service in "${services[@]}"; do
        port=$(echo $service | cut -d':' -f1)
        name=$(echo $service | cut -d':' -f2)
        
        print_status "Checking $name ($base_url:$port/health)..."
        
        max_attempts=30
        attempt=1
        
        while [ $attempt -le $max_attempts ]; do
            if curl -f -s "$base_url:$port/health" > /dev/null 2>&1; then
                print_success "$name is healthy"
                break
            else
                if [ $attempt -eq $max_attempts ]; then
                    print_error "$name health check failed after $max_attempts attempts"
                    return 1
                fi
                print_status "Attempt $attempt/$max_attempts failed, retrying in 2 seconds..."
                sleep 2
                ((attempt++))
            fi
        done
    done
    
    print_success "All services are healthy"
}

# Show deployment status
show_status() {
    print_status "Deployment Status:"
    echo ""
    echo "🌐 API Gateway:        http://localhost:3000"
    echo "🔐 Auth Service:       http://localhost:3001"
    echo "👤 User Service:       http://localhost:3002"
    echo "💼 Job Service:        http://localhost:3003"
    echo "📄 Resume Service:     http://localhost:3004"
    echo "📊 Analytics Service:  http://localhost:3005"
    echo "🔔 Notification Service: http://localhost:3006"
    echo "🔗 Integration Service: http://localhost:3007"
    echo "💳 Payment Service:    http://localhost:3008"
    echo ""
    echo "📊 MongoDB:            mongodb://localhost:27017"
    echo "🗄️  Redis:              redis://localhost:6379"
    echo ""
    echo "🔍 Health Checks:"
    echo "   API Gateway:        http://localhost:3000/health"
    echo "   All Services:       http://localhost:3000/metrics (Admin only)"
    echo ""
    echo "📚 API Documentation:  http://localhost:3000/api"
    echo ""
    print_success "Job Application Platform is ready! 🎉"
}

# Cleanup function
cleanup() {
    print_status "Cleaning up..."
    docker-compose down 2>/dev/null || true
    docker-compose -f docker-compose.prod.yml down 2>/dev/null || true
}

# Main deployment function
main() {
    local environment=${1:-development}
    local skip_tests=${2:-false}
    
    echo "=========================================="
    echo "  Job Application Platform Deployment"
    echo "=========================================="
    echo ""
    
    # Trap cleanup on exit
    trap cleanup EXIT
    
    # Pre-deployment checks
    check_docker
    check_docker_compose
    check_node
    setup_environment
    
    # Build and test
    install_dependencies
    build_projects
    
    if [ "$skip_tests" != "true" ]; then
        run_tests
    else
        print_warning "Skipping tests as requested"
    fi
    
    # Deploy
    deploy_docker "$environment"
    
    # Show status
    show_status
}

# Parse command line arguments
ENVIRONMENT="development"
SKIP_TESTS="false"

while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        --skip-tests)
            SKIP_TESTS="true"
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  -e, --environment ENV    Deployment environment (development|production) [default: development]"
            echo "  --skip-tests            Skip running tests"
            echo "  -h, --help              Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                                    # Deploy in development mode"
            echo "  $0 -e production                     # Deploy in production mode"
            echo "  $0 --skip-tests                      # Deploy without running tests"
            echo "  $0 -e production --skip-tests        # Production deployment without tests"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            echo "Use -h or --help for usage information"
            exit 1
            ;;
    esac
done

# Validate environment
if [ "$ENVIRONMENT" != "development" ] && [ "$ENVIRONMENT" != "production" ]; then
    print_error "Invalid environment: $ENVIRONMENT. Must be 'development' or 'production'"
    exit 1
fi

# Run main deployment
main "$ENVIRONMENT" "$SKIP_TESTS"