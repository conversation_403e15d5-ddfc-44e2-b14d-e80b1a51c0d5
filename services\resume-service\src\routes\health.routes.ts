import { Router, Request, Response } from 'express';
import { database } from '../database/connection';
import { S3Service } from '../services/s3.service';
import { logger } from '../utils/logger';

const router = Router();

interface HealthStatus {
  status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: string;
  uptime: number;
  services: {
    database: {
      status: 'connected' | 'disconnected' | 'error';
      responseTime?: number;
    };
    s3: {
      status: 'available' | 'unavailable' | 'error';
      responseTime?: number;
    };
  };
  memory: {
    used: number;
    total: number;
    percentage: number;
  };
  version: string;
  environment: string;
}

// Basic health check
router.get('/', async (req: Request, res: Response) => {
  try {
    const health: HealthStatus = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      services: {
        database: { status: 'disconnected' },
        s3: { status: 'unavailable' }
      },
      memory: {
        used: 0,
        total: 0,
        percentage: 0
      },
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development'
    };

    // Check database connection
    const dbStart = Date.now();
    try {
      if (database.isHealthy()) {
        health.services.database = {
          status: 'connected',
          responseTime: Date.now() - dbStart
        };
      } else {
        health.services.database.status = 'disconnected';
        health.status = 'degraded';
      }
    } catch (error) {
      health.services.database.status = 'error';
      health.status = 'unhealthy';
      logger.error('Database health check failed:', error);
    }

    // Check S3 service
    const s3Start = Date.now();
    try {
      const s3Service = new S3Service();
      if (s3Service.isInitialized()) {
        health.services.s3 = {
          status: 'available',
          responseTime: Date.now() - s3Start
        };
      } else {
        health.services.s3.status = 'unavailable';
        health.status = 'degraded';
      }
    } catch (error) {
      health.services.s3.status = 'error';
      health.status = 'degraded'; // S3 is not critical for basic functionality
      logger.warn('S3 health check failed:', error);
    }

    // Memory usage
    const memUsage = process.memoryUsage();
    health.memory = {
      used: Math.round(memUsage.heapUsed / 1024 / 1024), // MB
      total: Math.round(memUsage.heapTotal / 1024 / 1024), // MB
      percentage: Math.round((memUsage.heapUsed / memUsage.heapTotal) * 100)
    };

    // Set HTTP status based on health
    const statusCode = health.status === 'healthy' ? 200 : 
                      health.status === 'degraded' ? 200 : 503;

    res.status(statusCode).json(health);
  } catch (error) {
    logger.error('Health check failed:', error);
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Detailed health check for monitoring systems
router.get('/detailed', async (req: Request, res: Response) => {
  try {
    const health: any = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      services: {},
      metrics: {},
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development'
    };

    // Database metrics
    try {
      health.services.database = {
        status: database.isHealthy() ? 'connected' : 'disconnected',
        connectionState: database.getConnectionState(),
        responseTime: 0 // Would measure actual query time in production
      };
    } catch (error) {
      health.services.database = {
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
      health.status = 'unhealthy';
    }

    // System metrics
    const memUsage = process.memoryUsage();
    health.metrics = {
      memory: {
        heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
        heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
        external: Math.round(memUsage.external / 1024 / 1024),
        rss: Math.round(memUsage.rss / 1024 / 1024)
      },
      cpu: {
        usage: process.cpuUsage(),
        loadAverage: process.platform !== 'win32' ? require('os').loadavg() : [0, 0, 0]
      },
      uptime: {
        process: process.uptime(),
        system: require('os').uptime()
      }
    };

    const statusCode = health.status === 'healthy' ? 200 : 503;
    res.status(statusCode).json(health);
  } catch (error) {
    logger.error('Detailed health check failed:', error);
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Readiness probe (for Kubernetes)
router.get('/ready', async (req: Request, res: Response) => {
  try {
    const isReady = database.isHealthy();
    
    if (isReady) {
      res.status(200).json({
        status: 'ready',
        timestamp: new Date().toISOString()
      });
    } else {
      res.status(503).json({
        status: 'not ready',
        timestamp: new Date().toISOString(),
        reason: 'Database not connected'
      });
    }
  } catch (error) {
    res.status(503).json({
      status: 'not ready',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Liveness probe (for Kubernetes)
router.get('/live', (req: Request, res: Response) => {
  res.status(200).json({
    status: 'alive',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

export { router as healthRoutes };