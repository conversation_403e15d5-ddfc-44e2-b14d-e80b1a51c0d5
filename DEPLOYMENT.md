# Job Application Platform - Deployment Guide

## 🚀 Quick Start

### Prerequisites
- Node.js 22.x or higher
- Docker and Docker Compose
- Git

### One-Command Deployment
```bash
# Clone and deploy in one command
git clone <your-repo-url> && cd job-application-platform-backend && ./scripts/deploy.sh
```

## 📋 Deployment Options

### Development Deployment
```bash
# Standard development deployment
./scripts/deploy.sh

# Skip tests for faster deployment
./scripts/deploy.sh --skip-tests
```

### Production Deployment
```bash
# Full production deployment
./scripts/deploy.sh -e production

# Production without tests (CI/CD)
./scripts/deploy.sh -e production --skip-tests
```

## 🔧 Manual Setup

### 1. Environment Configuration
```bash
# Copy environment template
cp .env.example .env

# Update with your configuration
nano .env
```

### 2. Install Dependencies
```bash
# Install all dependencies
npm install

# Install individual service dependencies
npm run setup
```

### 3. Build Projects
```bash
# Build all services
npm run build
```

### 4. Start Services
```bash
# Development mode
npm run dev

# Production mode with Docker
docker-compose -f docker-compose.prod.yml up -d
```

## 🏗️ Architecture Overview

### Services
- **API Gateway** (Port 3000) - Request routing and load balancing
- **Auth Service** (Port 3001) - Authentication and authorization
- **User Service** (Port 3002) - User management and profiles
- **Job Service** (Port 3003) - Job listings and applications
- **Resume Service** (Port 3004) - Resume processing and optimization
- **Analytics Service** (Port 3005) - User and platform analytics
- **Notification Service** (Port 3006) - Multi-channel notifications
- **Integration Service** (Port 3007) - Third-party API integrations
- **Payment Service** (Port 3008) - Subscription and billing

### Infrastructure
- **MongoDB** (Port 27017) - Primary database
- **Redis** (Port 6379) - Caching and sessions

## 🔐 Security Configuration

### Required Environment Variables
```bash
# JWT Secrets (MUST be changed in production)
JWT_SECRET=your-super-secret-jwt-key-at-least-32-characters-long
JWT_REFRESH_SECRET=your-super-secret-refresh-key-at-least-32-characters-long

# Database Credentials
MONGO_ROOT_PASSWORD=your-secure-mongodb-password
REDIS_PASSWORD=your-secure-redis-password

# External Services
GOOGLE_CLIENT_ID=your-google-oauth-client-id
GOOGLE_CLIENT_SECRET=your-google-oauth-client-secret
SENDGRID_API_KEY=your-sendgrid-api-key
STRIPE_SECRET_KEY=your-stripe-secret-key
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
```

### Security Checklist
- [ ] Change default passwords
- [ ] Update JWT secrets
- [ ] Configure CORS origins
- [ ] Set up SSL/TLS certificates
- [ ] Enable firewall rules
- [ ] Configure monitoring alerts

## 📊 Health Monitoring

### Health Check Endpoints
```bash
# API Gateway health
curl http://localhost:3000/health

# Individual service health
curl http://localhost:3001/health  # Auth Service
curl http://localhost:3002/health  # User Service
curl http://localhost:3003/health  # Job Service
# ... etc
```

### Monitoring Dashboard
- **Metrics**: http://localhost:3000/metrics (Admin only)
- **Service Status**: http://localhost:3000/health/detailed

## 🔄 CI/CD Pipeline

### GitHub Actions Example
```yaml
name: Deploy Job Platform
on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '22'
      - name: Deploy
        run: ./scripts/deploy.sh -e production --skip-tests
        env:
          JWT_SECRET: ${{ secrets.JWT_SECRET }}
          MONGO_ROOT_PASSWORD: ${{ secrets.MONGO_ROOT_PASSWORD }}
          # ... other secrets
```

## 🐳 Docker Commands

### Development
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### Production
```bash
# Build and start production services
docker-compose -f docker-compose.prod.yml up -d --build

# Scale services
docker-compose -f docker-compose.prod.yml up -d --scale job-service=3

# Update services
docker-compose -f docker-compose.prod.yml pull
docker-compose -f docker-compose.prod.yml up -d
```

## 📈 Scaling

### Horizontal Scaling
```bash
# Scale specific services
docker-compose -f docker-compose.prod.yml up -d \
  --scale api-gateway=2 \
  --scale job-service=3 \
  --scale resume-service=2
```

### Load Balancing
- API Gateway handles internal load balancing
- Use nginx or AWS ALB for external load balancing
- Configure Redis for session sharing across instances

## 🔧 Maintenance

### Database Backup
```bash
# MongoDB backup
docker exec job-platform-mongodb-prod mongodump \
  --host localhost:27017 \
  --db job_platform \
  --out /backup

# Redis backup
docker exec job-platform-redis-prod redis-cli BGSAVE
```

### Log Management
```bash
# View service logs
docker-compose logs -f [service-name]

# Log rotation (add to crontab)
0 0 * * * docker system prune -f
```

### Updates
```bash
# Update dependencies
npm update

# Rebuild services
docker-compose build --no-cache

# Rolling update
docker-compose up -d --force-recreate
```

## 🚨 Troubleshooting

### Common Issues

#### Services Won't Start
```bash
# Check logs
docker-compose logs [service-name]

# Verify environment variables
docker-compose config

# Check port conflicts
netstat -tulpn | grep :3000
```

#### Database Connection Issues
```bash
# Test MongoDB connection
docker exec -it job-platform-mongodb-prod mongo \
  --host localhost:27017 \
  -u admin -p

# Test Redis connection
docker exec -it job-platform-redis-prod redis-cli ping
```

#### Memory Issues
```bash
# Check resource usage
docker stats

# Increase memory limits in docker-compose.yml
deploy:
  resources:
    limits:
      memory: 512M
```

### Performance Optimization
- Enable Redis persistence
- Configure MongoDB replica sets
- Implement database indexing
- Use CDN for static assets
- Enable gzip compression

## 📞 Support

### Getting Help
1. Check the troubleshooting section
2. Review service logs
3. Check health endpoints
4. Verify environment configuration
5. Create an issue with logs and configuration

### Monitoring and Alerts
- Set up uptime monitoring
- Configure error tracking
- Monitor resource usage
- Set up log aggregation
- Configure backup alerts

---

**🎉 Your job application platform is now ready for production!**

For additional support and advanced configuration options, please refer to the individual service documentation in their respective directories.