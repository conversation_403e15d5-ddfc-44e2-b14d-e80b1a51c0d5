# 🎛️ Job Service Dashboard Guide

## Overview

The Job Service Dashboard provides comprehensive control and monitoring capabilities for the job service through your frontend application. This system allows administrators to manage job synchronization, monitor system health, and analyze job data in real-time.

## 🚀 Features

### **Dashboard Capabilities**
- **Real-time System Monitoring**: Live system health, database status, and performance metrics
- **Job Sync Control**: Start, stop, and monitor job synchronization processes
- **Analytics & Reporting**: Detailed analytics on job sources, types, locations, and trends
- **Job Management**: Filter, search, and manage job listings
- **System Administration**: Cleanup operations, metrics reset, and system control

### **Access Control**
- **Admin-only Access**: Dashboard features are restricted to users with `admin` role
- **Secure API Endpoints**: All dashboard endpoints require authentication
- **Role-based Navigation**: Admin section appears only for admin users

## 📊 Dashboard Components

### **1. Main Dashboard (`/admin/jobs`)**
- **System Status Cards**: Real-time health indicators
- **Job Statistics**: Total jobs, active jobs, external vs internal
- **Database Status**: Connection status and performance metrics
- **Memory Usage**: System memory consumption monitoring
- **Job Management**: Filter and search job listings
- **Recent Activity**: Latest job additions and updates

### **2. Analytics Dashboard (`/admin/analytics`)**
- **Time-based Analytics**: Job trends over time (1d, 7d, 30d)
- **Source Analysis**: Jobs by external source (Adzuna, Indeed, LinkedIn, Glassdoor)
- **Type Distribution**: Job types breakdown
- **Location Analytics**: Top job locations
- **Company Analytics**: Top hiring companies
- **Status Distribution**: Job status breakdown

### **3. Real-time Monitor**
- **Live System Metrics**: Auto-refreshing system status
- **Database Performance**: Response times and connection status
- **Sync Statistics**: Success rates and performance metrics
- **Memory Monitoring**: Real-time memory usage
- **Health Indicators**: System health status with color-coded indicators

## 🔧 API Endpoints

### **Dashboard Overview**
```http
GET /api/v1/dashboard/overview
```
Returns comprehensive system status, job statistics, and recent activity.

### **Job Sync Control**
```http
POST /api/v1/dashboard/sync/start
Content-Type: application/json

{
  "query": "developer",
  "location": "us",
  "maxJobs": 50,
  "categories": false
}
```

### **Sync Status**
```http
GET /api/v1/dashboard/sync/status
```
Returns current sync status and metrics.

### **Job Management**
```http
GET /api/v1/dashboard/jobs?page=1&limit=20&status=active&source=adzuna
```
Retrieve jobs with filtering and pagination.

### **Analytics**
```http
GET /api/v1/dashboard/analytics?period=7d
```
Returns detailed analytics for the specified period.

### **System Control**
```http
POST /api/v1/dashboard/system/cleanup
Content-Type: application/json

{
  "daysOld": 30
}
```

## 🎨 Frontend Components

### **JobDashboard Component**
- **Location**: `/services/frontend-service/src/pages/JobDashboard.tsx`
- **Features**: Main dashboard interface with system monitoring and job management
- **Auto-refresh**: Updates every 30 seconds
- **Real-time Controls**: Start sync, cleanup, and system management

### **JobAnalytics Component**
- **Location**: `/services/frontend-service/src/components/JobAnalytics.tsx`
- **Features**: Interactive charts and analytics visualization
- **Charts**: Line charts, pie charts, bar charts, and progress bars
- **Time Periods**: 1 day, 7 days, 30 days analytics

### **RealTimeMonitor Component**
- **Location**: `/services/frontend-service/src/components/RealTimeMonitor.tsx`
- **Features**: Live system monitoring with auto-refresh
- **Metrics**: System health, database status, memory usage, sync statistics
- **Controls**: Auto-refresh toggle and manual refresh

## 🔐 Security Features

### **Authentication & Authorization**
- All dashboard endpoints require valid JWT authentication
- Admin role verification for dashboard access
- Secure API key handling (no exposure in logs)

### **Input Validation**
- Comprehensive parameter validation
- SQL injection prevention
- XSS protection through input sanitization
- Rate limiting on API endpoints

### **Data Protection**
- API keys masked in error logs
- Sensitive data filtering
- Secure error handling
- Input length limits and character filtering

## 🚀 Usage Instructions

### **1. Access Dashboard**
1. Log in as an admin user
2. Navigate to `/admin/jobs` for main dashboard
3. Navigate to `/admin/analytics` for analytics dashboard

### **2. Start Job Sync**
1. Go to the main dashboard
2. Click "Sync Jobs" button
3. Monitor progress in real-time
4. View results in the dashboard

### **3. Monitor System Health**
1. Check system status cards for overall health
2. Monitor database connection and response times
3. Review memory usage and performance metrics
4. Watch sync success rates and error rates

### **4. Analyze Job Data**
1. Navigate to analytics dashboard
2. Select time period (1d, 7d, 30d)
3. Review job distribution by source, type, location
4. Analyze trends and patterns

### **5. Manage Jobs**
1. Use filters to find specific jobs
2. Search by company, location, or keywords
3. Sort by various criteria
4. View job details and statistics

## 📈 Monitoring Features

### **Real-time Metrics**
- System uptime and health status
- Database connection and performance
- Memory usage (RSS, Heap, External)
- Sync success rates and timing
- Job counts and statistics

### **Health Indicators**
- **Green**: Healthy status
- **Yellow**: Degraded performance
- **Red**: Unhealthy or critical issues

### **Performance Tracking**
- Average sync times
- Success/failure rates
- Database response times
- Memory consumption trends

## 🔧 Configuration

### **Environment Variables**
Ensure these are set in your job service environment:
```env
# Job API Keys
ADZUNA_APP_ID=your_adzuna_app_id
ADZUNA_API_KEY=your_adzuna_api_key
INDEED_PUBLISHER_ID=your_indeed_publisher_id
LINKEDIN_CLIENT_ID=your_linkedin_client_id
LINKEDIN_CLIENT_SECRET=your_linkedin_client_secret
GLASSDOOR_PARTNER_ID=your_glassdoor_partner_id
GLASSDOOR_API_KEY=your_glassdoor_api_key
```

### **API Gateway Configuration**
The dashboard endpoints are automatically routed through the API Gateway:
- `/api/v1/dashboard/*` → Job Service Dashboard Routes
- Authentication handled by API Gateway
- CORS and security headers applied

## 🎯 Best Practices

### **Dashboard Usage**
1. **Regular Monitoring**: Check system health daily
2. **Sync Management**: Monitor sync success rates
3. **Performance Tracking**: Watch for performance degradation
4. **Data Cleanup**: Regular cleanup of old jobs

### **System Administration**
1. **Backup Strategy**: Regular database backups
2. **Monitoring Alerts**: Set up alerts for critical issues
3. **Performance Optimization**: Monitor and optimize slow queries
4. **Security Updates**: Keep API keys and credentials secure

### **Analytics Usage**
1. **Trend Analysis**: Monitor job trends over time
2. **Source Performance**: Track which job sources perform best
3. **Geographic Analysis**: Understand job distribution by location
4. **Company Insights**: Identify top hiring companies

## 🚨 Troubleshooting

### **Common Issues**

#### **Dashboard Not Loading**
- Check user role (must be admin)
- Verify authentication token
- Check API Gateway configuration

#### **Sync Failures**
- Verify API keys are configured
- Check external API status
- Review error logs for specific issues

#### **Performance Issues**
- Monitor memory usage
- Check database performance
- Review sync frequency and batch sizes

#### **Data Issues**
- Verify database connection
- Check data integrity
- Review cleanup operations

### **Debug Steps**
1. Check system health indicators
2. Review error logs
3. Verify API key configuration
4. Test database connectivity
5. Monitor sync performance

## 📚 API Reference

### **Dashboard Endpoints**

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/v1/dashboard/overview` | GET | System overview and metrics |
| `/api/v1/dashboard/sync/start` | POST | Start job synchronization |
| `/api/v1/dashboard/sync/status` | GET | Sync status and metrics |
| `/api/v1/dashboard/jobs` | GET | Job listings with filters |
| `/api/v1/dashboard/analytics` | GET | Analytics data |
| `/api/v1/dashboard/system/cleanup` | POST | System cleanup |
| `/api/v1/dashboard/system/reset-metrics` | POST | Reset monitoring metrics |

### **Response Formats**

#### **Overview Response**
```json
{
  "success": true,
  "data": {
    "system": {
      "status": "healthy",
      "uptime": 3600,
      "memory": { "rss": "100MB", "heapTotal": "50MB" },
      "database": { "connected": true, "responseTime": 10 }
    },
    "sync": {
      "totalJobs": 1000,
      "externalJobs": 800,
      "activeJobs": 950
    },
    "metrics": {
      "successRate": "95%",
      "totalSyncs": 50
    }
  }
}
```

## 🎉 Conclusion

The Job Service Dashboard provides a comprehensive solution for managing and monitoring your job service. With real-time monitoring, analytics, and control capabilities, you can ensure optimal performance and data quality for your job platform.

For additional support or questions, refer to the main documentation or contact the development team.
