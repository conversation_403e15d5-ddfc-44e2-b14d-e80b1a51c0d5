#!/bin/bash

# Production build script for Frontend Service

set -e

echo "🏗️  Building Job Application Automator Frontend Service for production..."

# Install dependencies
echo "📦 Installing dependencies..."
npm ci

# Type checking
echo "🔍 Running type checks..."
npm run type-check

# Linting
echo "🧹 Running linter..."
npm run lint

# Build the application
echo "🏗️  Building application..."
npm run build

echo "✅ Build completed successfully!"
echo "📁 Built files are in the 'dist' directory"
echo "🚀 Ready for deployment!"
