#!/bin/bash

# Simple local development script that bypasses TypeScript compilation issues
# by using ts-node with proper module resolution

set -e

echo "🚀 Starting Job Application Platform in Development Mode..."

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check if Node.js version is correct
check_node_version() {
    node_version=$(node --version)
    if [[ ! "$node_version" =~ ^v22\. ]]; then
        print_warning "Expected Node.js v22.x.x, found $node_version"
        print_warning "Please switch to Node.js v22.18.0 using nvm: nvm use 22.18.0"
    else
        print_success "Node.js version: $node_version ✓"
    fi
}

# Start infrastructure
start_infrastructure() {
    print_status "Starting infrastructure (MongoDB + Redis)..."
    docker-compose up -d mongodb redis
    
    print_status "Waiting for infrastructure to be ready..."
    sleep 5
    
    # Simple health check
    if docker exec job-platform-mongodb mongosh --eval "db.adminCommand('ping')" &> /dev/null; then
        print_success "MongoDB is ready ✓"
    else
        print_warning "MongoDB might not be ready yet"
    fi
    
    if docker exec job-platform-redis redis-cli ping &> /dev/null; then
        print_success "Redis is ready ✓"
    else
        print_warning "Redis might not be ready yet"
    fi
}

# Setup environment
setup_env() {
    if [ ! -f .env ]; then
        print_status "Creating environment files..."
        ./scripts/setup-env.sh
    else
        print_success "Environment files exist ✓"
    fi
}

# Install dependencies
install_deps() {
    print_status "Installing dependencies..."
    
    # Install root dependencies
    npm install
    
    # Build shared library
    print_status "Building shared library..."
    cd shared && npm install && npm run build && cd ..
    
    # Install service dependencies
    for service in api-gateway auth-service user-service job-service resume-service; do
        if [ -d "services/$service" ]; then
            print_status "Installing dependencies for $service..."
            cd "services/$service" && npm install && cd ../..
        fi
    done
    
    print_success "Dependencies installed ✓"
}

# Start services using ts-node
start_services() {
    print_status "Starting services..."
    
    mkdir -p logs
    
    # Kill any existing processes
    pkill -f "ts-node.*src/index.ts" || true
    sleep 2
    
    # Start services with ts-node and module resolution
    services=("api-gateway:3000" "auth-service:3001" "user-service:3002" "job-service:3003" "resume-service:3004")
    
    for service_port in "${services[@]}"; do
        service=$(echo $service_port | cut -d':' -f1)
        port=$(echo $service_port | cut -d':' -f2)
        
        if [ -d "services/$service" ]; then
            print_status "Starting $service on port $port..."
            cd "services/$service"
            
            # Use ts-node with esm and path mapping
            NODE_PATH=../../shared/dist \
            PORT=$port \
            npx ts-node \
                --esm \
                --experimental-specifier-resolution=node \
                --loader ts-node/esm \
                src/index.ts > "../../logs/$service.log" 2>&1 &
            
            echo $! > "../../logs/$service.pid"
            cd ../..
            sleep 2
        fi
    done
    
    print_success "All services started ✓"
}

# Health check
health_check() {
    print_status "Performing health checks..."
    sleep 5
    
    services=("3000:API Gateway" "3001:Auth Service" "3002:User Service" "3003:Job Service" "3004:Resume Service")
    
    for service in "${services[@]}"; do
        port=$(echo $service | cut -d':' -f1)
        name=$(echo $service | cut -d':' -f2)
        
        if curl -f -s "http://localhost:$port/health" > /dev/null 2>&1; then
            print_success "$name is healthy ✓"
        else
            print_warning "$name health check failed (might still be starting)"
        fi
    done
}

# Show status
show_status() {
    echo ""
    echo "=========================================="
    echo "🎉 Job Application Platform is Running!"
    echo "=========================================="
    echo ""
    echo "🌐 Services:"
    echo "   API Gateway:    http://localhost:3000"
    echo "   Auth Service:   http://localhost:3001"
    echo "   User Service:   http://localhost:3002"
    echo "   Job Service:    http://localhost:3003"
    echo "   Resume Service: http://localhost:3004"
    echo ""
    echo "🔍 Health Checks:"
    echo "   curl http://localhost:3000/health"
    echo "   curl http://localhost:3001/health"
    echo ""
    echo "📊 Infrastructure:"
    echo "   MongoDB:        mongodb://localhost:27017"
    echo "   Redis:          redis://localhost:6379"
    echo ""
    echo "📝 View Logs:"
    echo "   tail -f logs/api-gateway.log"
    echo "   tail -f logs/auth-service.log"
    echo ""
    echo "🛑 Stop Services:"
    echo "   ./scripts/run-local-dev.sh stop"
    echo ""
}

# Cleanup
cleanup() {
    print_status "Stopping services..."
    
    # Kill service processes
    if [ -d "logs" ]; then
        for pidfile in logs/*.pid; do
            if [ -f "$pidfile" ]; then
                pid=$(cat "$pidfile")
                kill "$pid" 2>/dev/null || true
                rm "$pidfile"
            fi
        done
    fi
    
    # Kill any remaining ts-node processes
    pkill -f "ts-node.*src/index.ts" || true
    
    # Stop Docker containers
    docker-compose down
    
    print_success "Services stopped ✓"
}

# Main function
main() {
    local action=${1:-start}
    
    case $action in
        start)
            check_node_version
            setup_env
            start_infrastructure
            install_deps
            start_services
            health_check
            show_status
            
            echo "Press Ctrl+C to stop all services"
            trap cleanup EXIT
            
            # Keep script running
            while true; do
                sleep 10
            done
            ;;
        stop)
            cleanup
            ;;
        logs)
            service=${2:-api-gateway}
            if [ -f "logs/$service.log" ]; then
                tail -f "logs/$service.log"
            else
                echo "Available logs:"
                ls logs/*.log 2>/dev/null || echo "No log files found"
            fi
            ;;
        *)
            echo "Usage: $0 [start|stop|logs [service]]"
            echo ""
            echo "Commands:"
            echo "  start    - Start all services (default)"
            echo "  stop     - Stop all services"
            echo "  logs     - Show logs for a service"
            exit 1
            ;;
    esac
}

main "$@"