#!/bin/bash

# Proper Microservices Deployment Script
# Each service runs independently without shared libraries

set -e

echo "🚀 Starting Job Application Platform - True Microservices Architecture"

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check Node.js version
check_node_version() {
    node_version=$(node --version)
    if [[ ! "$node_version" =~ ^v22\. ]]; then
        print_warning "Expected Node.js v22.x.x, found $node_version"
        print_warning "Please switch to Node.js v22.18.0 using nvm: nvm use 22.18.0"
    else
        print_success "Node.js version: $node_version ✓"
    fi
}

# Start infrastructure
start_infrastructure() {
    print_status "Starting infrastructure (Redis)..."
    docker-compose up -d redis
    
    print_status "Waiting for infrastructure to be ready..."
    sleep 10
    
    # MongoDB Atlas is used instead of local MongoDB
    print_success "MongoDB Atlas connection configured ✓"
    
    # Health check Redis
    for i in {1..30}; do
        if docker exec job-platform-redis redis-cli ping &> /dev/null; then
            print_success "Redis is ready ✓"
            break
        fi
        if [ $i -eq 30 ]; then
            print_error "Redis failed to start"
            exit 1
        fi
        sleep 2
    done
}

# Setup environment files
setup_env() {
    if [ ! -f .env ]; then
        print_status "Creating environment files..."
        ./scripts/setup-env.sh
    else
        print_success "Environment files exist ✓"
    fi
}

# Install dependencies for each service independently
install_service_deps() {
    local service=$1
    if [ -d "services/$service" ]; then
        print_status "Installing dependencies for $service..."
        cd "services/$service"
        npm install
        cd ../..
        print_success "$service dependencies installed ✓"
    fi
}

# Start a single service
start_service() {
    local service=$1
    local port=$2
    
    if [ -d "services/$service" ]; then
        print_status "Starting $service on port $port..."
        cd "services/$service"
        
        # Create logs directory if it doesn't exist
        mkdir -p ../../logs
        
        # Start service with tsx (TypeScript runner)
        PORT=$port \
        NODE_ENV=development \
        npx tsx src/index.ts > "../../logs/$service.log" 2>&1 &
        
        local pid=$!
        echo $pid > "../../logs/$service.pid"
        cd ../..
        
        # Wait a moment and check if process is still running
        sleep 3
        if kill -0 $pid 2>/dev/null; then
            print_success "$service started successfully (PID: $pid) ✓"
        else
            print_error "$service failed to start"
            cat "logs/$service.log" | tail -20
            return 1
        fi
    else
        print_error "Service directory not found: services/$service"
        return 1
    fi
}

# Health check for a service
health_check_service() {
    local port=$1
    local name=$2
    local max_attempts=30
    
    for i in $(seq 1 $max_attempts); do
        if curl -f -s "http://localhost:$port/health" > /dev/null 2>&1; then
            print_success "$name is healthy ✓"
            return 0
        fi
        sleep 2
    done
    
    print_warning "$name health check failed (might still be starting)"
    return 1
}

# Install all dependencies
install_all_deps() {
    print_status "Installing dependencies for all services..."
    
    # Install root dependencies (mainly for scripts)
    print_status "Installing root dependencies..."
    npm install
    
    # Install dependencies for each service
    services=("api-gateway" "auth-service" "user-service" "job-service" "resume-service")
    
    for service in "${services[@]}"; do
        install_service_deps "$service"
    done
    
    print_success "All dependencies installed ✓"
}

# Start all services
start_all_services() {
    print_status "Starting all microservices..."
    
    # Define services and their ports (simple array approach)
    services=("api-gateway:3000" "auth-service:3001" "user-service:3002" "job-service:3003" "resume-service:3004")
    
    # Start services in order
    for service_port in "${services[@]}"; do
        service=$(echo $service_port | cut -d':' -f1)
        port=$(echo $service_port | cut -d':' -f2)
        start_service "$service" "$port"
    done
    
    print_success "All services started ✓"
}

# Perform health checks
perform_health_checks() {
    print_status "Performing health checks..."
    sleep 5
    
    # Define service checks (simple array approach)
    service_checks=("3000:API Gateway" "3001:Auth Service" "3002:User Service" "3003:Job Service" "3004:Resume Service")
    
    local all_healthy=true
    
    for service_check in "${service_checks[@]}"; do
        port=$(echo $service_check | cut -d':' -f1)
        name=$(echo $service_check | cut -d':' -f2-)
        if ! health_check_service "$port" "$name"; then
            all_healthy=false
        fi
    done
    
    if [ "$all_healthy" = true ]; then
        print_success "All services are healthy ✓"
    else
        print_warning "Some services may not be fully ready yet"
    fi
}

# Show status
show_status() {
    echo ""
    echo "=========================================="
    echo "🎉 Job Application Platform is Running!"
    echo "=========================================="
    echo ""
    echo "🌐 Services (True Microservices):"
    echo "   API Gateway:    http://localhost:3000"
    echo "   Auth Service:   http://localhost:3001"
    echo "   User Service:   http://localhost:3002"
    echo "   Job Service:    http://localhost:3003"
    echo "   Resume Service: http://localhost:3004"
    echo ""
    echo "🔍 Health Checks:"
    echo "   curl http://localhost:3000/health"
    echo "   curl http://localhost:3001/health"
    echo "   curl http://localhost:3002/health"
    echo ""
    echo "📊 Infrastructure:"
    echo "   MongoDB:        MongoDB Atlas (Cloud)"
    echo "   Redis:          redis://localhost:6379"
    echo ""
    echo "📝 View Logs:"
    echo "   tail -f logs/api-gateway.log"
    echo "   tail -f logs/auth-service.log"
    echo "   tail -f logs/user-service.log"
    echo ""
    echo "🛑 Stop Services:"
    echo "   ./scripts/deploy-microservices.sh stop"
    echo ""
    echo "✨ Each service is completely independent!"
    echo "   No shared libraries, true microservices architecture"
    echo ""
}

# Cleanup function
cleanup() {
    print_status "Stopping all services..."
    
    # Kill service processes
    if [ -d "logs" ]; then
        for pidfile in logs/*.pid; do
            if [ -f "$pidfile" ]; then
                pid=$(cat "$pidfile")
                if kill -0 "$pid" 2>/dev/null; then
                    kill "$pid" 2>/dev/null || true
                    print_status "Stopped process $pid"
                fi
                rm "$pidfile"
            fi
        done
    fi
    
    # Kill any remaining tsx processes
    pkill -f "tsx.*src/index.ts" || true
    
    # Stop Docker containers
    docker-compose down
    
    print_success "All services stopped ✓"
}

# Main function
main() {
    local action=${1:-start}
    
    case $action in
        start)
            check_node_version
            setup_env
            start_infrastructure
            install_all_deps
            start_all_services
            perform_health_checks
            show_status
            
            echo "Press Ctrl+C to stop all services"
            trap cleanup EXIT
            
            # Keep script running and show periodic status
            while true; do
                sleep 30
                print_status "Services running... ($(date))"
            done
            ;;
        stop)
            cleanup
            ;;
        status)
            echo "Service Status:"
            for port in 3000 3001 3002 3003 3004; do
                if curl -f -s "http://localhost:$port/health" > /dev/null 2>&1; then
                    echo "  Port $port: ✅ Healthy"
                else
                    echo "  Port $port: ❌ Down"
                fi
            done
            ;;
        logs)
            service=${2:-api-gateway}
            if [ -f "logs/$service.log" ]; then
                tail -f "logs/$service.log"
            else
                echo "Available logs:"
                ls logs/*.log 2>/dev/null || echo "No log files found"
            fi
            ;;
        *)
            echo "Usage: $0 [start|stop|status|logs [service]]"
            echo ""
            echo "Commands:"
            echo "  start    - Start all services (default)"
            echo "  stop     - Stop all services"
            echo "  status   - Check service status"
            echo "  logs     - Show logs for a service"
            echo ""
            echo "Available services: api-gateway, auth-service, user-service, job-service, resume-service"
            exit 1
            ;;
    esac
}

main "$@"