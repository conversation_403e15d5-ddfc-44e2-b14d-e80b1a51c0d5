import { apiService } from './api';
import { 
  DashboardStats, 
  RecentActivity, 
  Application, 
  Resume,
  UserAnalytics 
} from '@/types/api';

class DashboardService {
  // Dashboard statistics
  async getDashboardStats(): Promise<DashboardStats> {
    try {
      // Get application stats
      const applicationStats = await apiService.get('/applications/stats') as any;
      
      // Get resume stats
      const resumeStats = await apiService.get('/resumes/stats') as any;
      
      // Get user analytics
      const userAnalytics = await apiService.get('/users/analytics') as any;
      
      // Combine all stats
      return {
        totalApplications: applicationStats.totalApplications || 0,
        pendingApplications: applicationStats.pendingApplications || 0,
        interviewsScheduled: applicationStats.interviewsScheduled || 0,
        offersReceived: applicationStats.offersReceived || 0,
        resumesCreated: resumeStats.totalResumes || 0,
        profileViews: userAnalytics.profileViews || 0,
        jobsSaved: applicationStats.jobsSaved || 0,
        applicationsThisWeek: applicationStats.applicationsThisWeek || 0,
        applicationsThisMonth: applicationStats.applicationsThisMonth || 0,
        averageResponseTime: applicationStats.averageResponseTime || 0,
        successRate: applicationStats.successRate || 0,
      };
    } catch (error: any) {
      console.error('Error fetching dashboard stats:', error);
      
      // If user is not authenticated, return default stats
      if (error?.response?.status === 401) {
        console.log('User not authenticated, returning default stats');
      }
      
      // Return default stats if API fails
      return {
        totalApplications: 0,
        pendingApplications: 0,
        interviewsScheduled: 0,
        offersReceived: 0,
        resumesCreated: 0,
        profileViews: 0,
        jobsSaved: 0,
        applicationsThisWeek: 0,
        applicationsThisMonth: 0,
        averageResponseTime: 0,
        successRate: 0,
      };
    }
  }

  // Recent applications
  async getRecentApplications(limit: number = 5): Promise<Application[]> {
    try {
        const response = await apiService.get('/applications', {
          params: { limit, sort: '-appliedAt' }
        }) as any;
        return response.data || [];
    } catch (error: any) {
      console.error('Error fetching recent applications:', error);
      
      // If user is not authenticated, return empty array
      if (error?.response?.status === 401) {
        console.log('User not authenticated, returning empty applications');
      }
      
      return [];
    }
  }

  // Recent resumes
  async getRecentResumes(limit: number = 5): Promise<Resume[]> {
    try {
        const response = await apiService.get('/resumes', {
          params: { limit, sort: '-createdAt' }
        }) as any;
        return response.data || [];
    } catch (error: any) {
      console.error('Error fetching recent resumes:', error);
      
      // If user is not authenticated, return empty array
      if (error?.response?.status === 401) {
        console.log('User not authenticated, returning empty resumes');
      }
      
      return [];
    }
  }

  // Recent activity (combines applications and resumes)
  async getRecentActivity(limit: number = 10): Promise<RecentActivity[]> {
    try {
      const [applications, resumes] = await Promise.all([
        this.getRecentApplications(limit),
        this.getRecentResumes(limit)
      ]);

      const activities: RecentActivity[] = [];

      // Add application activities
      applications.forEach(app => {
        activities.push({
          id: `app-${app.id}`,
          type: 'application',
          title: `Applied to Job #${app.jobId}`,
          description: `Status: ${app.status}`,
          timestamp: app.appliedAt,
          status: app.status,
        });
      });

      // Add resume activities
      resumes.forEach(resume => {
        activities.push({
          id: `resume-${resume.id}`,
          type: 'resume',
          title: `Created ${resume.title}`,
          description: `Status: ready`,
          timestamp: resume.createdAt,
          status: 'ready',
        });
      });

      // Sort by timestamp and return limited results
      return activities
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
        .slice(0, limit);
    } catch (error) {
      console.error('Error fetching recent activity:', error);
      return [];
    }
  }

  // Application analytics
  async getApplicationAnalytics(): Promise<UserAnalytics> {
    try {
      return await apiService.get('/applications/analytics');
    } catch (error) {
      console.error('Error fetching application analytics:', error);
      return {
        profileViews: 0,
        searchAppearances: 0,
        applicationsSent: 0,
        interviewsScheduled: 0,
        offersReceived: 0,
        loginStreak: 0,
        totalLogins: 0,
        averageSessionDuration: 0,
        responseRate: 0,
        interviewRate: 0,
        offerRate: 0,
      };
    }
  }

  // Resume analytics
  async getResumeAnalytics(): Promise<{
    totalResumes: number;
    resumesByStatus: Record<string, number>;
    averageScore: number;
    topSkills: string[];
  }> {
    try {
      return await apiService.get('/resumes/analytics');
    } catch (error) {
      console.error('Error fetching resume analytics:', error);
      return {
        totalResumes: 0,
        resumesByStatus: {},
        averageScore: 0,
        topSkills: [],
      };
    }
  }

  // Quick stats for dashboard cards
  async getQuickStats(): Promise<{
    totalApplications: number;
    activeApplications: number;
    interviews: number;
    responses: number;
  }> {
    try {
      const stats = await this.getDashboardStats();
      return {
        totalApplications: stats.totalApplications,
        activeApplications: stats.pendingApplications,
        interviews: stats.interviewsScheduled,
        responses: stats.offersReceived,
      };
    } catch (error) {
      console.error('Error fetching quick stats:', error);
      return {
        totalApplications: 0,
        activeApplications: 0,
        interviews: 0,
        responses: 0,
      };
    }
  }
}

export const dashboardService = new DashboardService();
