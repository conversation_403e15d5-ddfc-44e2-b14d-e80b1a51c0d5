import { Router, Request, Response } from 'express';
import { ResponseUtil } from '../utils/response';
import { Resume } from '../models/resume.model';
import { logger } from '../utils/logger';
import { authenticateToken } from '../middleware/auth.middleware';
import { uploadSingle, handleUploadError } from '../middleware/upload.middleware';
import { validateUploadResume, validateUpdateResume, validateAnalyzeResume, validateMongoId, sanitizeInput, rateLimitUploads } from '../middleware/validation.middleware';
import { uploadRateLimit } from '../middleware/security.middleware';
import { S3Service } from '../services/s3.service';
import { ResumeParserService } from '../services/resume-parser.service';
import { ResumeAnalysisService } from '../services/resume-analysis.service';

const router = Router();

// Get all resumes
router.get('/', authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 10, isDefault } = req.query;
    const skip = (Number(page) - 1) * Number(limit);
    
    // Filter by authenticated user
    const filter: any = { userId: req.user!.id };
    if (isDefault !== undefined) filter.isDefault = isDefault === 'true';
    
    const resumes = await Resume.find(filter)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(Number(limit));
    
    const total = await Resume.countDocuments(filter);
    
    const response = ResponseUtil.success(
      {
        resumes,
        total,
        page: Number(page),
        limit: Number(limit),
        totalPages: Math.ceil(total / Number(limit)),
      },
      'Resumes retrieved successfully'
    );

    res.status(response.statusCode).json(response);
  } catch (error) {
    logger.error('Error retrieving resumes:', error);
    const response = ResponseUtil.error('Failed to retrieve resumes', 500);
    res.status(response.statusCode).json(response);
  }
});

// Upload resume
router.post('/upload', authenticateToken, uploadRateLimit, uploadSingle, handleUploadError, sanitizeInput, validateUploadResume, async (req: Request, res: Response): Promise<void> => {
  try {
    if (!req.file) {
      const response = ResponseUtil.error('No file provided', 400);
      res.status(response.statusCode).json(response);
      return;
    }

    const file = req.file;
    const { name, isDefault } = req.body;
    
    // Generate unique filename
    const timestamp = Date.now();
    const fileExtension = file.originalname.substring(file.originalname.lastIndexOf('.'));
    const fileName = `${req.user!.id}_${timestamp}${fileExtension}`;
    const s3Key = `resumes/${req.user!.id}/${fileName}`;
    
    logger.info('Starting resume upload process:', {
      userId: req.user!.id,
      fileName: file.originalname,
      fileSize: file.size,
      fileType: file.mimetype
    });

    // Initialize services
    const s3Service = new S3Service();
    const parserService = new ResumeParserService();
    const analysisService = new ResumeAnalysisService();

    // Upload file to S3
    let fileUrl: string;
    try {
      await s3Service.initialize();
      fileUrl = await s3Service.uploadFile(file.buffer, s3Key, file.mimetype);
      logger.info('File uploaded to S3 successfully:', { fileUrl });
    } catch (s3Error) {
      logger.error('S3 upload failed:', s3Error);
      // Continue without S3 for now
      fileUrl = `/uploads/${fileName}`;
    }

    // Parse resume content
    let parsedContent: any = {
      personalInfo: {},
      experience: [],
      education: [],
      skills: [],
      certifications: [],
      languages: [],
      projects: []
    };
    try {
      if (file.mimetype === 'application/pdf') {
        parsedContent = await parserService.parsePDF(file.buffer);
      } else {
        parsedContent = await parserService.parseDocument(file.buffer, file.mimetype);
      }
      logger.info('Resume content parsed successfully');
    } catch (parseError) {
      logger.warn('Resume parsing failed, continuing with empty content:', parseError);
    }

    // Analyze resume
    let analysis = null;
    try {
      analysis = await analysisService.analyzeResume(parsedContent);
      logger.info('Resume analysis completed:', {
        overallScore: analysis.overallScore,
        atsScore: analysis.atsScore
      });
    } catch (analysisError) {
      logger.warn('Resume analysis failed, continuing without analysis:', analysisError);
    }

    // Create resume record
    const resumeData = {
      userId: req.user!.id,
      title: name || file.originalname || 'Untitled Resume',
      fileName: fileName,
      filePath: fileUrl,
      fileSize: file.size,
      fileType: file.mimetype,
      content: parsedContent,
      analysis: analysis,
      isDefault: isDefault === 'true' || false,
      tags: [],
      createdAt: new Date(),
    };
    
    const resume = new Resume(resumeData);
    await resume.save();
    
    logger.info('Resume uploaded and processed successfully:', {
      resumeId: resume._id,
      userId: req.user!.id,
      fileName: resume.fileName,
      fileSize: resume.fileSize,
      fileType: resume.fileType,
      hasContent: Object.keys(parsedContent).length > 0,
      hasAnalysis: analysis !== null
    });
    
    const response = ResponseUtil.created(resume, 'Resume uploaded and processed successfully');
    res.status(response.statusCode).json(response);
  } catch (error) {
    logger.error('Error uploading resume:', error);
    const response = ResponseUtil.error('Failed to upload resume', 500);
    res.status(response.statusCode).json(response);
  }
});

// Get resume statistics
router.get('/stats', authenticateToken, async (req, res) => {
  try {
    logger.info('Resume stats endpoint called', {
      query: req.query,
      headers: req.headers,
      url: req.url,
      userId: req.user!.id
    });

    // Filter by authenticated user
    const filter: any = { userId: req.user!.id };

    logger.info('Querying resume statistics', { filter });

    const totalResumes = await Resume.countDocuments(filter);
    logger.info('Total resumes count', { totalResumes });

    // Get resumes with analysis data
    const resumesWithAnalysis = await Resume.find({
      ...filter,
      'analysis.overallScore': { $exists: true }
    });
    logger.info('Resumes with analysis', { count: resumesWithAnalysis.length });

    const averageScore = resumesWithAnalysis.length > 0
      ? resumesWithAnalysis.reduce((sum, resume) => sum + (resume.analysis?.overallScore || 0), 0) / resumesWithAnalysis.length
      : 0;

    // Extract top skills from all resumes
    const allSkills = resumesWithAnalysis.flatMap(resume => resume.content?.skills || []);
    const skillCounts = allSkills.reduce((acc, skill) => {
      acc[skill] = (acc[skill] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const topSkills = Object.entries(skillCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([skill]) => skill);

    const statsData = {
      totalResumes,
      resumesByStatus: {
        ready: totalResumes, // All resumes are considered "ready" in our model
        processing: 0,
        error: 0,
      },
      averageScore: Math.round(averageScore * 100) / 100,
      topSkills,
    };

    logger.info('Resume statistics calculated successfully', { statsData });

    const response = ResponseUtil.success(
      statsData,
      'Resume statistics retrieved successfully'
    );

    res.status(response.statusCode).json(response);
  } catch (error) {
    logger.error('Error retrieving resume statistics:', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      query: req.query,
      url: req.url
    });

    const response = ResponseUtil.error('Failed to retrieve resume statistics', 500);
    res.status(response.statusCode).json(response);
  }
});

// Get resume by ID
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    
    const resume = await Resume.findOne({ _id: id, userId: req.user!.id });
    if (!resume) {
      const response = ResponseUtil.error('Resume not found', 404);
      return res.status(response.statusCode).json(response);
    }
    
    // Increment view count
    await Resume.findByIdAndUpdate(id, { $inc: { viewCount: 1 } });
    
    const response = ResponseUtil.success(resume, 'Resume retrieved successfully');
    return res.status(response.statusCode).json(response);
  } catch (error) {
    logger.error('Error retrieving resume by ID:', error);
    const response = ResponseUtil.error('Failed to retrieve resume', 500);
    return res.status(response.statusCode).json(response);
  }
});



// Update resume
router.put('/:id', authenticateToken, sanitizeInput, validateUpdateResume, async (req, res) => {
  try {
    const { id } = req.params;
    const updates = req.body;
    
    const resume = await Resume.findOneAndUpdate(
      { _id: id, userId: req.user!.id },
      { ...updates, updatedAt: new Date() },
      { new: true, runValidators: true }
    );
    
    if (!resume) {
      const response = ResponseUtil.error('Resume not found', 404);
      return res.status(response.statusCode).json(response);
    }
    
    const response = ResponseUtil.success(resume, 'Resume updated successfully');
    return res.status(response.statusCode).json(response);
  } catch (error) {
    logger.error('Error updating resume:', error);
    const response = ResponseUtil.error('Failed to update resume', 500);
    return res.status(response.statusCode).json(response);
  }
});

// Delete resume
router.delete('/:id', authenticateToken, validateMongoId, async (req, res) => {
  try {
    const { id } = req.params;
    
    const resume = await Resume.findOneAndDelete({ _id: id, userId: req.user!.id });
    if (!resume) {
      const response = ResponseUtil.error('Resume not found', 404);
      return res.status(response.statusCode).json(response);
    }
    
    const response = ResponseUtil.success(null, 'Resume deleted successfully');
    return res.status(response.statusCode).json(response);
  } catch (error) {
    logger.error('Error deleting resume:', error);
    const response = ResponseUtil.error('Failed to delete resume', 500);
    return res.status(response.statusCode).json(response);
  }
});

// Set default resume
router.post('/:id/set-default', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    
    // First, unset all other resumes as default for this user
    await Resume.updateMany(
      { userId: req.user!.id },
      { isDefault: false }
    );
    
    // Set the specified resume as default
    const resume = await Resume.findOneAndUpdate(
      { _id: id, userId: req.user!.id },
      { isDefault: true, updatedAt: new Date() },
      { new: true }
    );
    
    if (!resume) {
      const response = ResponseUtil.error('Resume not found', 404);
      return res.status(response.statusCode).json(response);
    }
    
    const response = ResponseUtil.success(resume, 'Resume set as default successfully');
    return res.status(response.statusCode).json(response);
  } catch (error) {
    logger.error('Error setting default resume:', error);
    const response = ResponseUtil.error('Failed to set default resume', 500);
    return res.status(response.statusCode).json(response);
  }
});

// Download resume
router.get('/:id/download', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    
    const resume = await Resume.findOne({ _id: id, userId: req.user!.id });
    if (!resume) {
      const response = ResponseUtil.error('Resume not found', 404);
      return res.status(response.statusCode).json(response);
    }

    // Generate signed download URL
    const s3Service = new S3Service();
    try {
      await s3Service.initialize();
      const s3Key = resume.filePath.replace(/^https:\/\/[^\/]+\//, '');
      const downloadUrl = await s3Service.getSignedDownloadUrl(s3Key, 3600); // 1 hour expiry
      
      logger.info('Generated download URL for resume:', {
        resumeId: id,
        userId: req.user!.id
      });
      
      const response = ResponseUtil.success(
        { downloadUrl },
        'Download URL generated successfully'
      );
      return res.status(response.statusCode).json(response);
    } catch (s3Error) {
      logger.error('Failed to generate download URL:', s3Error);
      const response = ResponseUtil.error('Failed to generate download URL', 500);
      return res.status(response.statusCode).json(response);
    }
  } catch (error) {
    logger.error('Error generating download URL:', error);
    const response = ResponseUtil.error('Failed to generate download URL', 500);
    return res.status(response.statusCode).json(response);
  }
});

// Analyze resume
router.post('/:id/analyze', authenticateToken, sanitizeInput, validateAnalyzeResume, async (req, res) => {
  try {
    const { id } = req.params;
    const { jobDescription } = req.body;
    
    const resume = await Resume.findOne({ _id: id, userId: req.user!.id });
    if (!resume) {
      const response = ResponseUtil.error('Resume not found', 404);
      return res.status(response.statusCode).json(response);
    }

    if (!resume.content || Object.keys(resume.content).length === 0) {
      const response = ResponseUtil.error('Resume content not available for analysis', 400);
      return res.status(response.statusCode).json(response);
    }

    const analysisService = new ResumeAnalysisService();
    const analysis = await analysisService.analyzeResume(resume.content as any, jobDescription);

    // Update resume with new analysis
    await Resume.findByIdAndUpdate(id, { 
      analysis,
      updatedAt: new Date()
    });

    logger.info('Resume analysis completed:', {
      resumeId: id,
      userId: req.user!.id,
      overallScore: analysis.overallScore
    });

    const response = ResponseUtil.success(analysis, 'Resume analysis completed successfully');
    return res.status(response.statusCode).json(response);
  } catch (error) {
    logger.error('Error analyzing resume:', error);
    const response = ResponseUtil.error('Failed to analyze resume', 500);
    return res.status(response.statusCode).json(response);
  }
});

// Get resume analytics
router.get('/analytics', authenticateToken, async (req, res) => {
  try {
    const userId = req.user!.id;
    
    const filter = { userId };
    
    const totalResumes = await Resume.countDocuments(filter);
    
    // Get resumes with analysis data
    const resumesWithAnalysis = await Resume.find({ 
      ...filter, 
      'analysis.overallScore': { $exists: true } 
    });
    
    const averageScore = resumesWithAnalysis.length > 0 
      ? resumesWithAnalysis.reduce((sum, resume) => sum + (resume.analysis?.overallScore || 0), 0) / resumesWithAnalysis.length
      : 0;
    
    // Extract top skills from all resumes
    const allSkills = resumesWithAnalysis.flatMap(resume => resume.content?.skills || []);
    const skillCounts = allSkills.reduce((acc, skill) => {
      acc[skill] = (acc[skill] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    const topSkills = Object.entries(skillCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([skill]) => skill);
    
    const response = ResponseUtil.success(
      {
        totalResumes,
        resumesByStatus: {
          ready: totalResumes,
          processing: 0,
          error: 0,
        },
        averageScore: Math.round(averageScore * 100) / 100,
        topSkills,
      },
      'Resume analytics retrieved successfully'
    );

    return res.status(response.statusCode).json(response);
  } catch (error) {
    logger.error('Error retrieving resume analytics:', error);
    const response = ResponseUtil.error('Failed to retrieve resume analytics', 500);
    return res.status(response.statusCode).json(response);
  }
});

export { router as resumeRoutes };
