import { useAuth } from '@/contexts/AuthContext';

/**
 * Hook to check if the current user is a ROOT Admin
 * ROOT Admin has access to all system controls and can manage all services
 */
export const useRootAdmin = () => {
  const { user, isAuthenticated } = useAuth();
  
  const isRootAdmin = isAuthenticated && user?.role === 'root_admin';
  
  return {
    isRootAdmin,
    user,
    isAuthenticated
  };
};
