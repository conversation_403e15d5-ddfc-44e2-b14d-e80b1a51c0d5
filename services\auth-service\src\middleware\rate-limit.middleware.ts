import rateLimit from 'express-rate-limit';
import { ResponseUtil } from '../utils/response';

export const rateLimitMiddleware = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limit each IP to 100 requests per windowMs
  message: ResponseUtil.error(
    'Too many requests from this IP, please try again later',
    429,
    ['Rate limit exceeded']
  ),
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => {
    res
      .status(429)
      .json(
        ResponseUtil.error(
          'Too many requests from this IP, please try again later',
          429,
          ['Rate limit exceeded'],
          req.path
        )
      );
  },
});

export const authRateLimitMiddleware = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // Limit each IP to 10 auth requests per windowMs
  message: ResponseUtil.error(
    'Too many authentication attempts, please try again later',
    429,
    ['Auth rate limit exceeded']
  ),
  standardHeaders: true,
  legacyHeaders: false,
  skipSuccessfulRequests: true,
  handler: (req, res) => {
    res
      .status(429)
      .json(
        ResponseUtil.error(
          'Too many authentication attempts, please try again later',
          429,
          ['Auth rate limit exceeded'],
          req.path
        )
      );
  },
});
