import { v2 as cloudinary } from 'cloudinary';
import { z } from 'zod';
import dotenv from 'dotenv';
import { logger } from '../utils/logger';

dotenv.config();

const cloudinarySchema = z.object({
  CLOUDINARY_CLOUD_NAME: z.string().min(1).optional(),
  CLOUDINARY_API_KEY: z.string().min(1).optional(),
  CLOUDINARY_API_SECRET: z.string().min(1).optional(),
});

let cloudinaryConfig: any = {};
let isCloudinaryConfigured = false;

try {
  cloudinaryConfig = cloudinarySchema.parse(process.env);
  isCloudinaryConfigured = !!(
    cloudinaryConfig.CLOUDINARY_CLOUD_NAME &&
    cloudinaryConfig.CLOUDINARY_API_KEY &&
    cloudinaryConfig.CLOUDINARY_API_SECRET
  );
} catch (error) {
  logger.warn('Cloudinary configuration not found. Image uploads will be disabled.');
  isCloudinaryConfigured = false;
}

// Configure Cloudinary only if all credentials are available
if (isCloudinaryConfigured) {
  cloudinary.config({
    cloud_name: cloudinaryConfig.CLOUDINARY_CLOUD_NAME,
    api_key: cloudinaryConfig.CLOUDINARY_API_KEY,
    api_secret: cloudinaryConfig.CLOUDINARY_API_SECRET,
    secure: true,
  });
  logger.info('Cloudinary configured successfully');
} else {
  logger.warn('Cloudinary not configured. Image upload functionality will be disabled.');
}

export { cloudinary, isCloudinaryConfigured };
