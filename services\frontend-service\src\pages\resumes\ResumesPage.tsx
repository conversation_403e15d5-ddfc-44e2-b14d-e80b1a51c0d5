import React, { useState, useEffect, useRef } from 'react';
import { FileText, Download, Edit, Trash2, Star, Plus, Loader2, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { FileUpload } from '@/components/FileUpload';
import { resumeService } from '@/services/resume.service';
import { Resume } from '@/types/api';

export const ResumesPage: React.FC = () => {
  const [resumes, setResumes] = useState<Resume[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [uploading, setUploading] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    const fetchResumes = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await resumeService.getResumes();
        setResumes(response.resumes || []);
      } catch (err) {
        console.error('Error fetching resumes:', err);
        setError('Failed to load resumes. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchResumes();
  }, []);

  const handleRefresh = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await resumeService.getResumes();
      setResumes(response.resumes || []);
    } catch (err) {
      console.error('Error refreshing resumes:', err);
      setError('Failed to refresh resumes. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = async (resumeId: string) => {
    try {
      const { downloadUrl } = await resumeService.downloadResume(resumeId);
      window.open(downloadUrl, '_blank');
    } catch (err) {
      console.error('Error downloading resume:', err);
      setError('Failed to download resume. Please try again.');
    }
  };

  const handleAnalyze = async (resumeId: string) => {
    try {
      setLoading(true);
      await resumeService.analyzeResume(resumeId);
      // Refresh the resume list to show updated analysis
      await handleRefresh();
    } catch (err) {
      console.error('Error analyzing resume:', err);
      setError('Failed to analyze resume. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (resumeId: string) => {
    if (!confirm('Are you sure you want to delete this resume?')) {
      return;
    }
    
    try {
      setLoading(true);
      await resumeService.deleteResume(resumeId);
      // Refresh the resume list
      await handleRefresh();
    } catch (err) {
      console.error('Error deleting resume:', err);
      setError('Failed to delete resume. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleFileUpload = async (file: File, options: { name?: string; isDefault?: boolean }) => {
    try {
      setUploading(true);
      setUploadError(null);
      
      await resumeService.uploadResume(file, options);
      
      // Refresh the resume list
      await handleRefresh();
    } catch (err) {
      console.error('Error uploading resume:', err);
      setUploadError('Failed to upload resume. Please try again.');
    } finally {
      setUploading(false);
    }
  };

  const handleEmptyStateUpload = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length > 0) {
      handleFileUpload(files[0], {});
    }
  };

  const handleViewAnalysis = async (resumeId: string) => {
    try {
      setLoading(true);
      const analysis = await resumeService.getATSCompatibility(resumeId);
      // For now, show analysis in an alert - this could be improved with a modal
      alert(`ATS Compatibility Score: ${analysis.score}%\n\nIssues found: ${analysis.issues.length}\nRecommendations: ${analysis.recommendations.join(', ')}`);
    } catch (err) {
      console.error('Error getting analysis:', err);
      setError('Failed to load analysis. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleOptimizeResume = async (resumeId: string) => {
    try {
      setLoading(true);
      const optimization = await resumeService.optimizeResume(resumeId);
      // For now, show optimization suggestions in an alert - this could be improved with a modal
      const suggestions = optimization.suggestions.map(s => `${s.type}: ${s.message}`).join('\n');
      alert(`Optimization suggestions:\n\n${suggestions}`);
    } catch (err) {
      console.error('Error optimizing resume:', err);
      setError('Failed to optimize resume. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleSetAsDefault = async (resumeId: string) => {
    try {
      setLoading(true);
      await resumeService.updateResume(resumeId, { isDefault: true });
      await handleRefresh();
    } catch (err) {
      console.error('Error setting resume as default:', err);
      setError('Failed to set resume as default. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ready':
        return 'status-active';
      case 'processing':
        return 'status-pending';
      case 'error':
        return 'status-error';
      default:
        return 'status-inactive';
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-success-600';
    if (score >= 60) return 'text-warning-600';
    return 'text-error-600';
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-64">
          <div className="flex items-center space-x-2">
            <Loader2 className="w-6 h-6 animate-spin" />
            <span>Loading resumes...</span>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="text-red-500 mr-2">⚠️</div>
              <span className="text-red-700">{error}</span>
            </div>
            <Button onClick={handleRefresh} variant="outline" size="sm">
              <RefreshCw className="w-4 h-4 mr-2" />
              Retry
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-8">
        <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4">
          <div className="min-w-0 flex-1">
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">My Resumes</h1>
            <p className="text-gray-600 mt-2">
              Upload, manage, and optimize your resumes for better job matches
            </p>
          </div>
          <div className="flex-shrink-0">
            <Button onClick={handleRefresh} variant="outline" size="sm" className="w-full sm:w-auto">
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh
            </Button>
          </div>
        </div>
      </div>

      {/* Upload section */}
      <div className="card mb-6">
        <div className="card-body">
          <FileUpload
            onFileSelect={handleFileUpload}
            loading={uploading}
            error={uploadError}
          />
        </div>
      </div>

      {/* Resume list */}
      <div className="space-y-4">
        {resumes.length > 0 ? (
          resumes.map((resume) => (
          <div key={resume.id} className="card">
            <div className="card-body">
              <div className="flex flex-col sm:flex-row sm:items-center gap-4">
                <div className="flex items-center space-x-4 flex-1 min-w-0">
                  <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0">
                    <FileText className="w-5 h-5 sm:w-6 sm:h-6 text-gray-600" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="text-base sm:text-lg font-semibold text-gray-900 truncate">
                        {resume.title}
                      </h3>
                      {resume.isDefault && (
                        <Star className="w-4 h-4 text-warning-500 fill-current flex-shrink-0" />
                      )}
                      <span className={`badge ${getStatusColor('ready')}`}>
                        ready
                      </span>
                    </div>
                    <div className="flex items-center gap-4 text-sm text-gray-500">
                      <span>Created {new Date(resume.createdAt).toLocaleDateString()}</span>
                      <span>{Math.round(resume.fileSize / 1024)} KB</span>
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-6">
                  {/* Analysis scores */}
                  {resume.analysis && (
                    <div className="flex space-x-4 text-sm">
                      <div className="text-center">
                        <div className={`font-semibold ${getScoreColor(resume.analysis.overallScore)}`}>
                          {resume.analysis.overallScore}%
                        </div>
                        <div className="text-gray-500">Overall</div>
                      </div>
                      <div className="text-center">
                        <div className={`font-semibold ${getScoreColor(resume.analysis.atsScore)}`}>
                          {resume.analysis.atsScore}%
                        </div>
                        <div className="text-gray-500">ATS</div>
                      </div>
                      <div className="text-center">
                        <div className={`font-semibold ${getScoreColor(resume.analysis.readabilityScore)}`}>
                          {resume.analysis.readabilityScore}%
                        </div>
                        <div className="text-gray-500">Readability</div>
                      </div>
                    </div>
                  )}

                </div>

                {/* Actions */}
                <div className="flex items-center space-x-1 sm:space-x-2 flex-shrink-0">
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    className="p-2"
                    onClick={() => handleDownload(resume.id)}
                    title="Download resume"
                  >
                    <Download className="w-4 h-4" />
                  </Button>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    className="p-2"
                    onClick={() => handleAnalyze(resume.id)}
                    title="Analyze resume"
                  >
                    <Edit className="w-4 h-4" />
                  </Button>
                  {!resume.isDefault && (
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="p-2"
                      onClick={() => handleDelete(resume.id)}
                      title="Delete resume"
                    >
                      <Trash2 className="w-4 h-4 text-error-600" />
                    </Button>
                  )}
                </div>
              </div>

              {/* Analysis details */}
              {resume.analysis && (
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-gray-600">
                      Last analyzed {new Date(resume.createdAt).toLocaleDateString()}
                    </div>
                    <div className="flex gap-2">
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => handleViewAnalysis(resume.id)}
                      >
                        View Analysis
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => handleOptimizeResume(resume.id)}
                      >
                        Optimize Resume
                      </Button>
                      {!resume.isDefault && (
                        <Button 
                          size="sm"
                          onClick={() => handleSetAsDefault(resume.id)}
                        >
                          Set as Default
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        ))
        ) : (
          <div className="card">
            <div className="card-body text-center py-12">
              <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <FileText className="w-8 h-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No resumes found</h3>
              <p className="text-gray-500 mb-4">Upload your first resume to get started!</p>
              <Button onClick={handleEmptyStateUpload} disabled={uploading}>
                <Plus className="w-4 h-4 mr-2" />
                {uploading ? 'Uploading...' : 'Upload Resume'}
              </Button>
              <input
                ref={fileInputRef}
                type="file"
                accept="application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                onChange={handleFileInputChange}
                className="hidden"
              />
            </div>
          </div>
        )}
      </div>

      {/* Tips section */}
      <div className="card mt-8">
        <div className="card-header">
          <h2 className="text-lg font-semibold text-gray-900">Resume Tips</h2>
        </div>
        <div className="card-body">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-medium text-gray-900 mb-2">Optimize for ATS</h3>
              <p className="text-sm text-gray-600">
                Use standard section headings and avoid complex formatting to ensure 
                your resume passes through Applicant Tracking Systems.
              </p>
            </div>
            <div>
              <h3 className="font-medium text-gray-900 mb-2">Tailor for Each Job</h3>
              <p className="text-sm text-gray-600">
                Customize your resume for each application by highlighting relevant 
                skills and experiences that match the job description.
              </p>
            </div>
            <div>
              <h3 className="font-medium text-gray-900 mb-2">Use Action Verbs</h3>
              <p className="text-sm text-gray-600">
                Start bullet points with strong action verbs like "developed," 
                "managed," or "implemented" to make your achievements stand out.
              </p>
            </div>
            <div>
              <h3 className="font-medium text-gray-900 mb-2">Quantify Results</h3>
              <p className="text-sm text-gray-600">
                Include specific numbers and metrics to demonstrate the impact 
                of your work and make your accomplishments more compelling.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
