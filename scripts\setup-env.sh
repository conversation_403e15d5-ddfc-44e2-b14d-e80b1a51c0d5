#!/bin/bash

# Job Application Platform - Environment Setup Script
# This script creates .env files for all services

set -e

echo "🔧 Setting up environment files for all services..."

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# Create main .env file
print_status "Creating main .env file..."
cat > .env << 'EOF'
# Job Application Platform - Main Environment Configuration

# ================================
# APPLICATION CONFIGURATION
# ================================
NODE_ENV=development
API_VERSION=1.0.0
PORT=3000

# ================================
# DATABASE CONFIGURATION
# ================================
MONGODB_URI=*****************************************************************************

# ================================
# REDIS CONFIGURATION
# ================================
REDIS_URL=redis://:jobplatform2024@localhost:6379

# ================================
# JWT CONFIGURATION
# ================================
# IMPORTANT: Change these in production!
JWT_SECRET=job-platform-super-secret-jwt-key-for-development-change-in-production-must-be-32-chars-minimum
JWT_REFRESH_SECRET=job-platform-super-secret-refresh-key-for-development-change-in-production-must-be-32-chars
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# ================================
# GOOGLE OAUTH CONFIGURATION
# ================================
# Get these from Google Cloud Console
GOOGLE_CLIENT_ID=your-google-client-id-from-console
GOOGLE_CLIENT_SECRET=your-google-client-secret-from-console
GOOGLE_CALLBACK_URL=http://localhost:3000/api/v1/auth/google/callback

# ================================
# AWS CONFIGURATION
# ================================
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-aws-access-key-id
AWS_SECRET_ACCESS_KEY=your-aws-secret-access-key
AWS_S3_BUCKET=job-platform-files-dev

# ================================
# EMAIL CONFIGURATION (SendGrid)
# ================================
SENDGRID_API_KEY=SG.your-sendgrid-api-key-here
FROM_EMAIL=<EMAIL>

# ================================
# STRIPE CONFIGURATION
# ================================
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key-here
STRIPE_WEBHOOK_SECRET=whsec_your-webhook-secret-here

# ================================
# EXTERNAL API CONFIGURATION
# ================================
# LinkedIn API
LINKEDIN_CLIENT_ID=your-linkedin-client-id
LINKEDIN_CLIENT_SECRET=your-linkedin-client-secret

# Indeed API
INDEED_API_KEY=your-indeed-api-key

# Glassdoor API
GLASSDOOR_API_KEY=your-glassdoor-api-key

# ================================
# RATE LIMITING CONFIGURATION
# ================================
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
PREMIUM_RATE_LIMIT_MAX_REQUESTS=1000

# ================================
# FILE UPLOAD CONFIGURATION
# ================================
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=pdf,doc,docx

# ================================
# SECURITY CONFIGURATION
# ================================
BCRYPT_ROUNDS=12
SESSION_SECRET=job-platform-session-secret-for-development-change-in-production-must-be-32-chars-minimum
CORS_ORIGIN=http://localhost:3000,http://localhost:3001,http://localhost:8080

# ================================
# LOGGING CONFIGURATION
# ================================
LOG_LEVEL=debug

# ================================
# HEALTH CHECK CONFIGURATION
# ================================
HEALTH_CHECK_INTERVAL=30000

# ================================
# SERVICE URLS (for development)
# ================================
AUTH_SERVICE_URL=http://localhost:3001
USER_SERVICE_URL=http://localhost:3002
JOB_SERVICE_URL=http://localhost:3003
RESUME_SERVICE_URL=http://localhost:3004
ANALYTICS_SERVICE_URL=http://localhost:3005
NOTIFICATION_SERVICE_URL=http://localhost:3006
INTEGRATION_SERVICE_URL=http://localhost:3007
PAYMENT_SERVICE_URL=http://localhost:3008

# ================================
# FRONTEND CONFIGURATION
# ================================
FRONTEND_URL=http://localhost:3000
ADMIN_EMAIL=<EMAIL>

# ================================
# DEVELOPMENT FLAGS
# ================================
ENABLE_SWAGGER=true
ENABLE_DEBUG_LOGS=true
MOCK_EXTERNAL_SERVICES=true
EOF

print_success "Main .env file created"

# API Gateway .env
print_status "Creating API Gateway .env file..."
cat > services/api-gateway/.env << 'EOF'
# API Gateway Service Environment Configuration

NODE_ENV=development
PORT=3000
API_VERSION=1.0.0

# Database
MONGODB_URI=*****************************************************************************
REDIS_URL=redis://:jobplatform2024@localhost:6379

# JWT Configuration
JWT_SECRET=job-platform-super-secret-jwt-key-for-development-change-in-production-must-be-32-chars-minimum
JWT_REFRESH_SECRET=job-platform-super-secret-refresh-key-for-development-change-in-production-must-be-32-chars

# Service URLs
AUTH_SERVICE_URL=http://localhost:3001
USER_SERVICE_URL=http://localhost:3002
JOB_SERVICE_URL=http://localhost:3003
RESUME_SERVICE_URL=http://localhost:3004
ANALYTICS_SERVICE_URL=http://localhost:3005
NOTIFICATION_SERVICE_URL=http://localhost:3006
INTEGRATION_SERVICE_URL=http://localhost:3007
PAYMENT_SERVICE_URL=http://localhost:3008

# Security
CORS_ORIGIN=http://localhost:3000,http://localhost:8080
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Health Checks
HEALTH_CHECK_INTERVAL=30000

# Logging
LOG_LEVEL=debug
EOF

# Auth Service .env
print_status "Creating Auth Service .env file..."
cat > services/auth-service/.env << 'EOF'
# Auth Service Environment Configuration

NODE_ENV=development
PORT=3001
API_VERSION=1.0.0

# Database
MONGODB_URI=*****************************************************************************
REDIS_URL=redis://:jobplatform2024@localhost:6379

# JWT Configuration
JWT_SECRET=job-platform-super-secret-jwt-key-for-development-change-in-production-must-be-32-chars-minimum
JWT_REFRESH_SECRET=job-platform-super-secret-refresh-key-for-development-change-in-production-must-be-32-chars
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# Google OAuth
GOOGLE_CLIENT_ID=your-google-client-id-from-console
GOOGLE_CLIENT_SECRET=your-google-client-secret-from-console
GOOGLE_CALLBACK_URL=http://localhost:3001/api/v1/google/callback

# Email Configuration
SENDGRID_API_KEY=SG.your-sendgrid-api-key-here
FROM_EMAIL=<EMAIL>

# Security
BCRYPT_ROUNDS=12
SESSION_SECRET=job-platform-session-secret-for-development-change-in-production-must-be-32-chars-minimum
CORS_ORIGIN=http://localhost:3000,http://localhost:8080

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=50

# Frontend URL
FRONTEND_URL=http://localhost:3000

# Logging
LOG_LEVEL=debug
EOF

# User Service .env
print_status "Creating User Service .env file..."
cat > services/user-service/.env << 'EOF'
# User Service Environment Configuration

NODE_ENV=development
PORT=3002
API_VERSION=1.0.0

# Database
MONGODB_URI=*****************************************************************************
REDIS_URL=redis://:jobplatform2024@localhost:6379

# JWT Configuration
JWT_SECRET=job-platform-super-secret-jwt-key-for-development-change-in-production-must-be-32-chars-minimum
JWT_REFRESH_SECRET=job-platform-super-secret-refresh-key-for-development-change-in-production-must-be-32-chars

# Security
CORS_ORIGIN=http://localhost:3000,http://localhost:8080

# Logging
LOG_LEVEL=debug
EOF

# Job Service .env
print_status "Creating Job Service .env file..."
cat > services/job-service/.env << 'EOF'
# Job Service Environment Configuration

NODE_ENV=development
PORT=3003
API_VERSION=1.0.0

# Database
MONGODB_URI=*****************************************************************************
REDIS_URL=redis://:jobplatform2024@localhost:6379

# JWT Configuration
JWT_SECRET=job-platform-super-secret-jwt-key-for-development-change-in-production-must-be-32-chars-minimum
JWT_REFRESH_SECRET=job-platform-super-secret-refresh-key-for-development-change-in-production-must-be-32-chars

# Security
CORS_ORIGIN=http://localhost:3000,http://localhost:8080

# External APIs
LINKEDIN_CLIENT_ID=your-linkedin-client-id
LINKEDIN_CLIENT_SECRET=your-linkedin-client-secret
INDEED_API_KEY=your-indeed-api-key
GLASSDOOR_API_KEY=your-glassdoor-api-key

# Logging
LOG_LEVEL=debug
EOF

# Resume Service .env
print_status "Creating Resume Service .env file..."
cat > services/resume-service/.env << 'EOF'
# Resume Service Environment Configuration

NODE_ENV=development
PORT=3004
API_VERSION=1.0.0

# Database
MONGODB_URI=*****************************************************************************
REDIS_URL=redis://:jobplatform2024@localhost:6379

# JWT Configuration
JWT_SECRET=job-platform-super-secret-jwt-key-for-development-change-in-production-must-be-32-chars-minimum
JWT_REFRESH_SECRET=job-platform-super-secret-refresh-key-for-development-change-in-production-must-be-32-chars

# AWS Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-aws-access-key-id
AWS_SECRET_ACCESS_KEY=your-aws-secret-access-key
AWS_S3_BUCKET=job-platform-files-dev

# File Upload
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=pdf,doc,docx

# Security
CORS_ORIGIN=http://localhost:3000,http://localhost:8080

# Logging
LOG_LEVEL=debug
EOF

# Create .env files for remaining services
services_ports=("analytics-service:3005" "notification-service:3006" "integration-service:3007" "payment-service:3008")

for service_port in "${services_ports[@]}"; do
    service=$(echo $service_port | cut -d':' -f1)
    port=$(echo $service_port | cut -d':' -f2)
    
    print_status "Creating $service .env file..."
    service_name=$(echo $service | sed 's/-service//' | sed 's/.*/\u&/')
    
    cat > services/$service/.env << EOF
# ${service_name} Service Environment Configuration

NODE_ENV=development
PORT=$port
API_VERSION=1.0.0

# Database
MONGODB_URI=*****************************************************************************
REDIS_URL=redis://:jobplatform2024@localhost:6379

# JWT Configuration
JWT_SECRET=job-platform-super-secret-jwt-key-for-development-change-in-production-must-be-32-chars-minimum
JWT_REFRESH_SECRET=job-platform-super-secret-refresh-key-for-development-change-in-production-must-be-32-chars

# Security
CORS_ORIGIN=http://localhost:3000,http://localhost:8080

# Logging
LOG_LEVEL=debug
EOF
done

print_success "All environment files created successfully!"

echo ""
echo "📝 Next steps:"
echo "1. Update the .env files with your actual API keys and credentials"
echo "2. Run './scripts/deploy.sh' to start the platform"
echo ""
echo "🔑 Default admin credentials:"
echo "   Email: <EMAIL>"
echo "   Password: admin123"
echo ""

print_success "Environment setup completed!"