# Auth Service Environment Configuration

NODE_ENV=development
PORT=8080
API_VERSION=1.0.0

# Database
MONGODB_URI=mongodb+srv://ResumeAdmin:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0ResumePlatform
REDIS_URL=redis://:jobplatform2024@localhost:6379

# JWT Configuration
JWT_SECRET=job-platform-super-secret-jwt-key-for-development-change-in-production-must-be-32-chars-minimum
JWT_REFRESH_SECRET=job-platform-super-secret-refresh-key-for-development-change-in-production-must-be-32-chars
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# Google OAuth
GOOGLE_CLIENT_ID=604212784470-cn2b43d1s4a4jt22n9btk7i7dt91vcr9.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
GOOGLE_CALLBACK_URL=https://auth-service.ondigitalocean.app/api/v1/auth/google/callback

# Email Configuration
SENDGRID_API_KEY=SG.your-sendgrid-api-key-here
FROM_EMAIL=<EMAIL>

# Security
BCRYPT_ROUNDS=12
SESSION_SECRET=job-platform-session-secret-for-development-change-in-production-must-be-32-chars-minimum
CORS_ORIGIN=*

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=50

# Frontend URL
FRONTEND_URL=https://frontend-service.ondigitalocean.app

# Logging
LOG_LEVEL=debug
