import axios, { AxiosResponse } from 'axios';
import { logger } from '../utils/logger';
// Using DigitalOcean environment variables directly

export interface JobApiResponse {
  id: string;
  title: string;
  company: string;
  description: string;
  location: string;
  type: string;
  salary?: {
    min?: number;
    max?: number;
    currency: string;
  } | undefined;
  requirements?: string[];
  benefits?: string[];
  skills?: string[];
  experience?: {
    min?: number;
    max?: number;
  };
  education?: string;
  remote?: boolean;
  postedAt: string;
  expiresAt?: string;
  url: string;
  source: string;
}

export class JobApiService {
  private readonly adzunaBaseUrl = 'https://api.adzuna.com/v1/api';
  private readonly indeedBaseUrl = 'https://api.indeed.com/ads/apisearch';
  private readonly maxRetries = 3;
  private readonly retryDelay = 1000; // 1 second

  /**
   * Fetch jobs from Adzuna API
   */
  async fetchJobsFromAdzuna(
    query: string = 'developer',
    location: string = 'us',
    results: number = 50
  ): Promise<JobApiResponse[]> {
    // Input validation and sanitization
    const sanitizedQuery = this.sanitizeInput(query);
    const sanitizedLocation = this.sanitizeInput(location);
    const sanitizedResults = Math.min(Math.max(1, results), 100); // Limit to 1-100
    if (!process.env.ADZUNA_APP_ID || !process.env.ADZUNA_API_KEY) {
      logger.warn('Adzuna API credentials not configured');
      return [];
    }

    return await this.retryWithBackoff(async () => {
      const response: AxiosResponse = await axios.get(
        `${this.adzunaBaseUrl}/jobs/${sanitizedLocation}/search/1`,
        {
          params: {
            app_id: process.env.ADZUNA_APP_ID,
            app_key: process.env.ADZUNA_API_KEY,
            what: sanitizedQuery,
            results_per_page: sanitizedResults,
            content_type: 'application/json',
          },
          timeout: 10000,
        }
      );

      const jobs = response.data.results || [];
      return jobs.map((job: any) => this.transformAdzunaJob(job));
    }, 'Adzuna');
  }

  /**
   * Fetch jobs from Indeed API
   */
  async fetchJobsFromIndeed(
    query: string = 'developer',
    location: string = 'us',
    results: number = 25
  ): Promise<JobApiResponse[]> {
    if (!process.env.INDEED_PUBLISHER_ID) {
      logger.warn('Indeed API credentials not configured');
      return [];
    }

    try {
      const response: AxiosResponse = await axios.get(this.indeedBaseUrl, {
        params: {
          publisher: process.env.INDEED_PUBLISHER_ID,
          v: '2',
          format: 'json',
          q: query,
          l: location,
          sort: 'date',
          radius: '25',
          st: 'jobsite',
          jt: 'fulltime',
          start: 0,
          limit: results,
          fromage: '7',
          highlight: '0',
          filter: '0',
          latlong: '1',
          co: 'us',
          chnl: '',
          userip: '*******',
          useragent: 'Mozilla/5.0 (compatible; JobBot/1.0)',
        },
        timeout: 10000,
      });

      const jobs = response.data.results || [];
      return jobs.map((job: any) => this.transformIndeedJob(job));
    } catch (error) {
      logger.error('Error fetching jobs from Indeed:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        status: error && typeof error === 'object' && 'response' in error 
          ? (error as any).response?.status : 'unknown'
      });
      return [];
    }
  }

  /**
   * Fetch jobs from multiple sources
   */
  async fetchJobsFromAllSources(
    query: string = 'developer',
    location: string = 'us',
    results: number = 50
  ): Promise<JobApiResponse[]> {
    try {
      const [adzunaJobs, indeedJobs] = await Promise.allSettled([
        this.fetchJobsFromAdzuna(query, location, Math.floor(results / 2)),
        this.fetchJobsFromIndeed(query, location, Math.floor(results / 2)),
      ]);

      const allJobs: JobApiResponse[] = [];
      
      if (adzunaJobs.status === 'fulfilled') {
        allJobs.push(...adzunaJobs.value);
      } else {
        logger.warn('Adzuna API failed:', adzunaJobs.reason);
      }
      
      if (indeedJobs.status === 'fulfilled') {
        allJobs.push(...indeedJobs.value);
      } else {
        logger.warn('Indeed API failed:', indeedJobs.reason);
      }

      logger.info(`Successfully fetched ${allJobs.length} jobs from external APIs`);
      return allJobs;
    } catch (error) {
      logger.error('Error fetching jobs from all sources:', error);
      return [];
    }
  }

  /**
   * Transform Adzuna job data to our format
   */
  private transformAdzunaJob(job: any): JobApiResponse {
    return {
      id: `adzuna_${job.id}`,
      title: job.title || 'No title',
      company: job.company?.display_name || 'Unknown company',
      description: job.description || '',
      location: job.location?.display_name || 'Unknown location',
      type: this.mapJobType(job.contract_type),
      salary: job.salary_min || job.salary_max ? {
        min: job.salary_min,
        max: job.salary_max,
        currency: job.salary_currency || 'USD',
      } : undefined,
      requirements: this.extractRequirements(job.description),
      skills: this.extractSkills(job.description),
      experience: this.extractExperience(job.description),
      education: this.extractEducation(job.description),
      remote: this.isRemote(job.description),
      postedAt: job.created || new Date().toISOString(),
      expiresAt: job.expires ? new Date(job.expires).toISOString() : '',
      url: job.redirect_url || job.url,
      source: 'adzuna',
    };
  }

  /**
   * Transform Indeed job data to our format
   */
  private transformIndeedJob(job: any): JobApiResponse {
    return {
      id: `indeed_${job.jobkey}`,
      title: job.jobtitle || 'No title',
      company: job.company || 'Unknown company',
      description: job.snippet || '',
      location: job.formattedLocation || 'Unknown location',
      type: this.mapJobType(job.jobType),
      salary: this.extractSalaryFromSnippet(job.snippet),
      requirements: this.extractRequirements(job.snippet),
      skills: this.extractSkills(job.snippet),
      experience: this.extractExperience(job.snippet),
      education: this.extractEducation(job.snippet),
      remote: this.isRemote(job.snippet),
      postedAt: job.date || new Date().toISOString(),
      url: job.url,
      source: 'indeed',
    };
  }

  /**
   * Map external job types to our internal format
   */
  private mapJobType(externalType: string): string {
    const typeMap: Record<string, string> = {
      'full-time': 'full_time',
      'fulltime': 'full_time',
      'part-time': 'part_time',
      'parttime': 'part_time',
      'contract': 'contract',
      'contractor': 'contract',
      'internship': 'internship',
      'freelance': 'freelance',
      'temporary': 'contract',
    };

    return typeMap[externalType?.toLowerCase()] || 'full_time';
  }

  /**
   * Extract requirements from job description
   */
  private extractRequirements(description: string): string[] {
    if (!description) return [];
    
    const requirements: string[] = [];
    const reqPatterns = [
      /(?:requirements?|qualifications?|must have|should have)[:\s]*([^.]+)/gi,
      /(?:experience|years? of experience)[:\s]*([^.]+)/gi,
      /(?:skills?|technologies?)[:\s]*([^.]+)/gi,
    ];

    reqPatterns.forEach(pattern => {
      const matches = description.match(pattern);
      if (matches) {
        requirements.push(...matches.map(match => match.trim()));
      }
    });

    return requirements.slice(0, 10); // Limit to 10 requirements
  }

  /**
   * Extract skills from job description
   */
  private extractSkills(description: string): string[] {
    if (!description) return [];
    
    const commonSkills = [
      'JavaScript', 'TypeScript', 'Python', 'Java', 'C++', 'C#', 'Go', 'Rust',
      'React', 'Angular', 'Vue', 'Node.js', 'Express', 'Django', 'Flask',
      'AWS', 'Azure', 'GCP', 'Docker', 'Kubernetes', 'MongoDB', 'PostgreSQL',
      'MySQL', 'Redis', 'Git', 'Linux', 'Agile', 'Scrum', 'DevOps',
    ];

    const foundSkills = commonSkills.filter(skill =>
      description.toLowerCase().includes(skill.toLowerCase())
    );

    return foundSkills.slice(0, 15); // Limit to 15 skills
  }

  /**
   * Extract experience requirements
   */
  private extractExperience(description: string): { min?: number; max?: number } {
    if (!description) return {};
    
    const expPattern = /(\d+)[\s-]*(\d+)?\s*years?/gi;
    const match = description.match(expPattern);
    
    if (match) {
      const numbers = match[0].match(/\d+/g);
      if (numbers) {
        const min = parseInt(numbers[0]);
        const max = numbers[1] ? parseInt(numbers[1]) : min;
        return { min, max };
      }
    }
    
    return {};
  }

  /**
   * Extract education requirements
   */
  private extractEducation(description: string): string {
    if (!description) return 'any';
    
    const educationPatterns = [
      { pattern: /phd|doctorate/gi, level: 'phd' },
      { pattern: /master|mba|ms|ma/gi, level: 'master' },
      { pattern: /bachelor|bachelor's|bs|ba|b\.s\.|b\.a\./gi, level: 'bachelor' },
      { pattern: /associate|aa|as/gi, level: 'associate' },
      { pattern: /high school|hs/gi, level: 'high_school' },
    ];

    for (const { pattern, level } of educationPatterns) {
      if (pattern.test(description)) {
        return level;
      }
    }

    return 'any';
  }

  /**
   * Check if job is remote
   */
  private isRemote(description: string): boolean {
    if (!description) return false;
    
    const remotePatterns = [
      /remote/gi,
      /work from home/gi,
      /wfh/gi,
      /telecommute/gi,
      /virtual/gi,
    ];

    return remotePatterns.some(pattern => pattern.test(description));
  }

  /**
   * Extract salary information from job snippet
   */
  private extractSalaryFromSnippet(snippet: string): { min?: number; max?: number; currency: string } | undefined {
    if (!snippet) return undefined;
    
    const salaryPattern = /\$(\d{1,3}(?:,\d{3})*(?:k|K)?)\s*[-–]\s*\$(\d{1,3}(?:,\d{3})*(?:k|K)?)/gi;
    const match = snippet.match(salaryPattern);
    
    if (match) {
      const numbers = match[0].match(/\d+/g);
      if (numbers && numbers.length >= 2) {
        const min = parseInt(numbers[0].replace(/,/g, ''));
        const max = parseInt(numbers[1]?.replace(/,/g, '') || '0');
        return { min, max, currency: 'USD' };
      }
    }
    
    return undefined;
  }

  /**
   * Sanitize input to prevent injection attacks
   */
  private sanitizeInput(input: string): string {
    if (!input || typeof input !== 'string') {
      return '';
    }
    
    return input
      .trim()
      .replace(/[<>\"'&]/g, '') // Remove potentially dangerous characters
      .substring(0, 100) // Limit length
      .toLowerCase();
  }

  /**
   * Retry mechanism with exponential backoff
   */
  private async retryWithBackoff<T>(
    operation: () => Promise<T>,
    serviceName: string
  ): Promise<T> {
    let lastError: Error | null = null;
    
    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');
        
        if (attempt === this.maxRetries) {
          logger.error(`Failed to fetch from ${serviceName} after ${this.maxRetries} attempts:`, {
            message: lastError.message,
            status: error && typeof error === 'object' && 'response' in error 
              ? (error as any).response?.status : 'unknown'
          });
          return [] as T;
        }
        
        const delay = this.retryDelay * Math.pow(2, attempt - 1);
        logger.warn(`${serviceName} attempt ${attempt} failed, retrying in ${delay}ms:`, {
          message: lastError.message
        });
        
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    return [] as T;
  }
}
