import { Router } from 'express';
import { ResponseUtil } from '../utils/response';
import { Application } from '../models/application.model';
import { authenticateToken } from '../middleware/auth.middleware';

const router = Router();

// Get all applications
router.get('/', async (req, res) => {
  try {
    const { page = 1, limit = 10, status, userId } = req.query;
    const skip = (Number(page) - 1) * Number(limit);
    
    const filter: any = {};
    if (status) filter.status = status;
    if (userId) filter.userId = userId;
    
    const applications = await Application.find(filter)
      .sort({ appliedAt: -1 })
      .skip(skip)
      .limit(Number(limit));
    
    const total = await Application.countDocuments(filter);
    
    const response = ResponseUtil.success(
      {
        applications,
        total,
        page: Number(page),
        limit: Number(limit),
        totalPages: Math.ceil(total / Number(limit)),
      },
      'Applications retrieved successfully'
    );

    res.status(response.statusCode).json(response);
  } catch (error) {
    const response = ResponseUtil.error('Failed to retrieve applications', 500);
    res.status(response.statusCode).json(response);
  }
});

// Create application
router.post('/', async (req, res) => {
  try {
    const applicationData = {
      ...req.body,
      status: 'pending',
      appliedAt: new Date(),
    };
    
    const application = new Application(applicationData);
    await application.save();
    
    const response = ResponseUtil.created(application, 'Application submitted successfully');
    res.status(response.statusCode).json(response);
  } catch (error) {
    const response = ResponseUtil.error('Failed to create application', 500);
    res.status(response.statusCode).json(response);
  }
});

// Update application
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const application = await Application.findByIdAndUpdate(
      id,
      { ...req.body, updatedAt: new Date() },
      { new: true, runValidators: true }
    );
    
    if (!application) {
      const response = ResponseUtil.error('Application not found', 404);
      return res.status(response.statusCode).json(response);
    }
    
    const response = ResponseUtil.success(application, 'Application updated successfully');
    return res.status(response.statusCode).json(response);
  } catch (error) {
    const response = ResponseUtil.error('Failed to update application', 500);
    return res.status(response.statusCode).json(response);
  }
});

// Get application statistics
router.get('/stats', async (req, res) => {
  try {
    const { userId } = req.query;
    const filter: any = {};
    if (userId) filter.userId = userId;
    
    const now = new Date();
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    
    const [
      totalApplications,
      pendingApplications,
      interviewsScheduled,
      offersReceived,
      applicationsThisWeek,
      applicationsThisMonth,
    ] = await Promise.all([
      Application.countDocuments(filter),
      Application.countDocuments({ ...filter, status: 'pending' }),
      Application.countDocuments({ ...filter, status: 'interviewed' }),
      Application.countDocuments({ ...filter, status: 'accepted' }),
      Application.countDocuments({ ...filter, appliedAt: { $gte: oneWeekAgo } }),
      Application.countDocuments({ ...filter, appliedAt: { $gte: oneMonthAgo } }),
    ]);
    
    const successRate = totalApplications > 0 ? (offersReceived / totalApplications) * 100 : 0;
    
    const response = ResponseUtil.success(
      {
        totalApplications,
        pendingApplications,
        interviewsScheduled,
        offersReceived,
        applicationsThisWeek,
        applicationsThisMonth,
        averageResponseTime: 0, // TODO: Calculate from actual response times
        successRate: Math.round(successRate * 100) / 100,
        jobsSaved: 0, // TODO: Implement jobs saved feature
      },
      'Application statistics retrieved successfully'
    );

    res.status(response.statusCode).json(response);
  } catch (error) {
    const response = ResponseUtil.error('Failed to retrieve application statistics', 500);
    res.status(response.statusCode).json(response);
  }
});

// Get application analytics
router.get('/analytics', authenticateToken, async (req, res) => {
  try {
    const userId = req.user!.id;
    
    const filter = { userId };
    
    const [
      applicationsSent,
      interviewsScheduled,
      offersReceived,
    ] = await Promise.all([
      Application.countDocuments(filter),
      Application.countDocuments({ ...filter, status: 'interviewed' }),
      Application.countDocuments({ ...filter, status: 'accepted' }),
    ]);
    
    const responseRate = applicationsSent > 0 ? (interviewsScheduled / applicationsSent) * 100 : 0;
    const interviewRate = interviewsScheduled > 0 ? (offersReceived / interviewsScheduled) * 100 : 0;
    const offerRate = applicationsSent > 0 ? (offersReceived / applicationsSent) * 100 : 0;
    
    const response = ResponseUtil.success(
      {
        applicationsSent,
        interviewsScheduled,
        offersReceived,
        responseRate: Math.round(responseRate * 100) / 100,
        interviewRate: Math.round(interviewRate * 100) / 100,
        offerRate: Math.round(offerRate * 100) / 100,
      },
      'Application analytics retrieved successfully'
    );

    return res.status(response.statusCode).json(response);
  } catch (error) {
    const response = ResponseUtil.error('Failed to retrieve application analytics', 500);
    return res.status(response.statusCode).json(response);
  }
});

export { router as applicationRoutes };
