info: MongoDB connected successfully {"service":"resume-service","timestamp":"2025-09-21T10:48:00.634Z"}
info: Initializing S3 service... {"service":"resume-service","timestamp":"2025-09-21T10:48:00.638Z"}
info: S3 service initialized successfully {"service":"resume-service","timestamp":"2025-09-21T10:48:00.638Z"}
info: 📄 Resume Service running on port 3004 {"service":"resume-service","timestamp":"2025-09-21T10:48:00.640Z"}
info: 🔄 Resume Service shutting down... {"service":"resume-service","timestamp":"2025-09-21T10:51:15.983Z"}
warn: <PERSON><PERSON>D<PERSON> disconnected {"service":"resume-service","timestamp":"2025-09-21T10:51:16.005Z"}
info: MongoDB disconnected successfully {"service":"resume-service","timestamp":"2025-09-21T10:51:16.005Z"}
info: ✅ Resume Service shutdown completed {"service":"resume-service","timestamp":"2025-09-21T10:51:16.005Z"}
8[0m
[0mGET /health [32m200[0m 0.201 ms - 408[0m
