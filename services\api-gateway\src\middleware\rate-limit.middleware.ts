import rateLimit, { ip<PERSON>eyGenerator } from 'express-rate-limit';
import slowDown from 'express-slow-down';
import { Request, Response } from 'express';
import { logger } from '../utils/logger';

// Create rate limiter
export const rateLimitMiddleware = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS ?? '900000'), // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS ?? '100'), // limit each IP to 100 requests per windowMs
  message: {
    success: false,
    message: 'Too many requests from this IP, please try again later.',
    retryAfter: Math.ceil(
      parseInt(process.env.RATE_LIMIT_WINDOW_MS ?? '900000') / 1000
    ),
  },
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  keyGenerator: (req: Request): string => {
    // Use user ID if authenticated, otherwise use IP with proper IPv6 handling
    const user = req.user;
    if (user?.id) {
      return user.id;
    }
    // Use the official ipKeyGenerator helper for proper IPv6 support
    const ip = req.ip ?? req.socket.remoteAddress ?? '127.0.0.1';
    return ipKeyGenerator(ip);
  },
  handler: (req: Request, res: Response) => {
    const user = req.user;
    logger.warn(`Rate limit exceeded for ${user?.id ?? req.ip} on ${req.path}`);
    res.status(429).json({
      success: false,
      message: 'Too many requests from this IP, please try again later.',
      retryAfter: Math.ceil(
        parseInt(process.env.RATE_LIMIT_WINDOW_MS ?? '900000') / 1000
      ),
    });
  },
  skip: (req: Request): boolean => {
    // Skip rate limiting for health checks
    return req.path.startsWith('/health');
  },
});

// Create speed limiter (slow down responses after hitting limit)
export const speedLimitMiddleware = slowDown({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS ?? '900000'), // 15 minutes
  delayAfter: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS ?? '100') / 2, // allow first 50 requests per windowMs at full speed
  delayMs: () => 500, // slow down subsequent requests by 500ms per request
  maxDelayMs: 20000, // maximum delay of 20 seconds
  keyGenerator: (req: Request): string => {
    const user = req.user;
    if (user?.id) {
      return user.id;
    }
    // Use the official ipKeyGenerator helper for proper IPv6 support
    const ip = req.ip ?? req.socket.remoteAddress ?? '127.0.0.1';
    return ipKeyGenerator(ip);
  },
  skip: (req: Request): boolean => {
    return req.path.startsWith('/health');
  },
  validate: {
    delayMs: false, // Disable the warning about delayMs behavior change
  },
});

// Premium user rate limits (higher limits)
export const premiumRateLimitMiddleware = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS ?? '900000'),
  max: parseInt(process.env.PREMIUM_RATE_LIMIT_MAX_REQUESTS ?? '1000'), // 10x higher limit
  message: {
    success: false,
    message: 'Rate limit exceeded for premium user.',
    retryAfter: Math.ceil(
      parseInt(process.env.RATE_LIMIT_WINDOW_MS ?? '900000') / 1000
    ),
  },
  keyGenerator: (req: Request): string => {
    const user = req.user;
    if (user?.id) {
      return user.id;
    }
    // Use the official ipKeyGenerator helper for proper IPv6 support
    const ip = req.ip ?? req.socket.remoteAddress ?? '127.0.0.1';
    return ipKeyGenerator(ip);
  },
  skip: (req: Request): boolean => {
    // Only apply to authenticated premium users
    const user = req.user;
    return !user || !['premium', 'enterprise'].includes(user.subscriptionTier);
  },
});

// API-specific rate limits
export const authRateLimitMiddleware = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // limit each IP to 5 login requests per windowMs
  message: {
    success: false,
    message: 'Too many authentication attempts, please try again later.',
  },
  skipSuccessfulRequests: true, // Don't count successful requests
  keyGenerator: (req: Request): string => {
    // Use the official ipKeyGenerator helper for proper IPv6 support
    const ip = req.ip ?? req.socket.remoteAddress ?? '127.0.0.1';
    return ipKeyGenerator(ip);
  },
});

export const uploadRateLimitMiddleware = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 10, // limit each user to 10 uploads per hour
  message: {
    success: false,
    message: 'Upload limit exceeded, please try again later.',
  },
  keyGenerator: (req: Request): string => {
    const user = req.user;
    if (user?.id) {
      return user.id;
    }
    // Use the official ipKeyGenerator helper for proper IPv6 support
    const ip = req.ip ?? req.socket.remoteAddress ?? '127.0.0.1';
    return ipKeyGenerator(ip);
  },
});
