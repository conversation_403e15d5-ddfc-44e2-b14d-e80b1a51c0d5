# 🚀 Quick Start Guide

## One-Command Setup

```bash
# Setup environment files
./scripts/setup-env.sh

# Start the platform locally
./scripts/deploy-local.sh
```

That's it! Your job application platform will be running locally.

## What This Does

1. **Creates Environment Files** - Sets up `.env` files for all services with mock data
2. **Starts Infrastructure** - Launches MongoDB and Redis in Docker
3. **Builds Services** - Compiles TypeScript and installs dependencies
4. **Runs Services** - Starts all microservices locally
5. **Health Checks** - Verifies everything is working

## Access Your Platform

- **API Gateway**: http://localhost:3000
- **Auth Service**: http://localhost:3001
- **User Service**: http://localhost:3002
- **Job Service**: http://localhost:3003
- **Resume Service**: http://localhost:3004

## Default Admin Account

- **Email**: `<EMAIL>`
- **Password**: `admin123`

## Health Checks

Visit any service's `/health` endpoint:
- http://localhost:3000/health (API Gateway)
- http://localhost:3001/health (Auth Service)
- etc.

## View Logs

```bash
# View all service logs
./scripts/deploy-local.sh logs

# View specific service log
./scripts/deploy-local.sh logs auth-service
```

## Stop Services

```bash
./scripts/deploy-local.sh stop
```

## Next Steps

1. **Update API Keys**: Edit the `.env` files with your real API keys
2. **Test Endpoints**: Use the health check endpoints to verify services
3. **Explore APIs**: Check http://localhost:3000/api for service information
4. **Add Features**: Start building your custom functionality

## Troubleshooting

- **Port conflicts**: Make sure ports 3000-3008, 27017, 6379 are available
- **Docker issues**: Ensure Docker is running
- **Build errors**: Check service logs with `./scripts/deploy-local.sh logs [service]`

Happy coding! 🎉