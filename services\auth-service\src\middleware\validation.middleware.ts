import { Request, Response, NextFunction } from 'express';
import { z } from 'zod';
import { ResponseUtil } from '../utils/response';

export const validateBody = (schema: z.ZodType<unknown>) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      const result = schema.parse(req.body);
      req.body = result;
      next();
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errors = error.issues.map(
          err => `${err.path.join('.')}: ${err.message}`
        );
        res
          .status(400)
          .json(ResponseUtil.error('Validation failed', 400, errors, req.path));
        return;
      }
      next(error);
    }
  };
};

export const validateQuery = (schema: z.ZodType<unknown>) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      const result = schema.parse(req.query);
      // Cast to ParsedQs compatible type - Express ParsedQs allows string, string[], or nested ParsedQs
      req.query = result as { [key: string]: string | string[] | undefined };
      next();
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errors = error.issues.map(
          err => `${err.path.join('.')}: ${err.message}`
        );
        res
          .status(400)
          .json(
            ResponseUtil.error('Query validation failed', 400, errors, req.path)
          );
        return;
      }
      next(error);
    }
  };
};

export const validateParams = (schema: z.ZodType<unknown>) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      const result = schema.parse(req.params);
      req.params = result as typeof req.params; // Assert the parsed result to the expected type of req.params
      next();
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errors = error.issues.map(
          err => `${err.path.join('.')}: ${err.message}`
        );
        res
          .status(400)
          .json(
            ResponseUtil.error(
              'Parameter validation failed',
              400,
              errors,
              req.path
            )
          );
        return;
      }
      next(error);
    }
  };
};
