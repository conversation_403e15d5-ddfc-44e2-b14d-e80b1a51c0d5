import { S3Client, PutObjectCommand, DeleteObjectCommand, GetObjectCommand } from '@aws-sdk/client-s3';
import { logger } from '../utils/logger';

export class S3Service {
  private s3Client: S3Client;
  private bucketName: string;
  private initialized = false;

  constructor() {
    this.bucketName = process.env.AWS_S3_BUCKET || 'job-platform-resumes';
    
    this.s3Client = new S3Client({
      region: process.env.AWS_REGION || 'us-east-1',
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
      },
    });
  }

  /**
   * Initialize S3 service
   */
  public async initialize(): Promise<void> {
    try {
      logger.info('Initializing S3 service...');

      // Test S3 connection by listing objects
      await this.s3Client.send(new GetObjectCommand({
        Bucket: this.bucketName,
        Key: 'test-connection'
      })).catch(() => {
        // Expected to fail for test key, but connection is working
      });

      this.initialized = true;
      logger.info('S3 service initialized successfully', {
        bucket: this.bucketName,
        region: process.env.AWS_REGION || 'us-east-1'
      });
    } catch (error) {
      logger.error('S3 service initialization failed:', error);
      throw new Error(`S3 initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Upload file to S3
   */
  public async uploadFile(file: Buffer, key: string, contentType: string): Promise<string> {
    if (!this.initialized) {
      throw new Error('S3 service not initialized');
    }

    try {
      logger.info(`Uploading file to S3: ${key}`, {
        size: file.length,
        contentType
      });

      const command = new PutObjectCommand({
        Bucket: this.bucketName,
        Key: key,
        Body: file,
        ContentType: contentType,
        ServerSideEncryption: 'AES256',
        Metadata: {
          'upload-timestamp': new Date().toISOString(),
          'file-size': file.length.toString()
        }
      });

      await this.s3Client.send(command);

      const fileUrl = `https://${this.bucketName}.s3.amazonaws.com/${key}`;
      
      logger.info(`File uploaded successfully: ${fileUrl}`);
      return fileUrl;
    } catch (error) {
      logger.error('File upload failed:', error);
      throw new Error(`File upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Delete file from S3
   */
  public async deleteFile(key: string): Promise<void> {
    if (!this.initialized) {
      throw new Error('S3 service not initialized');
    }

    try {
      logger.info(`Deleting file from S3: ${key}`);

      const command = new DeleteObjectCommand({
        Bucket: this.bucketName,
        Key: key
      });

      await this.s3Client.send(command);
      logger.info(`File deleted successfully: ${key}`);
    } catch (error) {
      logger.error('File deletion failed:', error);
      throw new Error(`File deletion failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate presigned URL for file download
   */
  public async getSignedDownloadUrl(key: string, expiresIn: number = 3600): Promise<string> {
    if (!this.initialized) {
      throw new Error('S3 service not initialized');
    }

    try {
      // For now, return direct S3 URL (in production, implement proper presigned URLs)
      const directUrl = `https://${this.bucketName}.s3.amazonaws.com/${key}`;
      logger.info(`Generated download URL for: ${key}`);
      return directUrl;
    } catch (error) {
      logger.error('Failed to generate download URL:', error);
      throw new Error(`Failed to generate download URL: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Check if file exists in S3
   */
  public async fileExists(key: string): Promise<boolean> {
    if (!this.initialized) {
      throw new Error('S3 service not initialized');
    }

    try {
      await this.s3Client.send(new GetObjectCommand({
        Bucket: this.bucketName,
        Key: key
      }));
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get file metadata
   */
  public async getFileMetadata(key: string): Promise<{ size: number; lastModified: Date; contentType: string } | null> {
    if (!this.initialized) {
      throw new Error('S3 service not initialized');
    }

    try {
      const command = new GetObjectCommand({
        Bucket: this.bucketName,
        Key: key
      });

      const response = await this.s3Client.send(command);
      
      return {
        size: response.ContentLength || 0,
        lastModified: response.LastModified || new Date(),
        contentType: response.ContentType || 'application/octet-stream'
      };
    } catch (error) {
      logger.error('Failed to get file metadata:', error);
      return null;
    }
  }

  /**
   * Check if service is initialized
   */
  public isInitialized(): boolean {
    return this.initialized;
  }
}
