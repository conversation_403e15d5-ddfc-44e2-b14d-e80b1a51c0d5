import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { ResponseUtil } from '../utils/response';

// Extend the Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email: string;
        role: string;
        [key: string]: any;
      };
    }
  }
}

export const authenticateToken = (req: Request, res: Response, next: NextFunction) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

  if (!token) {
    const response = ResponseUtil.error('Access token required', 401);
    return res.status(response.statusCode).json(response);
  }

  try {
    const secret = process.env.JWT_SECRET || 'your-secret-key';
    const decoded = jwt.verify(token, secret) as any;
    
    req.user = {
      id: decoded.userId || decoded.id,
      email: decoded.email,
      role: decoded.role,
      ...decoded
    };
    
    next();
  } catch (error) {
    const response = ResponseUtil.error('Invalid or expired token', 401);
    return res.status(response.statusCode).json(response);
  }
};

export const requireRole = (roles: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      const response = ResponseUtil.error('Authentication required', 401);
      return res.status(response.statusCode).json(response);
    }

    if (!roles.includes(req.user.role)) {
      const response = ResponseUtil.error('Insufficient permissions', 403);
      return res.status(response.statusCode).json(response);
    }

    next();
  };
};
