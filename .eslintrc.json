{
  "root": true,
  "parser": "@typescript-eslint/parser",
  "parserOptions": {
    "ecmaVersion": 2022,
    "sourceType": "module",
    "project": "./tsconfig.json"
  },
  "plugins": ["@typescript-eslint", "prettier"],
  "extends": [
    "eslint:recommended",
    "@typescript-eslint/recommended",
    "@typescript-eslint/recommended-requiring-type-checking",
    "prettier"
  ],
  "rules": {
    // Formatting and basic rules
    "prettier/prettier": "error",
    "@typescript-eslint/no-unused-vars": "error",
    "no-debugger": "error",
    "prefer-const": "error",
    "no-var": "error",

    // CRITICAL SAFETY RULES (Keep as errors)
    "@typescript-eslint/no-unsafe-assignment": "error",
    "@typescript-eslint/no-unsafe-call": "error", 
    "@typescript-eslint/no-unsafe-member-access": "error",
    "@typescript-eslint/no-unsafe-argument": "error",
    "@typescript-eslint/no-unsafe-return": "error",
    "@typescript-eslint/no-floating-promises": "error",
    "@typescript-eslint/no-misused-promises": "error",
    "@typescript-eslint/no-deprecated": "error",

    // IMPORTANT BUT FLEXIBLE (Keep as warnings or errors)
    "@typescript-eslint/prefer-nullish-coalescing": "error",
    "@typescript-eslint/prefer-optional-chain": "error",

    // RELAXED RULES (Warn instead of error, or off)
    "@typescript-eslint/no-explicit-any": "warn",
    "@typescript-eslint/explicit-function-return-type": "off",
    "@typescript-eslint/no-redundant-type-constituents": "warn",
    "@typescript-eslint/no-unnecessary-condition": "warn",
    "@typescript-eslint/ban-ts-comment": "warn",

    // STYLE PREFERENCES (Keep as errors for consistency)
    "object-shorthand": "error",
    "prefer-template": "error",
    "prefer-arrow-callback": "error",
    "prefer-spread": "error",
    "prefer-rest-params": "error",
    "max-len": ["error", { 
      "code": 120, 
      "ignoreUrls": true, 
      "ignoreComments": true, 
      "ignoreStrings": true 
    }]
  },
  "env": {
    "node": true,
    "browser": true,
    "es2022": true,
    "jest": true,
    "mongo": true
  },
  "globals": {
    "NativeDate": "readonly"
  },
  "overrides": [
    {
      "files": ["*.js", "*.config.js", "tailwind.config.js", "vite.config.ts"],
      "rules": {
        "@typescript-eslint/no-require-imports": "off",
        "@typescript-eslint/no-unsafe-assignment": "off",
        "@typescript-eslint/no-unsafe-call": "off",
        "@typescript-eslint/no-unsafe-member-access": "off",
        "@typescript-eslint/no-unsafe-argument": "off",
        "no-console": "off"
      }
    },
    {
      "files": ["*.ts", "*.tsx"],
      "rules": {
        "no-console": "warn"
      }
    }
  ],
  "ignorePatterns": [
    "dist/**/*",
    "node_modules/**/*",
    "**/*.js",
    "**/*.d.ts",
    "**/*.js.map",
    "**/*.d.ts.map"
  ]
}