{"name": "@job-platform/resume-service", "version": "1.0.0", "description": "Resume processing service for job application platform", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "prestart": "npm install --production", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "setup": "npm install"}, "dependencies": {"@aws-sdk/client-s3": "^3.893.0", "@aws-sdk/s3-presigned-post": "^3.893.0", "compression": "^1.8.1", "compromise": "^14.14.4", "cors": "^2.8.5", "dotenv": "^17.2.2", "express": "^5.1.0", "file-type": "^21.0.0", "helmet": "^8.1.0", "ioredis": "^5.7.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.18.1", "morgan": "^1.10.1", "multer": "^2.0.2", "multer-s3": "^3.0.1", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pdf-lib": "^1.17.1", "pdf-parse": "^1.1.1", "pdf2pic": "^3.2.0", "sharp": "^0.34.4", "winston": "^3.17.0", "zod": "^4.1.11"}, "devDependencies": {"@types/compression": "^1.8.1", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.10", "@types/multer": "^2.0.0", "@types/multer-s3": "^3.0.3", "@types/node": "^24.5.2", "@types/passport": "^1.0.17", "@types/passport-jwt": "^4.0.1", "@types/pdf-parse": "^1.1.5", "jest": "^30.1.3", "ts-jest": "^29.4.4", "tsx": "^4.20.5", "typescript": "^5.9.2"}, "engines": {"node": ">=22.18.0", "npm": ">=10.0.0"}}