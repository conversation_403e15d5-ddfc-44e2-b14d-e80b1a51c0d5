# Multi-stage build for production
FROM node:22-alpine AS builder

# Set working directory
WORKDIR /app

# Copy package files (build context is service directory)
COPY package*.json ./

# Install dependencies (including dev dependencies for build)
RUN npm install

# Copy source code
COPY . ./

# Build the application with production environment
RUN npm run build

# Production stage - Use nginx for serving static files
FROM nginx:alpine

# Install curl for health checks
RUN apk add --no-cache curl

# Copy built files from builder stage
COPY --from=builder /app/dist /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Create necessary directories and set permissions for nginx user
RUN chown -R nginx:nginx /usr/share/nginx/html && \
    chown -R nginx:nginx /var/cache/nginx && \
    chown -R nginx:nginx /var/log/nginx && \
    chown -R nginx:nginx /etc/nginx/conf.d && \
    # Create writable directories for nginx runtime files
    mkdir -p /tmp/nginx && \
    chown -R nginx:nginx /tmp/nginx && \
    # Ensure nginx can write to /tmp for PID file
    chmod 755 /tmp

# Switch to nginx user
USER nginx

# Expose port 8080 for DigitalOcean App Platform
EXPOSE 8080

# Health check for DigitalOcean compatibility
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD curl -f http://localhost:8080/ || exit 1

# Start nginx (daemon off is already set in nginx.conf)
CMD ["nginx"]
