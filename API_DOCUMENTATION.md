# Job Application Platform - API Documentation

## 🏗️ Architecture Overview

This platform follows a microservices architecture with the following services:

- **API Gateway** (Port 3000) - Central entry point for all API requests
- **Auth Service** (Port 3001) - Authentication, authorization, and session management
- **User Service** (Port 3002) - User profile management
- **Job Service** (Port 3003) - Job listings, search, and applications
- **Resume Service** (Port 3004) - Resume upload, processing, and optimization

## 🌐 Base URLs

- **Production**: `https://api.jobplatform.com`
- **Development**: `http://localhost:3000`

All API requests should be made through the API Gateway at port 3000.

## 🔐 Authentication

The platform uses JWT (JSON Web Tokens) for authentication:

### Headers Required for Protected Routes
```
Authorization: Bearer <access_token>
X-Session-ID: <session_id>
```

### Token Refresh
Access tokens expire after 15 minutes. Use the refresh token to get new access tokens.

---

## 📋 API Endpoints

### 🔑 Authentication Service
**Base Path**: `/api/v1/auth`

#### Register User
```http
POST /api/v1/auth/register
```

**Request Body**:
```json
{
  "email": "<EMAIL>",
  "password": "securepassword123",
  "firstName": "John",
  "lastName": "Doe",
  "googleId": "optional_google_id",
  "avatar": "https://example.com/avatar.jpg"
}
```

**Response** (201):
```json
{
  "success": true,
  "message": "User registered successfully",
  "data": {
    "user": {
      "id": "user_id",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "role": "user",
      "subscriptionTier": "free",
      "isVerified": false,
      "isActive": true
    },
    "tokens": {
      "accessToken": "jwt_access_token",
      "refreshToken": "jwt_refresh_token"
    },
    "expiresIn": 900
  }
}
```

#### Login User
```http
POST /api/v1/auth/login
```

**Request Body**:
```json
{
  "email": "<EMAIL>",
  "password": "securepassword123",
  "rememberMe": false
}
```

**Response** (200):
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "user": {
      "id": "user_id",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "role": "user",
      "subscriptionTier": "free"
    },
    "tokens": {
      "accessToken": "jwt_access_token",
      "refreshToken": "jwt_refresh_token"
    },
    "expiresIn": 900
  }
}
```

#### Google OAuth Login
```http
GET /api/v1/auth/google
```
Redirects to Google OAuth consent screen.

```http
GET /api/v1/auth/google/callback
```
Google OAuth callback endpoint.

#### Refresh Token
```http
POST /api/v1/auth/refresh
```

**Request Body**:
```json
{
  "refreshToken": "jwt_refresh_token"
}
```

**Response** (200):
```json
{
  "success": true,
  "message": "Token refreshed successfully",
  "data": {
    "accessToken": "new_jwt_access_token",
    "refreshToken": "new_jwt_refresh_token",
    "expiresIn": 900
  }
}
```

#### Logout
```http
POST /api/v1/auth/logout
```

**Headers Required**:
```
Authorization: Bearer <access_token>
X-Session-ID: <session_id>
```

**Response** (200):
```json
{
  "success": true,
  "message": "Logout successful",
  "data": null
}
```

#### Verify Email
```http
POST /api/v1/auth/verify-email
```

**Request Body**:
```json
{
  "email": "<EMAIL>",
  "token": "verification_token"
}
```

#### Forgot Password
```http
POST /api/v1/auth/forgot-password
```

**Request Body**:
```json
{
  "email": "<EMAIL>"
}
```

#### Reset Password
```http
POST /api/v1/auth/reset-password
```

**Request Body**:
```json
{
  "token": "reset_token",
  "password": "newpassword123",
  "confirmPassword": "newpassword123"
}
```

#### Change Password
```http
POST /api/v1/auth/change-password
```

**Headers Required**: Authorization Bearer token

**Request Body**:
```json
{
  "currentPassword": "oldpassword123",
  "newPassword": "newpassword123",
  "confirmPassword": "newpassword123"
}
```

#### Resend Verification Email
```http
POST /api/v1/auth/resend-verification
```

**Request Body**:
```json
{
  "email": "<EMAIL>"
}
```

---

### 👤 User Service
**Base Path**: `/api/v1/users`

#### Get User Profile
```http
GET /api/v1/users/profile
```

**Headers Required**: Authorization Bearer token

**Response** (200):
```json
{
  "success": true,
  "message": "User profile retrieved",
  "data": {
    "id": "user_id",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "role": "user",
    "subscriptionTier": "free",
    "profile": {
      "bio": "Software developer with 5 years experience",
      "phoneNumber": "+1234567890",
      "location": {
        "country": "USA",
        "state": "California",
        "city": "San Francisco",
        "zipCode": "94102",
        "remote": true
      },
      "website": "https://johndoe.dev",
      "linkedin": "https://linkedin.com/in/johndoe",
      "github": "https://github.com/johndoe",
      "currentPosition": "Senior Developer",
      "currentCompany": "Tech Corp",
      "yearsOfExperience": 5,
      "expectedSalary": {
        "min": 80000,
        "max": 120000,
        "currency": "USD"
      },
      "skills": [
        {
          "name": "JavaScript",
          "level": "expert",
          "verified": true,
          "yearsOfExperience": 5
        }
      ],
      "education": [
        {
          "institution": "University of Technology",
          "degree": "Bachelor's",
          "field": "Computer Science",
          "startDate": "2018-09-01",
          "endDate": "2022-06-01",
          "gpa": 3.8
        }
      ],
      "experience": [
        {
          "company": "Tech Corp",
          "position": "Senior Developer",
          "startDate": "2022-07-01",
          "current": true,
          "description": "Full-stack development",
          "skills": ["JavaScript", "React", "Node.js"],
          "achievements": ["Led team of 5 developers", "Increased performance by 40%"]
        }
      ]
    }
  }
}
```

#### Update User Profile
```http
PUT /api/v1/users/profile
```

**Headers Required**: Authorization Bearer token

**Request Body**:
```json
{
  "firstName": "John",
  "lastName": "Doe",
  "profile": {
    "bio": "Updated bio",
    "phoneNumber": "+1234567890",
    "location": {
      "country": "USA",
      "state": "California",
      "city": "San Francisco",
      "zipCode": "94102",
      "remote": true
    },
    "website": "https://johndoe.dev",
    "linkedin": "https://linkedin.com/in/johndoe",
    "github": "https://github.com/johndoe",
    "currentPosition": "Senior Developer",
    "currentCompany": "Tech Corp",
    "yearsOfExperience": 5,
    "expectedSalary": {
      "min": 80000,
      "max": 120000,
      "currency": "USD"
    }
  }
}
```

---

### 💼 Job Service
**Base Path**: `/api/v1/jobs`

#### Get All Jobs
```http
GET /api/v1/jobs
```

**Query Parameters**:
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10, max: 100)
- `sortBy` (optional): Sort field
- `sortOrder` (optional): asc | desc
- `q` (optional): Search query
- `category` (optional): Job category
- `status` (optional): Job status

**Response** (200):
```json
{
  "success": true,
  "message": "Jobs retrieved successfully",
  "data": {
    "jobs": [
      {
        "id": "job_id",
        "title": "Software Engineer",
        "company": "Tech Corp",
        "location": "San Francisco, CA",
        "type": "full_time",
        "salary": "$100k - $150k",
        "description": "We are looking for a talented software engineer...",
        "requirements": ["JavaScript", "React", "Node.js"],
        "benefits": ["Health insurance", "401k", "Remote work"],
        "postedAt": "2024-01-15T10:00:00Z",
        "expiresAt": "2024-02-15T10:00:00Z",
        "status": "active",
        "applicationsCount": 25
      }
    ],
    "total": 1,
    "page": 1,
    "limit": 10,
    "totalPages": 1
  }
}
```

#### Get Job by ID
```http
GET /api/v1/jobs/:id
```

**Response** (200):
```json
{
  "success": true,
  "message": "Job retrieved successfully",
  "data": {
    "id": "job_id",
    "title": "Software Engineer",
    "company": "Tech Corp",
    "description": "We are looking for a talented software engineer...",
    "location": "San Francisco, CA",
    "type": "full_time",
    "salary": "$100k - $150k",
    "requirements": ["JavaScript", "React", "Node.js"],
    "benefits": ["Health insurance", "401k", "Remote work"],
    "postedAt": "2024-01-15T10:00:00Z",
    "expiresAt": "2024-02-15T10:00:00Z",
    "status": "active",
    "applicationsCount": 25,
    "companyInfo": {
      "name": "Tech Corp",
      "logo": "https://example.com/logo.png",
      "website": "https://techcorp.com",
      "size": "100-500",
      "industry": "Technology"
    }
  }
}
```

#### Create Job (Admin/Employer only)
```http
POST /api/v1/jobs
```

**Headers Required**: Authorization Bearer token

**Request Body**:
```json
{
  "title": "Software Engineer",
  "company": "Tech Corp",
  "description": "We are looking for a talented software engineer...",
  "location": "San Francisco, CA",
  "type": "full_time",
  "salary": "$100k - $150k",
  "requirements": ["JavaScript", "React", "Node.js"],
  "benefits": ["Health insurance", "401k", "Remote work"],
  "expiresAt": "2024-02-15T10:00:00Z"
}
```

**Response** (201):
```json
{
  "success": true,
  "message": "Job created successfully",
  "data": {
    "id": "new_job_id",
    "title": "Software Engineer",
    "company": "Tech Corp",
    "description": "We are looking for a talented software engineer...",
    "location": "San Francisco, CA",
    "type": "full_time",
    "salary": "$100k - $150k",
    "createdAt": "2024-01-15T10:00:00Z",
    "status": "active"
  }
}
```

---

### 📄 Application Service
**Base Path**: `/api/v1/applications`

#### Get All Applications
```http
GET /api/v1/applications
```

**Headers Required**: Authorization Bearer token

**Query Parameters**:
- `page` (optional): Page number
- `limit` (optional): Items per page
- `status` (optional): Application status

**Response** (200):
```json
{
  "success": true,
  "message": "Applications retrieved successfully",
  "data": {
    "applications": [
      {
        "id": "application_id",
        "jobId": "job_id",
        "userId": "user_id",
        "status": "submitted",
        "appliedAt": "2024-01-15T10:00:00Z",
        "coverLetter": "I am excited to apply...",
        "resumeId": "resume_id",
        "job": {
          "title": "Software Engineer",
          "company": "Tech Corp",
          "location": "San Francisco, CA"
        }
      }
    ],
    "total": 1
  }
}
```

#### Submit Application
```http
POST /api/v1/applications
```

**Headers Required**: Authorization Bearer token

**Request Body**:
```json
{
  "jobId": "job_id",
  "resumeId": "resume_id",
  "coverLetter": "I am excited to apply for this position...",
  "customFields": {
    "availabilityDate": "2024-02-01",
    "expectedSalary": 120000
  }
}
```

**Response** (201):
```json
{
  "success": true,
  "message": "Application submitted successfully",
  "data": {
    "id": "application_id",
    "jobId": "job_id",
    "userId": "user_id",
    "status": "submitted",
    "appliedAt": "2024-01-15T10:00:00Z",
    "coverLetter": "I am excited to apply...",
    "resumeId": "resume_id"
  }
}
```

#### Update Application
```http
PUT /api/v1/applications/:id
```

**Headers Required**: Authorization Bearer token

**Request Body**:
```json
{
  "status": "withdrawn",
  "coverLetter": "Updated cover letter...",
  "customFields": {
    "availabilityDate": "2024-03-01"
  }
}
```

---

### 📋 Resume Service
**Base Path**: `/api/v1/resumes`

#### Get All Resumes
```http
GET /api/v1/resumes
```

**Headers Required**: Authorization Bearer token

**Response** (200):
```json
{
  "success": true,
  "message": "Resumes retrieved successfully",
  "data": {
    "resumes": [
      {
        "id": "resume_id",
        "name": "My Resume.pdf",
        "status": "ready",
        "uploadedAt": "2024-01-15T10:00:00Z",
        "isDefault": true,
        "fileSize": 1024000,
        "fileType": "application/pdf",
        "analysis": {
          "overallScore": 85,
          "atsScore": 90,
          "readabilityScore": 88
        }
      }
    ],
    "total": 1
  }
}
```

#### Upload Resume
```http
POST /api/v1/resumes/upload
```

**Headers Required**: Authorization Bearer token

**Content-Type**: `multipart/form-data`

**Form Data**:
- `file`: Resume file (PDF, DOC, DOCX)
- `name` (optional): Custom name for the resume
- `isDefault` (optional): Set as default resume

**Response** (201):
```json
{
  "success": true,
  "message": "Resume uploaded successfully",
  "data": {
    "id": "resume_id",
    "name": "New Resume.pdf",
    "status": "processing",
    "uploadedAt": "2024-01-15T10:00:00Z",
    "isDefault": false,
    "fileSize": 1024000,
    "fileType": "application/pdf"
  }
}
```

#### Get Resume by ID
```http
GET /api/v1/resumes/:id
```

**Headers Required**: Authorization Bearer token

**Response** (200):
```json
{
  "success": true,
  "message": "Resume retrieved successfully",
  "data": {
    "id": "resume_id",
    "name": "My Resume.pdf",
    "status": "ready",
    "uploadedAt": "2024-01-15T10:00:00Z",
    "isDefault": true,
    "fileSize": 1024000,
    "fileType": "application/pdf",
    "downloadUrl": "https://s3.amazonaws.com/bucket/resume.pdf",
    "content": {
      "personalInfo": {
        "firstName": "John",
        "lastName": "Doe",
        "email": "<EMAIL>",
        "phone": "+1234567890",
        "address": "San Francisco, CA"
      },
      "summary": "Experienced software developer...",
      "experience": [
        {
          "company": "Tech Corp",
          "position": "Senior Developer",
          "startDate": "2022-07-01",
          "endDate": null,
          "description": "Full-stack development"
        }
      ],
      "education": [
        {
          "institution": "University of Technology",
          "degree": "Bachelor's in Computer Science",
          "graduationDate": "2022-06-01"
        }
      ],
      "skills": ["JavaScript", "React", "Node.js", "Python"]
    },
    "analysis": {
      "overallScore": 85,
      "atsScore": 90,
      "readabilityScore": 88,
      "suggestions": [
        "Add more quantifiable achievements",
        "Include relevant keywords for target positions"
      ],
      "strengths": [
        "Clear formatting",
        "Relevant experience",
        "Strong technical skills"
      ],
      "improvements": [
        "Add metrics to achievements",
        "Optimize for ATS scanning"
      ]
    }
  }
}
```

---

## 🔍 Health Check Endpoints

### API Gateway Health
```http
GET /health
```

### Individual Service Health
```http
GET /health
```
Available on each service port (3001-3004)

**Response** (200):
```json
{
  "success": true,
  "message": "Service is healthy",
  "data": {
    "service": "auth-service",
    "status": "healthy",
    "timestamp": "2024-01-15T10:00:00Z",
    "version": "1.0.0",
    "uptime": 3600,
    "database": "connected",
    "redis": "connected"
  }
}
```

### Detailed Health Check
```http
GET /health/detailed
```

### Readiness Check
```http
GET /health/ready
```

### Liveness Check
```http
GET /health/live
```

---

## 📊 Metrics Endpoints (Admin Only)

### System Metrics
```http
GET /metrics
```

### Service Metrics
```http
GET /metrics/services
```

### Performance Metrics
```http
GET /metrics/performance
```

---

## 🚨 Error Response Format

All error responses follow this format:

```json
{
  "success": false,
  "message": "Error message",
  "code": "ERROR_CODE",
  "details": ["Additional error details"],
  "meta": {
    "timestamp": "2024-01-15T10:00:00Z",
    "requestId": "req_123456789",
    "path": "/api/v1/endpoint"
  }
}
```

### Common HTTP Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (authentication required)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found
- `409` - Conflict (duplicate resource)
- `429` - Too Many Requests (rate limited)
- `500` - Internal Server Error

### Common Error Codes

- `VALIDATION_ERROR` - Request validation failed
- `AUTHENTICATION_ERROR` - Invalid or expired token
- `AUTHORIZATION_ERROR` - Insufficient permissions
- `RESOURCE_NOT_FOUND` - Requested resource not found
- `DUPLICATE_RESOURCE` - Resource already exists
- `RATE_LIMIT_EXCEEDED` - Too many requests
- `SERVICE_UNAVAILABLE` - Service temporarily unavailable

---

## 🔧 Rate Limiting

Rate limits are applied per IP address:

- **Authentication endpoints**: 5 requests per minute
- **General API endpoints**: 100 requests per minute
- **File upload endpoints**: 10 requests per minute

Rate limit headers are included in responses:
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1642234567
```

---

## 📝 Request/Response Headers

### Common Request Headers
```
Content-Type: application/json
Authorization: Bearer <access_token>
X-Session-ID: <session_id>
X-Request-ID: <unique_request_id>
User-Agent: <client_user_agent>
```

### Common Response Headers
```
Content-Type: application/json
X-Request-ID: <request_id>
X-Response-Time: <response_time_ms>
X-RateLimit-Limit: <rate_limit>
X-RateLimit-Remaining: <remaining_requests>
X-RateLimit-Reset: <reset_timestamp>
```

---

## 🧪 Testing the API

### Using cURL

#### Register a new user
```bash
curl -X POST http://localhost:3000/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "firstName": "Test",
    "lastName": "User"
  }'
```

#### Login
```bash
curl -X POST http://localhost:3000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

#### Get user profile (with token)
```bash
curl -X GET http://localhost:3000/api/v1/users/profile \
  -H "Authorization: Bearer <your_access_token>"
```

### Using Postman

1. Import the API collection (if available)
2. Set up environment variables:
   - `base_url`: `http://localhost:3000`
   - `access_token`: Your JWT token
   - `session_id`: Your session ID

---

## 🚀 Getting Started

1. **Start the services**:
   ```bash
   ./scripts/deploy-microservices.sh
   ```

2. **Verify services are running**:
   ```bash
   curl http://localhost:3000/health
   ```

3. **Register a test user**:
   ```bash
   curl -X POST http://localhost:3000/api/v1/auth/register \
     -H "Content-Type: application/json" \
     -d '{"email":"<EMAIL>","password":"password123","firstName":"Test","lastName":"User"}'
   ```

4. **Start building your frontend** using the endpoints documented above!

---

## 📚 Additional Resources

- [Microservices Architecture](./MICROSERVICES_ARCHITECTURE.md)
- [Deployment Guide](./DEPLOYMENT.md)
- [Quick Start Guide](./QUICK_START.md)

---

*This documentation is automatically updated as the API evolves. Last updated: January 2024*