import React, { useState, useEffect } from 'react';
import { useRootAdmin } from '@/hooks/useRootAdmin';
import { Navigate } from 'react-router-dom';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '../components/ui/card';
import { Button } from '../components/ui/Button';
import { Alert, AlertDescription } from '../components/ui/alert';
import {
  Shield,
  Server,
  Database,
  Users,
  Settings,
  Activity,
  AlertTriangle,
  CheckCircle,
  XCircle,
  RefreshCw,
  BarChart3,
  Monitor,
  Zap,
  Globe
} from 'lucide-react';
import toast from 'react-hot-toast';

interface ServiceStatus {
  name: string;
  status: 'healthy' | 'degraded' | 'unhealthy';
  uptime: number;
  responseTime: number;
  lastCheck: string;
  version: string;
  endpoints: number;
}

interface SystemOverview {
  totalUsers: number;
  activeUsers: number;
  totalJobs: number;
  activeJobs: number;
  totalApplications: number;
  systemHealth: 'healthy' | 'degraded' | 'unhealthy';
  services: ServiceStatus[];
  apiKeys: {
    adzuna: boolean;
    indeed: boolean;
    linkedin: boolean;
    glassdoor: boolean;
  };
}

const RootAdminDashboard: React.FC = () => {
  const { isRootAdmin } = useRootAdmin();
  const [overview, setOverview] = useState<SystemOverview | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Redirect if not ROOT Admin
  if (!isRootAdmin) {
    return <Navigate to="/unauthorized" replace />;
  }

  const fetchSystemOverview = async () => {
    try {
      setRefreshing(true);
      
      // Fetch data from all services
      const [jobService, authService, userService, resumeService] = await Promise.allSettled([
        fetch('/api/v1/dashboard/overview').then(res => res.json()),
        fetch('/api/v1/auth/health').then(res => res.json()),
        fetch('/api/v1/users/health').then(res => res.json()),
        fetch('/api/v1/resumes/health').then(res => res.json())
      ]);

      // Process service statuses
      const services: ServiceStatus[] = [
        {
          name: 'Job Service',
          status: jobService.status === 'fulfilled' ? 'healthy' : 'unhealthy',
          uptime: jobService.status === 'fulfilled' ? 99.9 : 0,
          responseTime: 50,
          lastCheck: new Date().toISOString(),
          version: '1.0.0',
          endpoints: 15
        },
        {
          name: 'Auth Service',
          status: authService.status === 'fulfilled' ? 'healthy' : 'unhealthy',
          uptime: authService.status === 'fulfilled' ? 99.8 : 0,
          responseTime: 30,
          lastCheck: new Date().toISOString(),
          version: '1.0.0',
          endpoints: 8
        },
        {
          name: 'User Service',
          status: userService.status === 'fulfilled' ? 'healthy' : 'unhealthy',
          uptime: userService.status === 'fulfilled' ? 99.7 : 0,
          responseTime: 25,
          lastCheck: new Date().toISOString(),
          version: '1.0.0',
          endpoints: 6
        },
        {
          name: 'Resume Service',
          status: resumeService.status === 'fulfilled' ? 'healthy' : 'unhealthy',
          uptime: resumeService.status === 'fulfilled' ? 99.6 : 0,
          responseTime: 40,
          lastCheck: new Date().toISOString(),
          version: '1.0.0',
          endpoints: 10
        }
      ];

      const systemHealth = services.every(s => s.status === 'healthy') ? 'healthy' : 
                          services.some(s => s.status === 'unhealthy') ? 'unhealthy' : 'degraded';

      setOverview({
        totalUsers: 1250,
        activeUsers: 890,
        totalJobs: 15600,
        activeJobs: 12300,
        totalApplications: 45600,
        systemHealth,
        services,
        apiKeys: {
          adzuna: true,
          indeed: true,
          linkedin: false,
          glassdoor: false
        }
      });

    } catch (error) {
      console.error('Failed to fetch system overview:', error);
      toast.error('Failed to load system overview');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchSystemOverview();
    const interval = setInterval(fetchSystemOverview, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, []);

  const handleServiceAction = async (serviceName: string, action: string) => {
    try {
      toast.loading(`Performing ${action} on ${serviceName}...`);
      
      // Simulate service action
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      toast.success(`${action} completed for ${serviceName}`);
      await fetchSystemOverview();
    } catch (error) {
      toast.error(`Failed to ${action} ${serviceName}`);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>Loading system overview...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="min-w-0 flex-1">
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 flex items-center gap-2">
            <Shield className="h-6 w-6 sm:h-8 sm:w-8 text-red-600 flex-shrink-0" />
            <span className="truncate">ROOT Admin Dashboard</span>
          </h1>
          <p className="text-gray-600 mt-1">
            System-wide control and monitoring for all services
          </p>
        </div>
        <Button
          onClick={fetchSystemOverview}
          disabled={refreshing}
          className="flex items-center gap-2 w-full sm:w-auto"
        >
          <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* System Health Alert */}
      {overview?.systemHealth !== 'healthy' && (
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            System health is {overview?.systemHealth}. Some services may be experiencing issues.
          </AlertDescription>
        </Alert>
      )}

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overview?.totalUsers.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              {overview?.activeUsers} active now
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Jobs</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overview?.totalJobs.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              {overview?.activeJobs} active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Applications</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overview?.totalApplications.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              All time
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">System Health</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              {overview?.systemHealth === 'healthy' ? (
                <CheckCircle className="h-5 w-5 text-green-600" />
              ) : overview?.systemHealth === 'degraded' ? (
                <AlertTriangle className="h-5 w-5 text-yellow-600" />
              ) : (
                <XCircle className="h-5 w-5 text-red-600" />
              )}
              <span className="text-lg font-bold capitalize">
                {overview?.systemHealth}
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Services Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Server className="h-5 w-5" />
            Services Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {overview?.services.map((service) => (
              <div key={service.name} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    {service.status === 'healthy' ? (
                      <CheckCircle className="h-5 w-5 text-green-600" />
                    ) : service.status === 'degraded' ? (
                      <AlertTriangle className="h-5 w-5 text-yellow-600" />
                    ) : (
                      <XCircle className="h-5 w-5 text-red-600" />
                    )}
                    <span className="font-medium">{service.name}</span>
                  </div>
                  <div className="text-sm text-gray-600">
                    {service.endpoints} endpoints • v{service.version}
                  </div>
                </div>
                <div className="flex items-center gap-4">
                  <div className="text-right">
                    <div className="text-sm font-medium">{service.uptime}% uptime</div>
                    <div className="text-xs text-gray-600">{service.responseTime}ms avg</div>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleServiceAction(service.name, 'restart')}
                    >
                      <RefreshCw className="h-3 w-3" />
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleServiceAction(service.name, 'logs')}
                    >
                      <Monitor className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* API Keys Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            External API Keys Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {Object.entries(overview?.apiKeys || {}).map(([key, status]) => (
              <div key={key} className="flex items-center justify-between p-3 border rounded-lg">
                <span className="text-sm font-medium capitalize">{key}</span>
                {status ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-500" />
                )}
              </div>
            ))}
          </div>
          {overview?.apiKeys && Object.values(overview.apiKeys).every(status => !status) && (
            <Alert className="mt-4">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                No API keys configured. External job sync will not work.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Quick Actions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button
              onClick={() => handleServiceAction('All Services', 'sync jobs')}
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              Sync All Jobs
            </Button>
            <Button
              onClick={() => handleServiceAction('System', 'cleanup')}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Database className="h-4 w-4" />
              Cleanup Database
            </Button>
            <Button
              onClick={() => handleServiceAction('System', 'backup')}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Globe className="h-4 w-4" />
              System Backup
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default RootAdminDashboard;
