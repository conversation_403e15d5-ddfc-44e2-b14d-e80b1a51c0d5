import { Schema, model } from 'mongoose';

const userSchema = new Schema(
  {
    email: {
      type: String,
      required: true,
      unique: true,
      lowercase: true,
      trim: true,
    },
    firstName: {
      type: String,
      required: true,
      trim: true,
    },
    lastName: {
      type: String,
      required: true,
      trim: true,
    },
    avatar: String,
    role: {
      type: String,
      enum: ['admin', 'user', 'premium', 'enterprise'],
      default: 'user',
    },
    subscriptionTier: {
      type: String,
      enum: ['free', 'basic', 'premium', 'enterprise'],
      default: 'free',
    },
    isVerified: {
      type: Boolean,
      default: false,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    lastLoginAt: Date,
    profile: {
      bio: String,
      phoneNumber: String,
      location: {
        country: String,
        state: String,
        city: String,
        zipCode: String,
        coordinates: {
          lat: Number,
          lng: Number,
        },
        remote: Boolean,
      },
      website: String,
      linkedin: String,
      github: String,
      portfolio: String,
      currentPosition: String,
      currentCompany: String,
      yearsOfExperience: Number,
      expectedSalary: {
        min: Number,
        max: Number,
        currency: {
          type: String,
          default: 'USD',
        },
      },
      dateOfBirth: Date,
      nationality: String,
      languages: [
        {
          language: String,
          proficiency: {
            type: String,
            enum: ['basic', 'conversational', 'fluent', 'native'],
          },
        },
      ],
      skills: [
        {
          name: String,
          level: {
            type: String,
            enum: ['beginner', 'intermediate', 'advanced', 'expert'],
          },
          verified: Boolean,
          yearsOfExperience: Number,
        },
      ],
      education: [
        {
          institution: String,
          degree: String,
          field: String,
          startDate: Date,
          endDate: Date,
          gpa: Number,
          description: String,
        },
      ],
      experience: [
        {
          company: String,
          position: String,
          startDate: Date,
          endDate: Date,
          current: Boolean,
          description: String,
          skills: [String],
          achievements: [String],
        },
      ],
      profileVisibility: {
        type: String,
        enum: ['public', 'private', 'connections'],
        default: 'private',
      },
      searchable: {
        type: Boolean,
        default: true,
      },
    },
    preferences: {
      notifications: {
        email: {
          type: Boolean,
          default: true,
        },
        push: {
          type: Boolean,
          default: true,
        },
        sms: {
          type: Boolean,
          default: false,
        },
      },
      privacy: {
        profileVisibility: {
          type: String,
          enum: ['public', 'private', 'connections'],
          default: 'private',
        },
        showEmail: {
          type: Boolean,
          default: false,
        },
        showPhone: {
          type: Boolean,
          default: false,
        },
      },
      jobAlerts: {
        frequency: {
          type: String,
          enum: ['daily', 'weekly', 'monthly'],
          default: 'weekly',
        },
        keywords: [String],
        locations: [String],
        salaryRange: {
          min: Number,
          max: Number,
        },
      },
    },
    analytics: {
      profileViews: { type: Number, default: 0 },
      searchAppearances: { type: Number, default: 0 },
      applicationsSent: { type: Number, default: 0 },
      interviewsScheduled: { type: Number, default: 0 },
      offersReceived: { type: Number, default: 0 },
      loginStreak: { type: Number, default: 0 },
      totalLogins: { type: Number, default: 0 },
      averageSessionDuration: { type: Number, default: 0 },
      lastActiveAt: Date,
      featuresUsed: [String],
      premiumFeaturesUsed: [String],
      responseRate: { type: Number, default: 0 },
      interviewRate: { type: Number, default: 0 },
      offerRate: { type: Number, default: 0 },
    },
  },
  {
    timestamps: true,
    versionKey: false,
    toJSON: {
      transform(doc: unknown, ret: Record<string, unknown>): Record<string, unknown> {
        ret.id = (ret._id as { toString: () => string }).toString();
        delete ret._id;
        return ret;
      },
    },
  }
);

// Indexes
userSchema.index({ 'analytics.lastActiveAt': -1 });

export const User = model('User', userSchema);
