# DigitalOcean JWT Authentication Fix

## 🚨 Critical Issue Identified

The 401 authentication errors are caused by **JWT issuer validation mismatch** and potentially inconsistent environment variables across services in DigitalOcean.

### Root Cause
1. **Missing Issuer Validation**: New JWT middleware wasn't validating the issuer `'job-platform-auth'`
2. **Missing Session Creation**: Google OAuth callback wasn't creating sessions in database
3. **Refresh Token Verification**: Auth-service wasn't validating issuer in refresh token verification
4. **Environment Variables**: JWT_SECRET must be identical across all services

## 🛠️ Fix Applied

### 1. Updated JWT Middleware
Fixed all three services (`user-service`, `resume-service`, `job-service`) to properly validate JWT tokens:

```typescript
// Before (BROKEN)
const decoded = jwt.verify(token, jwtConfig.secret) as JWTPayload;

// After (FIXED)
const decoded = jwt.verify(token, jwtConfig.secret, {
  issuer: 'job-platform-auth', // Must match auth-service issuer
}) as JWTPayload;
```

### 2. Files Modified
- `services/user-service/src/middleware/auth.middleware.ts` - Added issuer validation
- `services/resume-service/src/middleware/auth.middleware.ts` - Added issuer validation
- `services/job-service/src/middleware/auth.middleware.ts` - Added issuer validation
- `services/auth-service/src/utils/encryption.ts` - Fixed refresh token verification
- `services/auth-service/src/controllers/auth.controller.ts` - Fixed session creation in OAuth

## 🔧 DigitalOcean Environment Configuration

### Required Environment Variables
Ensure these variables are **IDENTICAL** across all services in DigitalOcean App Platform:

```bash
# JWT Configuration (MUST BE IDENTICAL ACROSS ALL SERVICES)
JWT_SECRET=your-production-jwt-secret-key-here
JWT_REFRESH_SECRET=your-production-refresh-secret-key-here
JWT_ACCESS_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# Database Configuration
MONGODB_URI=mongodb+srv://username:<EMAIL>/job_platform
REDIS_URL=redis://redis-cluster-url:6379

# Service URLs (Internal DigitalOcean networking)
AUTH_SERVICE_URL=http://resume-automator-services-auth-s:3001
USER_SERVICE_URL=http://resume-automator-services-user-s:3002
JOB_SERVICE_URL=http://resume-automator-services-job-se:3003
RESUME_SERVICE_URL=http://resume-automator-services-resume:3005

# Google OAuth (Auth Service Only)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_CALLBACK_URL=https://jobs-app-ydwim.ondigitalocean.app/api/v1/auth/google/callback

# Frontend URL (Auth Service Only)
FRONTEND_URL=https://jobs-app-ydwim.ondigitalocean.app

# CORS Configuration
CORS_ORIGIN=https://jobs-app-ydwim.ondigitalocean.app

# Environment
NODE_ENV=production
LOG_LEVEL=info
```

### ⚠️ Critical Security Note
**NEVER use default JWT secrets in production!**
- Generate strong, unique secrets for JWT_SECRET and JWT_REFRESH_SECRET
- Use the same secrets across ALL services
- Store them securely in DigitalOcean App Platform environment variables

## 🚀 Deployment Steps

### 1. Update Environment Variables in DigitalOcean
For **EACH** service (auth-service, user-service, job-service, resume-service, api-gateway):

1. Go to DigitalOcean App Platform
2. Select your app
3. Go to Settings → Environment Variables
4. Add/Update the variables listed above
5. Ensure JWT_SECRET and JWT_REFRESH_SECRET are **IDENTICAL** across all services

### 2. Rebuild and Deploy Services
After updating environment variables:

```bash
# Build all services locally (optional, for testing)
cd services/user-service && npm run build
cd ../resume-service && npm run build  
cd ../job-service && npm run build
cd ../auth-service && npm run build

# Deploy to DigitalOcean (automatic on git push)
git add .
git commit -m "Fix JWT authentication with proper issuer validation"
git push origin main
```

### 3. Verify Deployment
1. Wait for all services to redeploy
2. Test Google OAuth login
3. Check that `/users/analytics` returns data instead of 401
4. Verify token refresh works

## 🧪 Testing & Debugging

### 1. Use Debug Script
```bash
# Get tokens from browser localStorage after login
node debug-jwt-auth.js <access_token> <refresh_token>

# Test endpoints directly
node debug-jwt-auth.js --test-endpoints <access_token>
```

### 2. Browser Console Testing
After Google OAuth login, check localStorage:
```javascript
// In browser console
console.log('Access Token:', localStorage.getItem('accessToken'));
console.log('Refresh Token:', localStorage.getItem('refreshToken'));
console.log('User:', localStorage.getItem('user'));
```

### 3. Manual API Testing
```bash
# Test analytics endpoint
curl -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
     https://jobs-app-ydwim.ondigitalocean.app/api/v1/users/analytics

# Test refresh endpoint  
curl -X POST \
     -H "Content-Type: application/json" \
     -d '{"refreshToken":"YOUR_REFRESH_TOKEN"}' \
     https://jobs-app-ydwim.ondigitalocean.app/api/v1/auth/refresh
```

## 🔍 Expected Behavior After Fix

### ✅ Working Scenarios
- Google OAuth login completes successfully
- User is redirected to dashboard with valid tokens
- `/users/analytics` returns 200 with analytics data
- `/resumes/analytics` returns 200 with resume data
- `/applications/analytics` returns 200 with application data
- Token refresh works automatically on 401 errors
- Jobs page loads without "Cannot read properties of undefined" errors

### ❌ Error Scenarios (Should be fixed)
- ~~401 Unauthorized on analytics endpoints~~
- ~~401 Unauthorized on refresh endpoint~~
- ~~"User ID is required" errors~~
- ~~Frontend JavaScript errors about undefined properties~~

## 🔧 Troubleshooting

### If Still Getting 401 Errors:

1. **Check Environment Variables**:
   - Verify JWT_SECRET is identical across all services
   - Ensure no typos in environment variable names
   - Confirm variables are set in production environment

2. **Check Service Logs**:
   - Look for JWT verification errors in service logs
   - Check for "Invalid token" or "Token expired" messages

3. **Verify Token Format**:
   - Use debug script to analyze token structure
   - Ensure tokens have correct issuer: 'job-platform-auth'

4. **Test Token Generation**:
   - Verify auth-service is generating tokens correctly
   - Check Google OAuth callback is working

### If Jobs Page Still Shows Errors:

1. **Check API Responses**: Ensure `/jobs` endpoint returns proper data structure
2. **Frontend Error Handling**: Verify frontend handles empty/undefined responses
3. **Network Issues**: Check if API Gateway is properly routing requests

## 📋 Verification Checklist

- [ ] JWT_SECRET identical across all services
- [ ] JWT_REFRESH_SECRET identical across all services  
- [ ] All services rebuilt and deployed
- [ ] Google OAuth login works
- [ ] `/users/analytics` returns 200
- [ ] `/auth/refresh` returns 200
- [ ] Jobs page loads without errors
- [ ] Token refresh happens automatically
- [ ] No console errors in browser

## 🆘 Emergency Rollback

If issues persist, temporarily revert analytics endpoints to accept userId query parameter:

```typescript
// Temporary fallback in analytics endpoints
const userId = req.user?.id || req.query.userId;
if (!userId) {
  return res.status(400).json(ResponseUtil.error('User ID is required', 400));
}
```

This allows frontend to work while debugging JWT issues.
