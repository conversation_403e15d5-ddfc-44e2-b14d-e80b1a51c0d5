info: MongoDB connected successfully {"service":"job-service","timestamp":"2025-09-21T10:47:57.562Z"}
info: 💼 Job Service running on port 3003 {"service":"job-service","timestamp":"2025-09-21T10:47:57.567Z"}
info: 🔄 Job Service shutting down... {"service":"job-service","timestamp":"2025-09-21T10:51:15.984Z"}
warn: MongoDB disconnected {"service":"job-service","timestamp":"2025-09-21T10:51:16.005Z"}
info: MongoDB disconnected successfully {"service":"job-service","timestamp":"2025-09-21T10:51:16.005Z"}
info: ✅ Job Service shutdown completed {"service":"job-service","timestamp":"2025-09-21T10:51:16.005Z"}
                                                info: ::1 - - [21/Sep/2025:10:48:17 +0000] "GET /health HTTP/1.1" 200 405 "-" "node" {"service":"job-service","timestamp":"2025-09-21T10:48:17.737Z"}
info: ::1 - - [21/Sep/2025:10:48:47 +0000] "GET /health HTTP/1.1" 200 405 "-" "node" {"service":"job-service","timestamp":"2025-09-21T10:48:47.722Z"}
info: ::1 - - [21/Sep/2025:10:49:17 +0000] "GET /health HTTP/1.1" 200 404 "-" "node" {"service":"job-service","timestamp":"2025-09-21T10:49:17.721Z"}
info: ::1 - - [21/Sep/2025:10:49:47 +0000] "GET /health HTTP/1.1" 200 405 "-" "node" {"service":"job-service","timestamp":"2025-09-21T10:49:47.721Z"}
info: ::1 - - [21/Sep/2025:10:50:17 +0000] "GET /health HTTP/1.1" 200 405 "-" "node" {"service":"job-service","timestamp":"2025-09-21T10:50:17.724Z"}
info: ::1 - - [21/Sep/2025:10:50:47 +0000] "GET /health HTTP/1.1" 200 405 "-" "node" {"service":"job-service","timestamp":"2025-09-21T10:50:47.727Z"}
info: ::1 - - [21/Sep/2025:10:52:23 +0000] "GET /health HTTP/1.1" 200 405 "-" "node" {"service":"job-service","timestamp":"2025-09-21T10:52:23.478Z"}
