#!/usr/bin/env node

/**
 * Refresh Token Debug Script
 * Specifically designed to debug the "Invalid refresh token" error
 */

const jwt = require('jsonwebtoken');

// Environment variables
const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key';
const JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET || 'your-super-secret-refresh-key';

console.log('🔍 Refresh Token Debug Tool\n');

function debugRefreshToken(refreshToken) {
  console.log('🔍 Debugging refresh token...\n');
  
  try {
    // Step 1: Decode without verification
    console.log('📋 Step 1: Decoding token structure...');
    const decoded = jwt.decode(refreshToken, { complete: true });
    
    if (!decoded) {
      console.log('❌ Failed to decode token - invalid format');
      return;
    }
    
    console.log('✅ Token decoded successfully');
    console.log('Header:', JSON.stringify(decoded.header, null, 2));
    console.log('Payload:', JSON.stringify(decoded.payload, null, 2));
    
    const payload = decoded.payload;
    
    // Step 2: Check required fields
    console.log('\n📋 Step 2: Checking required fields...');
    const requiredFields = ['userId', 'sessionId', 'iss', 'exp', 'iat'];
    const missingFields = requiredFields.filter(field => !payload[field]);
    
    if (missingFields.length > 0) {
      console.log(`❌ Missing required fields: ${missingFields.join(', ')}`);
    } else {
      console.log('✅ All required fields present');
    }
    
    // Step 3: Check issuer
    console.log('\n📋 Step 3: Checking issuer...');
    if (payload.iss === 'job-platform-auth') {
      console.log('✅ Correct issuer: job-platform-auth');
    } else {
      console.log(`❌ Incorrect issuer: ${payload.iss} (expected: job-platform-auth)`);
    }
    
    // Step 4: Check expiration
    console.log('\n📋 Step 4: Checking expiration...');
    const now = Math.floor(Date.now() / 1000);
    if (payload.exp > now) {
      const timeLeft = payload.exp - now;
      console.log(`✅ Token valid for ${Math.floor(timeLeft / 3600)} hours, ${Math.floor((timeLeft % 3600) / 60)} minutes`);
    } else {
      console.log('❌ Token expired');
      const timeExpired = now - payload.exp;
      console.log(`   Expired ${Math.floor(timeExpired / 3600)} hours, ${Math.floor((timeExpired % 3600) / 60)} minutes ago`);
    }
    
    // Step 5: Check sessionId format
    console.log('\n📋 Step 5: Checking sessionId...');
    if (typeof payload.sessionId === 'string') {
      console.log(`✅ SessionId is string: ${payload.sessionId}`);
    } else {
      console.log(`❌ SessionId is not string: ${typeof payload.sessionId} - ${payload.sessionId}`);
    }
    
    // Step 6: Check userId format
    console.log('\n📋 Step 6: Checking userId...');
    if (typeof payload.userId === 'string') {
      console.log(`✅ UserId is string: ${payload.userId}`);
    } else {
      console.log(`❌ UserId is not string: ${typeof payload.userId} - ${payload.userId}`);
    }
    
    // Step 7: Try verification with different approaches
    console.log('\n📋 Step 7: Testing verification approaches...');
    
    // Test 1: With issuer validation (NEW WAY)
    try {
      jwt.verify(refreshToken, JWT_REFRESH_SECRET, {
        issuer: 'job-platform-auth'
      });
      console.log('✅ Verification successful with issuer validation');
    } catch (error) {
      console.log(`❌ Verification failed with issuer validation: ${error.message}`);
      
      // Check if it's a secret issue
      if (error.message.includes('invalid signature')) {
        console.log('   🔑 This suggests JWT_REFRESH_SECRET mismatch between services');
      }
      
      // Check if it's an issuer issue
      if (error.message.includes('issuer')) {
        console.log('   🏷️  This suggests issuer mismatch');
      }
      
      // Check if it's expiration
      if (error.message.includes('expired')) {
        console.log('   ⏰ Token has expired');
      }
    }
    
    // Test 2: Without issuer validation (OLD WAY)
    try {
      jwt.verify(refreshToken, JWT_REFRESH_SECRET);
      console.log('✅ Verification successful without issuer validation');
      console.log('   ⚠️  This means the issuer validation is the problem');
    } catch (error) {
      console.log(`❌ Verification failed without issuer validation: ${error.message}`);
      
      if (error.message.includes('invalid signature')) {
        console.log('   🔑 JWT_REFRESH_SECRET is definitely wrong');
      }
    }
    
    // Test 3: With wrong secret (to confirm secret is correct)
    try {
      jwt.verify(refreshToken, 'wrong-secret');
      console.log('⚠️  Verification successful with wrong secret (this should not happen)');
    } catch (error) {
      console.log('✅ Verification correctly failed with wrong secret');
    }
    
    // Step 8: Environment check
    console.log('\n📋 Step 8: Environment configuration...');
    console.log(`JWT_REFRESH_SECRET: ${JWT_REFRESH_SECRET === 'your-super-secret-refresh-key' ? '⚠️  Using default' : '✅ Custom value'}`);
    
    if (JWT_REFRESH_SECRET === 'your-super-secret-refresh-key') {
      console.log('❌ WARNING: Using default JWT_REFRESH_SECRET!');
      console.log('   This will cause verification failures in production');
    }
    
    return payload;
    
  } catch (error) {
    console.log(`❌ Failed to decode token: ${error.message}`);
    return null;
  }
}

async function testRefreshEndpoint(refreshToken) {
  console.log('\n🌐 Testing refresh endpoint...');
  
  try {
    const response = await fetch('https://jobs-app-ydwim.ondigitalocean.app/api/v1/auth/refresh', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ refreshToken })
    });
    
    console.log(`Status: ${response.status} ${response.statusText}`);
    
    const responseText = await response.text();
    
    try {
      const jsonData = JSON.parse(responseText);
      console.log('Response:', JSON.stringify(jsonData, null, 2));
    } catch {
      console.log('Response (text):', responseText);
    }
    
    if (response.status === 401) {
      console.log('\n🔍 401 Error Analysis:');
      console.log('Possible causes:');
      console.log('1. JWT_REFRESH_SECRET mismatch between auth-service and token generation');
      console.log('2. Session not found in database (OAuth callback issue)');
      console.log('3. User not found or inactive');
      console.log('4. Token expired');
      console.log('5. Issuer validation failing');
    }
    
  } catch (error) {
    console.log(`❌ Request failed: ${error.message}`);
  }
}

// Main function
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log('📝 Usage:');
    console.log('  node debug-refresh-token.js <refresh_token>');
    console.log('\n💡 Get refresh token from browser localStorage after Google OAuth login');
    return;
  }
  
  const refreshToken = args[0];
  
  console.log('🔧 FIXES APPLIED:');
  console.log('✅ Added issuer validation to refresh token verification');
  console.log('✅ Fixed session creation in Google OAuth callback');
  console.log('✅ Updated JWT middleware issuer validation\n');
  
  // Debug the token
  const payload = debugRefreshToken(refreshToken);
  
  if (payload) {
    // Test the endpoint
    await testRefreshEndpoint(refreshToken);
  }
  
  console.log('\n🎯 NEXT STEPS:');
  console.log('1. Ensure JWT_REFRESH_SECRET is identical across all services');
  console.log('2. Check DigitalOcean environment variables');
  console.log('3. Verify services are using the updated compiled code');
  console.log('4. Check if session exists in database for the sessionId');
}

if (require.main === module) {
  main().catch(console.error);
}
