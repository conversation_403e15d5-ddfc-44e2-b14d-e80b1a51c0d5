{"name": "job-application-platform-backend", "version": "1.0.0", "description": "Production-ready backend for job application platform with microservices architecture", "type": "module", "main": "index.js", "engines": {"node": ">=22.18.0", "npm": ">=10.0.0"}, "scripts": {"dev": "concurrently \"npm run dev:api-gateway\" \"npm run dev:auth\" \"npm run dev:user\" \"npm run dev:job\" \"npm run dev:resume\" \"npm run dev:analytics\" \"npm run dev:notification\" \"npm run dev:integration\" \"npm run dev:payment\"", "dev:api-gateway": "cd services/api-gateway && npm run dev", "dev:auth": "cd services/auth-service && npm run dev", "dev:user": "cd services/user-service && npm run dev", "dev:job": "cd services/job-service && npm run dev", "dev:resume": "cd services/resume-service && npm run dev", "dev:analytics": "cd services/analytics-service && npm run dev", "dev:notification": "cd services/notification-service && npm run dev", "dev:integration": "cd services/integration-service && npm run dev", "dev:payment": "cd services/payment-service && npm run dev", "build": "npm run build --workspaces", "test": "npm run test --workspaces", "test:coverage": "npm run test:coverage --workspaces", "lint": "npm run lint --workspaces", "lint:fix": "npm run lint:fix --workspaces", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "setup": "npm install && for service in services/*; do cd $service && npm install && cd ../..; done", "seed": "npm run seed --workspaces"}, "workspaces": ["services/*"], "devDependencies": {"@types/node": "^24.5.2", "@typescript-eslint/eslint-plugin": "^8.44.0", "@typescript-eslint/parser": "^8.44.0", "concurrently": "^9.2.1", "eslint": "^9.36.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.4", "husky": "^9.1.7", "lint-staged": "^16.1.6", "prettier": "^3.6.2", "typescript": "^5.9.2"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{ts,js}": ["eslint --fix", "prettier --write", "git add"]}, "keywords": ["job-application", "microservices", "nodejs", "typescript", "mongodb", "express", "oauth2", "resume-processing"], "author": "Job Application Platform Team", "license": "MIT"}