import { Schema, model } from 'mongoose';

const applicationSchema = new Schema(
  {
    jobId: {
      type: String,
      required: true,
    },
    userId: {
      type: String,
      required: true,
    },
    resumeId: {
      type: String,
      required: true,
    },
    status: {
      type: String,
      enum: ['pending', 'reviewed', 'shortlisted', 'interviewed', 'rejected', 'accepted', 'withdrawn'],
      default: 'pending',
    },
    appliedAt: {
      type: Date,
      default: Date.now,
    },
    notes: String,
    coverLetter: String,
    customAnswers: Schema.Types.Mixed,
    interviewScheduledAt: Date,
    interviewNotes: String,
    rejectionReason: String,
    salaryExpectation: Number,
    availabilityDate: Date,
    isReferred: {
      type: Boolean,
      default: false,
    },
    referredBy: String,
    source: {
      type: String,
      enum: ['direct', 'job-board', 'referral', 'recruiter'],
      default: 'direct',
    },
    trackingData: {
      views: {
        type: Number,
        default: 0,
      },
      downloads: {
        type: Number,
        default: 0,
      },
      lastViewedAt: Date,
    },
  },
  {
    timestamps: true,
    versionKey: false,
    toJSON: {
      transform(doc: unknown, ret: Record<string, unknown>): Record<string, unknown> {
        ret.id = (ret._id as { toString: () => string }).toString();
        delete ret._id;
        return ret;
      },
    },
  }
);

// Indexes
applicationSchema.index({ userId: 1 });
applicationSchema.index({ jobId: 1 });
applicationSchema.index({ status: 1 });
applicationSchema.index({ appliedAt: -1 });

export const Application = model('Application', applicationSchema);
