# Resume-Automator: Product Requirements Document

> **Last Updated**: 2025-09-30  
> **Version**: 1.0  
> **Status**: Active Development

---

## WHY - VISION & PURPOSE

### What problem are you solving and for whom?

**The Problem**: Job seekers face a time-consuming, inefficient, and often frustrating application process. They must:
- Manually search across multiple job boards (LinkedIn, Indeed, Glassdoor)
- <PERSON><PERSON> resumes for each position to pass Applicant Tracking Systems (ATS)
- Track applications across different platforms without centralized visibility
- Struggle with low response rates due to poorly optimized resumes
- Miss opportunities due to delayed applications or lack of job alerts
- Lack insights into their application performance and market positioning

**Target Users**:
- **Primary**: Active job seekers (entry-level to senior professionals) seeking efficiency and better outcomes
- **Secondary**: Career changers needing resume optimization and market intelligence
- **Tertiary**: Recruiters and hiring managers (future enterprise tier)

### What does your application do?

Resume-Automator is an **AI-powered job application automation platform** that streamlines the entire job search lifecycle:

1. **Intelligent Job Aggregation**: Automatically pulls jobs from multiple sources (<PERSON><PERSON><PERSON>, Indeed, LinkedIn, Glassdoor)
2. **Resume Intelligence**: AI-powered resume parsing, ATS optimization scoring, and job-specific tailoring
3. **Application Automation**: One-click applications with auto-filled forms and optimized cover letters
4. **Centralized Tracking**: Complete application lifecycle management with status tracking and analytics
5. **Market Intelligence**: Real-time insights on application success rates, response times, and competitive positioning

### Who will use it?

**Primary User Personas**:

1. **Sarah - The Active Job Seeker** (25-35, Mid-level Professional)
   - Applying to 20-50 jobs per week
   - Needs: Speed, ATS optimization, application tracking
   - Pain: Manual resume tailoring, tracking across platforms
   - Goal: Land interviews faster with higher response rates

2. **Marcus - The Career Changer** (35-45, Transitioning Professional)
   - Needs: Resume optimization, skill highlighting, market insights
   - Pain: Uncertain how to position experience for new industry
   - Goal: Effectively communicate transferable skills

3. **Emily - The Recent Graduate** (22-25, Entry-level)
   - Needs: Resume building, application volume, learning resources
   - Pain: Limited experience, competitive market
   - Goal: Get first professional role quickly

### Why will they use it instead of alternatives?

**Unique Value Proposition**:

| Feature | Resume-Automator | LinkedIn Premium | Indeed | Traditional Job Boards |
|---------|------------------|------------------|--------|----------------------|
| Multi-platform job aggregation | ✅ | ❌ | ❌ | ❌ |
| AI-powered ATS optimization | ✅ | Limited | ❌ | ❌ |
| Automated application submission | ✅ | ❌ | Limited | ❌ |
| Centralized tracking dashboard | ✅ | Limited | Limited | ❌ |
| Job-specific resume tailoring | ✅ | ❌ | ❌ | ❌ |
| Real-time analytics & insights | ✅ | Limited | Limited | ❌ |
| Cover letter generation | ✅ | ❌ | ❌ | ❌ |

**Competitive Advantages**:
1. **Time Savings**: 10x faster application process (5 min → 30 sec per application)
2. **Higher Success Rates**: ATS-optimized resumes increase interview callbacks by 40%
3. **Unified Experience**: Single platform vs. juggling 5+ job boards
4. **Intelligence Layer**: AI-driven insights competitors don't offer
5. **Scalability**: Apply to 100+ jobs in the time it takes to manually apply to 10

---

## WHAT - CORE REQUIREMENTS

### What must your application do?

**Critical System Requirements** (Must-Have for MVP):

1. **User Management**
   - System must allow users to register via email/password or Google OAuth
   - System must verify email addresses before full account activation
   - System must support role-based access (user, premium, admin, enterprise)
   - System must maintain secure session management with JWT tokens
   - System must enforce subscription tier limits (free: 5 apps/month, premium: unlimited)

2. **Resume Management**
   - Users must be able to upload resumes in PDF, DOC, DOCX formats
   - System must parse resume content extracting: personal info, experience, education, skills, certifications
   - System must analyze resumes for ATS compatibility (scoring 0-100)
   - System must identify resume strengths, weaknesses, and missing keywords
   - Users must be able to maintain multiple resume versions
   - System must store resumes securely in AWS S3 or compatible storage

3. **Job Aggregation & Search**
   - System must fetch jobs from external APIs (Adzuna, Indeed) on scheduled intervals
   - System must deduplicate jobs from multiple sources
   - Users must be able to search jobs by: keywords, location, type, company, skills
   - System must support pagination and filtering (10-50 results per page)
   - Users must be able to save/bookmark jobs for later review

4. **Application Management**
   - Users must be able to apply to jobs with selected resume
   - System must track application status: pending, reviewed, shortlisted, interviewed, rejected, accepted, withdrawn
   - Users must be able to add notes, cover letters, and custom answers per application
   - System must record application metadata: applied date, source, referral info
   - Users must be able to withdraw applications with reason tracking

5. **Analytics & Insights**
   - System must calculate user metrics: applications sent, interviews scheduled, offers received
   - System must compute success rates: response rate, interview rate, offer rate
   - Users must be able to view dashboard with key statistics
   - System must track resume performance scores over time
   - System must provide job market insights (top companies, locations, skills)

### What actions need to happen?

**Key User Workflows**:

1. **Onboarding Flow**
   - User registers → Email verification → Profile setup → Resume upload → Dashboard access
   - Expected time: 5-10 minutes
   - Success criteria: User has verified account with at least one resume uploaded

2. **Job Discovery Flow**
   - User searches jobs → Filters results → Views job details → Saves interesting jobs → Applies with optimized resume
   - Expected time: 2-5 minutes per application
   - Success criteria: Application submitted with ATS-optimized resume

3. **Resume Optimization Flow**
   - User uploads resume → System parses content → AI analyzes ATS compatibility → User reviews suggestions → User applies improvements → Re-analysis shows improved score
   - Expected time: 10-15 minutes
   - Success criteria: ATS score improves by 15+ points

4. **Application Tracking Flow**
   - User views applications dashboard → Filters by status → Updates application status → Adds interview notes → Tracks outcomes
   - Expected time: 2-3 minutes daily
   - Success criteria: All applications have current status and notes

5. **Analytics Review Flow**
   - User accesses analytics dashboard → Reviews success metrics → Identifies improvement areas → Adjusts strategy
   - Expected time: 5-10 minutes weekly
   - Success criteria: User understands performance and takes action

### What should the outcomes be?

**For Users**:
- **Efficiency**: Apply to 50+ jobs per week (vs. 10-15 manually)
- **Quality**: 80+ ATS scores on optimized resumes (vs. 60-70 unoptimized)
- **Success**: 15-20% interview callback rate (vs. 5-10% industry average)
- **Visibility**: 100% application tracking (vs. scattered notes/emails)
- **Insights**: Data-driven job search strategy (vs. guesswork)

**For Business**:
- **User Acquisition**: 10,000 registered users in first 6 months
- **Engagement**: 60% weekly active users (WAU)
- **Conversion**: 15% free-to-premium conversion rate
- **Retention**: 80% monthly retention for premium users
- **Revenue**: $50K MRR by month 12

---

## HOW - PLANNING & IMPLEMENTATION

### What are the required stack components?

**Current Technology Stack** (Production-Ready):

**Frontend**:
- **Framework**: React 18 with TypeScript 5.6+
- **Build Tool**: Vite (fast HMR, optimized builds)
- **Styling**: Tailwind CSS 3.x (utility-first, responsive)
- **State Management**: React Query (TanStack Query) for server state, Context API for auth
- **Routing**: React Router v6
- **HTTP Client**: Axios with interceptors for token refresh
- **Deployment**: DigitalOcean App Platform (static site hosting)

**Backend Microservices**:
- **Runtime**: Node.js 22.x
- **Language**: TypeScript 5.6+
- **Framework**: Express.js 5.x
- **Architecture**: True microservices (no shared libraries)
- **Services**:
  - API Gateway (Port 3000) - Request routing, rate limiting
  - Auth Service (Port 3001) - JWT auth, OAuth, sessions
  - User Service (Port 3002) - Profile management
  - Job Service (Port 3003) - Job CRUD, applications, external API sync
  - Resume Service (Port 3005) - Upload, parsing, ATS analysis

**Database & Storage**:
- **Primary Database**: MongoDB Atlas 7.0+ (managed, auto-scaling)
- **ORM**: Mongoose (schema validation, middleware)
- **File Storage**: AWS S3 (resume storage, scalable)
- **Caching**: Redis 7.4+ (sessions, rate limiting) - Currently disabled, planned for production

**Authentication & Security**:
- **Auth Strategy**: JWT (access + refresh tokens)
- **OAuth Providers**: Google OAuth 2.0
- **Password Hashing**: bcrypt (10 rounds)
- **Security Headers**: Helmet.js
- **Rate Limiting**: Express rate limit (tier-based)
- **CORS**: Configurable origins

**External Integrations**:
- **Job APIs**: Adzuna API, Indeed Publisher API
- **Email**: SendGrid (verification, notifications) - Currently mocked
- **Payment**: Stripe (subscription management) - Planned
- **Analytics**: Custom analytics service - Planned

**DevOps & Infrastructure**:
- **Containerization**: Docker + Docker Compose
- **Deployment**: DigitalOcean App Platform
- **CI/CD**: GitHub Actions (planned)
- **Monitoring**: Winston logging, health checks
- **Environment**: Development, Production configs

### What are the system requirements?

**Performance Requirements**:
- **API Response Time**: < 200ms for 95th percentile
- **Page Load Time**: < 2 seconds for initial load
- **Resume Upload**: Support files up to 10MB
- **Concurrent Users**: Handle 1,000 simultaneous users
- **Job Sync**: Process 1,000+ jobs per sync operation
- **Database Queries**: < 100ms for indexed queries

**Security Requirements**:
- **Authentication**: Multi-factor authentication (MFA) for premium users
- **Data Encryption**: TLS 1.3 for data in transit, AES-256 for data at rest
- **Password Policy**: Minimum 8 characters, complexity requirements
- **Session Management**: 15-minute access token, 7-day refresh token
- **Rate Limiting**: 100 req/15min (free), 1000 req/15min (premium)
- **Audit Logging**: All security events logged with IP, user agent, timestamp
- **GDPR Compliance**: Data export, deletion, consent management

**Scalability Requirements**:
- **Horizontal Scaling**: Stateless services, load balancer ready
- **Database Scaling**: MongoDB sharding support, read replicas
- **File Storage**: CDN integration for resume delivery
- **Caching Strategy**: Redis for session data, API responses
- **Auto-scaling**: Scale services based on CPU/memory thresholds

**Reliability Requirements**:
- **Uptime**: 99.9% availability (< 43 minutes downtime/month)
- **Backup**: Daily automated backups, 30-day retention
- **Disaster Recovery**: RPO < 1 hour, RTO < 4 hours
- **Error Handling**: Graceful degradation, circuit breakers
- **Health Checks**: Service health endpoints, automated monitoring

**Integration Requirements**:
- **API Versioning**: `/api/v1/` namespace, backward compatibility
- **Webhook Support**: Stripe webhooks for payment events
- **OAuth Flows**: Google OAuth 2.0 authorization code flow
- **External APIs**: Retry logic with exponential backoff
- **Data Sync**: Scheduled job sync (hourly), manual trigger option

### What are the key user flows?

**1. New User Registration & Onboarding**
- **Entry Point**: Landing page → "Sign Up" button
- **Steps**:
  1. User enters email, password, first name, last name
  2. System validates input, checks for existing account
  3. System creates user account, sends verification email
  4. User clicks verification link in email
  5. System activates account, redirects to dashboard
  6. User prompted to upload first resume
  7. System parses resume, shows ATS analysis
  8. User completes profile (optional)
- **Success Criteria**: User has verified account with ≥1 resume, ATS score displayed
- **Alternative Flows**: 
  - Google OAuth: Skip email verification, auto-create profile from Google data
  - Email already exists: Show error, offer password reset

**2. Job Search & Application**
- **Entry Point**: Dashboard → "Browse Jobs" or Search bar
- **Steps**:
  1. User enters search criteria (keywords, location, filters)
  2. System queries job database, displays paginated results
  3. User clicks job card to view full details
  4. User reviews job description, requirements, company info
  5. User clicks "Apply" button
  6. System shows resume selection modal
  7. User selects resume, optionally adds cover letter/notes
  8. System submits application, updates status to "pending"
  9. User sees confirmation, application added to tracking dashboard
- **Success Criteria**: Application created with status "pending", visible in applications list
- **Alternative Flows**:
  - No resume uploaded: Prompt to upload resume first
  - Already applied: Show "Already Applied" badge, prevent duplicate
  - Premium feature (auto-apply): Bulk apply to multiple jobs

**3. Resume Upload & Optimization**
- **Entry Point**: Dashboard → "Resumes" → "Upload Resume"
- **Steps**:
  1. User drags/drops or selects PDF/DOCX file
  2. System validates file type and size (< 10MB)
  3. System uploads to S3, generates secure URL
  4. System parses resume content (PDF-lib for PDF, mammoth for DOCX)
  5. System extracts: personal info, experience, education, skills
  6. AI analyzes ATS compatibility, generates score (0-100)
  7. System identifies strengths, weaknesses, missing keywords
  8. User views analysis dashboard with actionable recommendations
  9. User can download optimized version or edit manually
- **Success Criteria**: Resume uploaded, parsed, ATS score ≥ 70, recommendations displayed
- **Alternative Flows**:
  - Parsing fails: Show basic upload success, manual entry option
  - Low ATS score (< 60): Highlight critical issues, offer premium optimization

**4. Application Status Tracking**
- **Entry Point**: Dashboard → "Applications" tab
- **Steps**:
  1. User views applications list (default: all statuses)
  2. User filters by status (pending, reviewed, interviewed, etc.)
  3. User clicks application to view details
  4. User updates status (e.g., "Interviewed" → "Offer Received")
  5. User adds notes (interview feedback, salary discussion)
  6. System updates analytics (interview rate, offer rate)
  7. User can set reminders for follow-ups
- **Success Criteria**: All applications have current status, notes added for key events
- **Alternative Flows**:
  - Bulk status update: Select multiple, update status at once
  - Withdraw application: Mark as "withdrawn", add reason

**5. Analytics & Performance Review**
- **Entry Point**: Dashboard → "Analytics" section
- **Steps**:
  1. User views dashboard with key metrics (applications sent, interviews, offers)
  2. System displays success rates (response rate, interview rate, offer rate)
  3. User filters by date range (7 days, 30 days, all time)
  4. User views charts: applications over time, status distribution
  5. User identifies trends (e.g., low response rate for certain job types)
  6. User adjusts strategy based on insights
- **Success Criteria**: User understands performance, identifies 1-2 actionable insights
- **Alternative Flows**:
  - Export data: Download CSV of all applications
  - Premium analytics: Competitive benchmarking, market insights

### What are the core interfaces?

**1. Authentication Pages**
- **Purpose**: Secure user access, account creation
- **Key Functionality**:
  - Login form (email/password, Google OAuth button)
  - Registration form (email, password, name, terms acceptance)
  - Password reset flow (email input, token validation, new password)
  - Email verification confirmation
- **Critical Components**: Form validation, error messaging, loading states, OAuth redirect handling
- **User Interactions**: Form submission, OAuth popup, password visibility toggle

**2. Dashboard (Home)**
- **Purpose**: Central hub, quick overview of job search progress
- **Key Functionality**:
  - Statistics cards (total applications, pending, interviews, offers)
  - Recent applications list (last 5-10)
  - Quick actions (upload resume, browse jobs, view analytics)
  - Subscription tier badge, upgrade CTA for free users
- **Critical Components**: Stat cards, activity feed, action buttons, charts (applications over time)
- **User Interactions**: Click stats to filter, quick apply from recent jobs, navigate to detailed views

**3. Job Search & Listings**
- **Purpose**: Discover relevant job opportunities
- **Key Functionality**:
  - Search bar (keywords, location autocomplete)
  - Filters (job type, company, skills, date posted)
  - Job cards (title, company, location, salary, posted date)
  - Pagination (10-50 results per page)
  - Save/unsave jobs (bookmark icon)
- **Critical Components**: Search input, filter dropdowns, job card grid, pagination controls
- **User Interactions**: Search, filter, click job card, save job, apply button

**4. Job Details Page**
- **Purpose**: Full job information, application submission
- **Key Functionality**:
  - Complete job description, requirements, benefits
  - Company information, logo, website link
  - Application form (resume selection, cover letter, custom questions)
  - Similar jobs recommendations
  - Save job, share job (social, email)
- **Critical Components**: Job header, description sections, application modal, related jobs carousel
- **User Interactions**: Read description, select resume, write cover letter, submit application

**5. Resume Management**
- **Purpose**: Upload, manage, optimize resumes
- **Key Functionality**:
  - Resume upload (drag-drop, file picker)
  - Resume list (thumbnails, titles, ATS scores, default badge)
  - ATS analysis dashboard (score, strengths, weaknesses, recommendations)
  - Resume actions (view, download, delete, set as default, optimize)
  - Multiple resume versions (e.g., "Software Engineer Resume", "Data Analyst Resume")
- **Critical Components**: File uploader, resume cards, analysis charts, action buttons
- **User Interactions**: Upload file, view analysis, download optimized version, set default

**6. Applications Tracker**
- **Purpose**: Monitor application lifecycle, update statuses
- **Key Functionality**:
  - Applications table (job title, company, status, applied date, actions)
  - Status filters (all, pending, reviewed, interviewed, rejected, offers)
  - Status update dropdown (change status, add notes)
  - Application details modal (full job info, resume used, timeline)
  - Bulk actions (withdraw multiple, export selected)
- **Critical Components**: Data table, status badges, filter tabs, detail modal, bulk action toolbar
- **User Interactions**: Filter by status, update status, add notes, view timeline, bulk select

**7. User Profile & Settings**
- **Purpose**: Manage account, preferences, privacy
- **Key Functionality**:
  - Profile editing (name, email, phone, location, bio)
  - Skills management (add, remove, proficiency levels)
  - Experience & education history
  - Notification preferences (email, push, SMS)
  - Privacy settings (profile visibility, data sharing)
  - Account actions (change password, delete account, export data)
- **Critical Components**: Form sections, skill tags, notification toggles, danger zone (delete account)
- **User Interactions**: Edit fields, add skills, toggle notifications, save changes

**8. Analytics Dashboard**
- **Purpose**: Insights into job search performance
- **Key Functionality**:
  - Key metrics (applications sent, response rate, interview rate, offer rate)
  - Charts (applications over time, status distribution, top companies)
  - Benchmarking (compare to platform averages - premium)
  - Resume performance (which resumes get best results)
  - Market insights (trending skills, top hiring companies)
- **Critical Components**: Metric cards, line/bar/pie charts, comparison tables, insights panel
- **User Interactions**: Select date range, hover for details, drill down into metrics

---

## BUSINESS REQUIREMENTS

### What are your access and authentication needs?

**User Types & Roles**:

1. **Free User** (Default)
   - Access: Basic job search, 5 applications/month, 1 resume upload
   - Permissions: Read jobs, create limited applications, view basic analytics
   - Restrictions: No AI optimization, no bulk apply, no premium analytics

2. **Premium User** (Paid Subscription)
   - Access: Unlimited applications, unlimited resumes, AI optimization, priority support
   - Permissions: All free features + AI resume optimization, cover letter generation, advanced analytics
   - Restrictions: None (full platform access)

3. **Enterprise User** (Custom Plan)
   - Access: All premium features + custom integrations, dedicated support, API access
   - Permissions: All premium + team management, custom branding, advanced reporting
   - Restrictions: None

4. **Admin** (Internal)
   - Access: User management, content moderation, system configuration
   - Permissions: View all users, manage subscriptions, access admin dashboard
   - Restrictions: Cannot access user private data without audit log

5. **Root Admin** (Super Admin)
   - Access: Full system access, database management, service configuration
   - Permissions: All admin features + job sync management, API key configuration, system health monitoring
   - Restrictions: All actions logged for security audit

**Authentication Requirements**:
- **Primary**: Email/password with bcrypt hashing (10 rounds)
- **Secondary**: Google OAuth 2.0 (authorization code flow)
- **Session Management**: JWT access tokens (15 min expiry), refresh tokens (7 day expiry)
- **Multi-Factor Authentication**: SMS/TOTP for premium users (planned)
- **Password Reset**: Email-based token (1 hour expiry)
- **Email Verification**: Required before full account activation
- **Account Lockout**: 5 failed login attempts → 15 minute lockout

**Access Control**:
- **Route Protection**: Frontend ProtectedRoute component, backend middleware
- **API Authorization**: JWT verification on all protected endpoints
- **Role-Based Access**: Middleware checks user role against required roles
- **Resource Ownership**: Users can only access their own data (applications, resumes)
- **Admin Routes**: Separate `/admin/*` routes with admin role requirement

### What business rules must be followed?

**Data Validation Rules**:
- **Email**: Valid format, unique across platform, max 255 characters
- **Password**: Min 8 characters, must include uppercase, lowercase, number
- **Resume File**: PDF/DOC/DOCX only, max 10MB, virus scan before processing
- **Application**: Must have valid jobId, userId, resumeId, cannot duplicate (user + job)
- **Job Sync**: Deduplicate by external job ID, update existing if found

**Process Requirements**:
- **Resume Upload**: Parse → Analyze → Store → Return analysis (< 30 seconds)
- **Job Application**: Validate user → Check limits → Create application → Update analytics
- **Job Sync**: Fetch from APIs → Deduplicate → Transform → Store → Log metrics
- **User Registration**: Validate → Hash password → Create user → Send verification → Create session
- **Subscription Upgrade**: Verify payment → Update tier → Grant permissions → Send confirmation

**Compliance Needs**:
- **GDPR**: Right to access, right to deletion, data portability, consent management
- **Data Retention**: User data retained until account deletion + 30 days
- **Privacy**: No selling user data, transparent data usage policy
- **Security**: SOC 2 Type II compliance (planned), annual security audits
- **Accessibility**: WCAG 2.1 AA compliance for frontend

**Service Level Expectations**:
- **Uptime**: 99.9% availability (< 43 min downtime/month)
- **Support Response**: Free (48 hours), Premium (24 hours), Enterprise (4 hours)
- **Data Backup**: Daily automated backups, 30-day retention
- **Incident Response**: Critical issues resolved within 4 hours
- **API Rate Limits**: Free (100 req/15min), Premium (1000 req/15min), Enterprise (custom)

### What are your implementation priorities?

**HIGH PRIORITY** (MVP - Must Have):
✅ **Completed**:
- User authentication (email/password, Google OAuth)
- User profile management
- Resume upload and storage (S3)
- Resume parsing (PDF extraction)
- ATS compatibility analysis
- Job aggregation (Adzuna, Indeed APIs)
- Job search and filtering
- Application submission and tracking
- Basic analytics dashboard
- Microservices architecture (API Gateway, Auth, User, Job, Resume services)
- MongoDB database with Mongoose
- JWT-based authentication
- Health checks and logging

🚧 **In Progress**:
- Email notifications (SendGrid integration mocked)
- Redis caching (disabled, needs production setup)
- Payment processing (Stripe integration planned)

**MEDIUM PRIORITY** (Post-MVP - Should Have):
- AI-powered resume optimization (OpenAI/Anthropic integration)
- Automated cover letter generation
- Job matching algorithm (ML-based recommendations)
- Advanced analytics (competitive benchmarking, market insights)
- Notification service (email, push, SMS)
- LinkedIn integration (profile sync, auto-apply)
- Bulk application submission
- Interview scheduling integration (Calendly)
- Mobile app (React Native)

**LOWER PRIORITY** (Future - Nice to Have):
- Analytics service (dedicated microservice)
- Payment service (subscription management)
- Integration service (third-party APIs)
- Chrome extension (quick apply from job boards)
- Salary negotiation assistant
- Career coaching marketplace
- Employer/recruiter portal
- Team collaboration features (enterprise)
- White-label solution for staffing agencies
- API marketplace for developers

---

## IMPLEMENTATION STATUS

### ✅ Fully Implemented Services

1. **API Gateway** - Request routing, CORS, rate limiting, health checks
2. **Auth Service** - Registration, login, OAuth, JWT tokens, sessions, security events
3. **User Service** - Profile CRUD, analytics tracking, preferences
4. **Job Service** - Job CRUD, external API sync (Adzuna, Indeed), applications, dashboard
5. **Resume Service** - Upload, S3 storage, PDF parsing, ATS analysis, optimization suggestions
6. **Frontend Service** - React SPA, authentication, job search, applications, resumes, dashboard

### 🚧 Partially Implemented

1. **Email Service** - Mocked (SendGrid integration ready, needs API key)
2. **Redis Caching** - Disabled (infrastructure ready, needs production Redis instance)
3. **Payment Processing** - Planned (Stripe integration scaffolded)

### ❌ Not Yet Implemented

1. **Analytics Service** - Dedicated microservice for advanced analytics
2. **Notification Service** - Multi-channel notifications (email, push, SMS)
3. **Integration Service** - LinkedIn, Glassdoor, other third-party integrations
4. **Payment Service** - Subscription management, billing, invoices

---

## TECHNICAL DEEP DIVE

### Database Schema & Data Models

**User Collection** (`users`)
```typescript
{
  _id: ObjectId,
  email: string (unique, indexed),
  password: string (hashed, select: false),
  googleId: string (unique, sparse index),
  firstName: string (max 50 chars),
  lastName: string (max 50 chars),
  avatar: string (URL),

  // Role & Permissions
  role: enum ['admin', 'user', 'premium', 'enterprise'],
  subscriptionTier: enum ['free', 'basic', 'premium', 'enterprise'],

  // Account Status
  isVerified: boolean,
  isActive: boolean,
  isSuspended: boolean,
  suspendedAt: Date,
  suspendedReason: string,
  isDeleted: boolean,
  deletedAt: Date,

  // Profile
  profile: {
    bio: string (max 1000 chars),
    phoneNumber: string,
    location: {
      country: string,
      state: string,
      city: string,
      zipCode: string,
      coordinates: { lat: number, lng: number },
      remote: boolean
    },
    website: string,
    linkedin: string,
    github: string,
    portfolio: string,

    // Professional
    currentPosition: string,
    currentCompany: string,
    yearsOfExperience: number,
    expectedSalary: {
      min: number,
      max: number,
      currency: string (default: 'USD')
    },

    // Skills & Experience
    skills: [{
      name: string,
      level: enum ['beginner', 'intermediate', 'advanced', 'expert'],
      verified: boolean,
      yearsOfExperience: number
    }],
    education: [{
      institution: string,
      degree: string,
      field: string,
      startDate: Date,
      endDate: Date,
      gpa: number,
      description: string
    }],
    experience: [{
      company: string,
      position: string,
      startDate: Date,
      endDate: Date,
      current: boolean,
      description: string,
      skills: [string],
      achievements: [string]
    }],

    // Visibility
    profileVisibility: enum ['public', 'private', 'connections'],
    searchable: boolean
  },

  // Preferences
  preferences: {
    jobAlerts: boolean,
    emailNotifications: boolean,
    pushNotifications: boolean,
    smsNotifications: boolean,
    weeklyDigest: boolean,
    marketingEmails: boolean,
    jobTypes: [string],
    locations: [string],
    remoteOnly: boolean,
    salaryRange: { min: number, max: number }
  },

  // Analytics
  analytics: {
    profileViews: number,
    searchAppearances: number,
    applicationsSent: number,
    interviewsScheduled: number,
    offersReceived: number,
    loginStreak: number,
    totalLogins: number,
    averageSessionDuration: number,
    lastActiveAt: Date,
    featuresUsed: [string],
    premiumFeaturesUsed: [string],
    responseRate: number,
    interviewRate: number,
    offerRate: number
  },

  // Timestamps
  createdAt: Date,
  updatedAt: Date,
  lastLoginAt: Date,
  passwordChangedAt: Date
}

// Indexes
- email: unique
- googleId: unique, sparse
- role: 1
- subscriptionTier: 1
- isDeleted: 1
- isActive: 1
- createdAt: -1
- profile.skills.name: 1
- profile.location.country: 1, profile.location.city: 1
```

**Job Collection** (`jobs`)
```typescript
{
  _id: ObjectId,
  title: string (required, indexed),
  company: string (required, indexed),
  description: string (required),
  location: string (required, indexed),
  type: enum ['full_time', 'part_time', 'contract', 'internship', 'freelance'],

  salary: {
    min: number,
    max: number,
    currency: string (default: 'USD'),
    range: string
  },

  requirements: [string],
  benefits: [string],
  skills: [string] (indexed),

  experience: {
    min: number,
    max: number
  },

  education: enum ['high_school', 'associate', 'bachelor', 'master', 'phd', 'any'],
  remote: boolean,
  status: enum ['active', 'paused', 'closed', 'draft'],

  postedBy: string (userId, indexed),
  expiresAt: Date,
  applicationDeadline: Date,
  tags: [string],
  isFeatured: boolean,

  // Metrics
  applicationCount: number,
  viewCount: number,

  // External Job Fields
  externalId: string (unique, sparse),
  source: enum ['internal', 'external', 'adzuna', 'indeed', 'linkedin', 'glassdoor'],
  externalUrl: string,
  postedAt: Date,

  // Timestamps
  createdAt: Date,
  updatedAt: Date
}

// Indexes
- status: 1, createdAt: -1
- company: 1
- externalId: 1 (unique, sparse)
- source: 1, postedAt: -1
- location: 1
- type: 1
- skills: 1
- postedBy: 1
- Compound: status: 1, type: 1, location: 1
- Compound: status: 1, source: 1, postedAt: -1
- Compound: skills: 1, status: 1, createdAt: -1
```

**Application Collection** (`applications`)
```typescript
{
  _id: ObjectId,
  jobId: string (required, indexed),
  userId: string (required, indexed),
  resumeId: string (required, indexed),

  status: enum ['pending', 'reviewed', 'shortlisted', 'interviewed', 'rejected', 'accepted', 'withdrawn'],

  appliedAt: Date (default: now),
  notes: string,
  coverLetter: string,
  customAnswers: Mixed,

  // Interview Details
  interviewScheduledAt: Date,
  interviewNotes: string,

  // Outcome
  rejectionReason: string,
  salaryExpectation: number,
  availabilityDate: Date,

  // Referral
  isReferred: boolean,
  referredBy: string,

  source: enum ['direct', 'job-board', 'referral', 'recruiter'],

  // Tracking
  trackingData: {
    views: number,
    downloads: number,
    lastViewedAt: Date
  },

  // Timestamps
  createdAt: Date,
  updatedAt: Date
}

// Indexes
- userId: 1
- jobId: 1
- resumeId: 1
- status: 1
- appliedAt: -1
- userId: 1, status: 1
```

**Resume Collection** (`resumes`)
```typescript
{
  _id: ObjectId,
  userId: string (required, indexed),
  title: string (required),
  fileName: string (required),
  filePath: string (required, S3 URL),
  fileSize: number (bytes),
  fileType: string (MIME type),

  content: {
    personalInfo: {
      firstName: string,
      lastName: string,
      email: string,
      phone: string,
      address: string,
      linkedin: string,
      github: string,
      website: string
    },
    summary: string,
    experience: [{
      company: string,
      position: string,
      startDate: Date,
      endDate: Date,
      current: boolean,
      description: string,
      achievements: [string],
      skills: [string]
    }],
    education: [{
      institution: string,
      degree: string,
      field: string,
      startDate: Date,
      endDate: Date,
      gpa: number,
      description: string
    }],
    skills: [string],
    certifications: [{
      name: string,
      issuer: string,
      date: Date,
      expiryDate: Date
    }],
    languages: [{
      language: string,
      proficiency: enum ['basic', 'conversational', 'fluent', 'native']
    }],
    projects: [{
      name: string,
      description: string,
      url: string,
      technologies: [string]
    }]
  },

  analysis: {
    overallScore: number (0-100),
    atsScore: number (0-100),
    readabilityScore: number (0-100),
    keywordMatch: number (0-100),
    skillsMatch: number (0-100),
    experienceMatch: number (0-100),
    educationMatch: number (0-100),
    recommendedImprovements: [string],
    strengths: [string],
    weaknesses: [string],
    missingKeywords: [string],
    lastAnalyzed: Date
  },

  isPublic: boolean,
  isDefault: boolean,
  tags: [string],

  // Metrics
  downloadCount: number,
  viewCount: number,
  lastUsedAt: Date,

  // Timestamps
  createdAt: Date,
  updatedAt: Date
}

// Indexes
- userId: 1
- isDefault: 1
- createdAt: -1
```

**Session Collection** (`sessions`)
```typescript
{
  _id: ObjectId,
  userId: ObjectId (indexed),
  sessionId: string (unique, indexed),

  deviceInfo: {
    userAgent: string,
    ip: string,
    deviceType: string,
    browser: string,
    os: string
  },

  isActive: boolean,
  expiresAt: Date (TTL index),

  createdAt: Date,
  updatedAt: Date
}

// Indexes
- userId: 1
- sessionId: 1 (unique)
- expiresAt: 1 (TTL, expireAfterSeconds: 0)
- isActive: 1
```

**Security Event Collection** (`securityevents`)
```typescript
{
  _id: ObjectId,
  userId: ObjectId (indexed),
  eventType: string (indexed),
  ipAddress: string,
  userAgent: string,
  metadata: Mixed,

  createdAt: Date
}

// Indexes
- userId: 1
- eventType: 1
- createdAt: -1
```

---

### Complete API Reference

**API Gateway** - Port 3000 (Production Entry Point)

All requests go through the API Gateway which routes to appropriate microservices.

**Base URL**: `https://api.resume-automator.com` (Production) | `http://localhost:3000` (Development)

**Authentication**:
- Header: `Authorization: Bearer <access_token>`
- Optional: `X-Session-ID: <session_id>`

---

#### **Authentication Endpoints** (`/api/v1/auth`)

**POST /api/v1/auth/register**
- **Description**: Register new user account
- **Auth Required**: No
- **Request Body**:
  ```json
  {
    "email": "<EMAIL>",
    "password": "SecurePass123",
    "firstName": "John",
    "lastName": "Doe",
    "googleId": "optional_google_id",
    "avatar": "https://example.com/avatar.jpg"
  }
  ```
- **Response** (201):
  ```json
  {
    "success": true,
    "message": "User registered successfully",
    "data": {
      "user": {
        "id": "user_id",
        "email": "<EMAIL>",
        "firstName": "John",
        "lastName": "Doe",
        "role": "user",
        "subscriptionTier": "free",
        "isVerified": false
      },
      "tokens": {
        "accessToken": "jwt_access_token",
        "refreshToken": "jwt_refresh_token"
      },
      "expiresIn": 900
    }
  }
  ```
- **Validation**:
  - Email: Valid format, unique
  - Password: Min 8 chars (if not using OAuth)
  - First/Last Name: 1-50 chars

**POST /api/v1/auth/login**
- **Description**: Login with email/password
- **Auth Required**: No
- **Request Body**:
  ```json
  {
    "email": "<EMAIL>",
    "password": "SecurePass123",
    "rememberMe": false
  }
  ```
- **Response** (200):
  ```json
  {
    "success": true,
    "message": "Login successful",
    "data": {
      "user": {
        "id": "user_id",
        "email": "<EMAIL>",
        "role": "user",
        "subscriptionTier": "free",
        "isVerified": true
      },
      "tokens": {
        "accessToken": "jwt_access_token",
        "refreshToken": "jwt_refresh_token"
      },
      "expiresIn": 900
    }
  }
  ```
- **Rate Limit**: 10 attempts per 15 minutes per IP
- **Account Lockout**: 5 failed attempts → 15 min lockout

**GET /api/v1/auth/google**
- **Description**: Initiate Google OAuth flow
- **Auth Required**: No
- **Response**: Redirects to Google OAuth consent screen

**GET /api/v1/auth/google/callback**
- **Description**: Google OAuth callback handler
- **Auth Required**: No
- **Response**: Redirects to frontend with tokens in URL params

**POST /api/v1/auth/refresh**
- **Description**: Refresh access token
- **Auth Required**: No (requires refresh token)
- **Request Body**:
  ```json
  {
    "refreshToken": "jwt_refresh_token"
  }
  ```
- **Response** (200):
  ```json
  {
    "success": true,
    "data": {
      "accessToken": "new_jwt_access_token",
      "expiresIn": 900
    }
  }
  ```

**POST /api/v1/auth/logout**
- **Description**: Logout and invalidate session
- **Auth Required**: Yes
- **Response** (200):
  ```json
  {
    "success": true,
    "message": "Logged out successfully"
  }
  ```

**POST /api/v1/auth/verify-email**
- **Description**: Verify email with token
- **Auth Required**: No
- **Request Body**:
  ```json
  {
    "email": "<EMAIL>",
    "token": "verification_token"
  }
  ```

**POST /api/v1/auth/forgot-password**
- **Description**: Request password reset
- **Auth Required**: No
- **Request Body**:
  ```json
  {
    "email": "<EMAIL>"
  }
  ```

**POST /api/v1/auth/reset-password**
- **Description**: Reset password with token
- **Auth Required**: No
- **Request Body**:
  ```json
  {
    "token": "reset_token",
    "newPassword": "NewSecurePass123"
  }
  ```

**POST /api/v1/auth/change-password**
- **Description**: Change password (authenticated)
- **Auth Required**: Yes
- **Request Body**:
  ```json
  {
    "currentPassword": "OldPass123",
    "newPassword": "NewPass123"
  }
  ```

---

#### **User Endpoints** (`/api/v1/users`)

**GET /api/v1/users/profile**
- **Description**: Get current user profile
- **Auth Required**: Yes
- **Response** (200):
  ```json
  {
    "success": true,
    "data": {
      "id": "user_id",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "avatar": "https://...",
      "role": "user",
      "subscriptionTier": "free",
      "profile": { /* full profile object */ },
      "preferences": { /* preferences object */ },
      "analytics": { /* analytics object */ }
    }
  }
  ```

**PUT /api/v1/users/profile**
- **Description**: Update user profile
- **Auth Required**: Yes
- **Request Body**: Partial profile object
- **Response** (200): Updated user object

**PATCH /api/v1/users/profile/:section**
- **Description**: Update specific profile section
- **Auth Required**: Yes
- **Sections**: `basic`, `professional`, `education`, `skills`, `contact`, `preferences`
- **Request Body**: Section-specific data

**GET /api/v1/users/analytics**
- **Description**: Get user analytics
- **Auth Required**: Yes
- **Response** (200):
  ```json
  {
    "success": true,
    "data": {
      "profileViews": 150,
      "applicationsSent": 45,
      "interviewsScheduled": 8,
      "offersReceived": 2,
      "responseRate": 17.78,
      "interviewRate": 25.0,
      "offerRate": 4.44
    }
  }
  ```

**POST /api/v1/users/avatar**
- **Description**: Upload/update avatar
- **Auth Required**: Yes
- **Request Body**:
  ```json
  {
    "avatarUrl": "https://example.com/avatar.jpg"
  }
  ```

**DELETE /api/v1/users/account**
- **Description**: Delete user account (soft delete)
- **Auth Required**: Yes
- **Response** (200): Confirmation message

---

#### **Job Endpoints** (`/api/v1/jobs`)

**GET /api/v1/jobs**
- **Description**: Get paginated job listings
- **Auth Required**: No (optional for personalization)
- **Query Parameters**:
  - `page`: number (default: 1)
  - `limit`: number (default: 10, max: 50)
  - `status`: string (default: 'active')
  - `type`: string (full_time, part_time, etc.)
  - `location`: string (regex search)
  - `company`: string (regex search)
  - `skills`: string[] (array of skills)
  - `remote`: boolean
- **Response** (200):
  ```json
  {
    "success": true,
    "data": {
      "jobs": [/* job objects */],
      "total": 150,
      "page": 1,
      "limit": 10,
      "totalPages": 15
    }
  }
  ```

**GET /api/v1/jobs/:id**
- **Description**: Get single job details
- **Auth Required**: No
- **Response** (200): Full job object

**POST /api/v1/jobs**
- **Description**: Create new job (admin/employer only)
- **Auth Required**: Yes (admin role)
- **Request Body**:
  ```json
  {
    "title": "Senior Software Engineer",
    "company": "Tech Corp",
    "description": "Full job description...",
    "location": "San Francisco, CA",
    "type": "full_time",
    "salary": {
      "min": 120000,
      "max": 180000,
      "currency": "USD"
    },
    "requirements": ["5+ years experience", "..."],
    "skills": ["JavaScript", "React", "Node.js"],
    "remote": true
  }
  ```

**PUT /api/v1/jobs/:id**
- **Description**: Update job
- **Auth Required**: Yes (admin or job owner)
- **Request Body**: Partial job object

**DELETE /api/v1/jobs/:id**
- **Description**: Delete job
- **Auth Required**: Yes (admin or job owner)

**POST /api/v1/jobs/search**
- **Description**: Advanced job search
- **Auth Required**: No
- **Request Body**:
  ```json
  {
    "keywords": "software engineer",
    "location": "San Francisco",
    "type": ["full_time", "contract"],
    "remote": true,
    "salaryMin": 100000,
    "skills": ["JavaScript", "React"],
    "experience": { "min": 3, "max": 7 }
  }
  ```

---

#### **Application Endpoints** (`/api/v1/applications`)

**GET /api/v1/applications**
- **Description**: Get user's applications
- **Auth Required**: Yes
- **Query Parameters**:
  - `page`: number
  - `limit`: number
  - `status`: string (filter by status)
- **Response** (200):
  ```json
  {
    "success": true,
    "data": {
      "applications": [/* application objects */],
      "total": 45,
      "page": 1,
      "limit": 10,
      "totalPages": 5
    }
  }
  ```

**GET /api/v1/applications/:id**
- **Description**: Get single application details
- **Auth Required**: Yes (owner only)

**POST /api/v1/applications**
- **Description**: Submit job application
- **Auth Required**: Yes
- **Request Body**:
  ```json
  {
    "jobId": "job_id",
    "resumeId": "resume_id",
    "coverLetter": "Dear Hiring Manager...",
    "customAnswers": {
      "question1": "answer1"
    },
    "salaryExpectation": 150000,
    "availabilityDate": "2025-11-01"
  }
  ```
- **Validation**:
  - User cannot apply to same job twice
  - Resume must belong to user
  - Free tier: max 5 applications/month
- **Response** (201): Created application object

**PUT /api/v1/applications/:id**
- **Description**: Update application (status, notes)
- **Auth Required**: Yes (owner only)
- **Request Body**:
  ```json
  {
    "status": "interviewed",
    "notes": "Interview went well...",
    "interviewScheduledAt": "2025-10-15T10:00:00Z",
    "interviewNotes": "Technical round with team lead"
  }
  ```

**DELETE /api/v1/applications/:id**
- **Description**: Withdraw application
- **Auth Required**: Yes (owner only)

**POST /api/v1/applications/bulk/withdraw**
- **Description**: Withdraw multiple applications
- **Auth Required**: Yes
- **Request Body**:
  ```json
  {
    "applicationIds": ["id1", "id2", "id3"],
    "reason": "Accepted another offer"
  }
  ```

**GET /api/v1/applications/analytics**
- **Description**: Get application analytics
- **Auth Required**: Yes
- **Response** (200):
  ```json
  {
    "success": true,
    "data": {
      "applicationsSent": 45,
      "interviewsScheduled": 8,
      "offersReceived": 2,
      "responseRate": 17.78,
      "interviewRate": 25.0,
      "offerRate": 4.44
    }
  }
  ```

---

#### **Resume Endpoints** (`/api/v1/resumes`)

**GET /api/v1/resumes**
- **Description**: Get user's resumes
- **Auth Required**: Yes
- **Query Parameters**:
  - `page`: number
  - `limit`: number
  - `isDefault`: boolean
- **Response** (200):
  ```json
  {
    "success": true,
    "data": {
      "resumes": [/* resume objects */],
      "total": 3,
      "page": 1,
      "limit": 10,
      "totalPages": 1
    }
  }
  ```

**GET /api/v1/resumes/:id**
- **Description**: Get single resume
- **Auth Required**: Yes (owner only)

**POST /api/v1/resumes/upload**
- **Description**: Upload new resume
- **Auth Required**: Yes
- **Content-Type**: `multipart/form-data`
- **Form Data**:
  - `file`: File (PDF, DOC, DOCX, max 10MB)
  - `name`: string (optional, resume title)
  - `isDefault`: boolean (optional)
- **Process**:
  1. Validate file type and size
  2. Upload to S3
  3. Parse content (PDF-lib for PDF)
  4. Analyze ATS compatibility
  5. Store in database
- **Response** (201):
  ```json
  {
    "success": true,
    "data": {
      "id": "resume_id",
      "title": "Software Engineer Resume",
      "fileName": "resume.pdf",
      "filePath": "https://s3.../resume.pdf",
      "fileSize": 245678,
      "fileType": "application/pdf",
      "content": { /* parsed content */ },
      "analysis": {
        "overallScore": 78,
        "atsScore": 82,
        "readabilityScore": 75,
        "strengths": ["Clear structure", "..."],
        "weaknesses": ["Missing keywords", "..."],
        "recommendedImprovements": ["Add more action verbs", "..."]
      }
    }
  }
  ```
- **Rate Limit**: 10 uploads per 15 minutes

**POST /api/v1/resumes/:id/analyze**
- **Description**: Re-analyze resume (optionally with job description)
- **Auth Required**: Yes (owner only)
- **Request Body** (optional):
  ```json
  {
    "jobDescription": {
      "title": "Senior Software Engineer",
      "requirements": ["5+ years", "..."],
      "skills": ["JavaScript", "React"],
      "experience": "5-7 years",
      "education": "Bachelor's degree"
    }
  }
  ```
- **Response** (200): Updated analysis object

**POST /api/v1/resumes/:id/optimize**
- **Description**: Get AI optimization suggestions (premium feature)
- **Auth Required**: Yes (premium tier)
- **Request Body** (optional):
  ```json
  {
    "jobId": "job_id"
  }
  ```
- **Response** (200):
  ```json
  {
    "success": true,
    "data": {
      "optimizedContent": { /* suggested content changes */ },
      "suggestions": [
        {
          "type": "keywords",
          "message": "Add 'cloud computing' to skills",
          "priority": "high",
          "applied": false
        }
      ]
    }
  }
  ```

**PUT /api/v1/resumes/:id**
- **Description**: Update resume metadata
- **Auth Required**: Yes (owner only)
- **Request Body**:
  ```json
  {
    "title": "Updated Resume Title",
    "tags": ["software", "engineering"],
    "isDefault": true
  }
  ```

**DELETE /api/v1/resumes/:id**
- **Description**: Delete resume
- **Auth Required**: Yes (owner only)

**GET /api/v1/resumes/:id/download**
- **Description**: Download resume file
- **Auth Required**: Yes (owner only)
- **Response**: File download (redirects to S3 signed URL)

---

#### **Admin Dashboard Endpoints** (`/api/v1/dashboard`)

**GET /api/v1/dashboard/overview**
- **Description**: Get system overview (root admin only)
- **Auth Required**: Yes (root_admin role)
- **Response** (200):
  ```json
  {
    "success": true,
    "data": {
      "systemHealth": {
        "status": "healthy",
        "uptime": 99.98,
        "services": {
          "database": "healthy",
          "redis": "healthy",
          "s3": "healthy"
        }
      },
      "syncStats": {
        "lastSync": "2025-09-30T10:00:00Z",
        "totalJobs": 1250,
        "successRate": 98.5
      },
      "jobStats": {
        "total": 1250,
        "active": 980,
        "bySource": {
          "adzuna": 650,
          "indeed": 600
        }
      },
      "apiKeyStatus": {
        "adzuna": true,
        "indeed": true,
        "linkedin": false,
        "glassdoor": false
      }
    }
  }
  ```

**POST /api/v1/dashboard/sync/start**
- **Description**: Manually trigger job sync
- **Auth Required**: Yes (root_admin role)
- **Request Body**:
  ```json
  {
    "query": "software engineer",
    "location": "us",
    "maxJobs": 50
  }
  ```

**GET /api/v1/dashboard/analytics**
- **Description**: Get platform analytics
- **Auth Required**: Yes (admin role)
- **Query Parameters**:
  - `period`: string ('1d', '7d', '30d', '90d')
- **Response** (200): Comprehensive analytics data

---

### Security Architecture & Implementation

**Multi-Layer Security Approach**

**1. Authentication Layer**
- **JWT Tokens**:
  - Access Token: 15-minute expiry, contains userId, email, role, sessionId
  - Refresh Token: 7-day expiry, contains userId, sessionId, tokenVersion
  - Issuer: `job-platform-auth`
  - Algorithm: HS256
  - Secret: 32+ character random string (environment variable)

- **OAuth 2.0 (Google)**:
  - Authorization Code Flow
  - Scopes: `profile`, `email`
  - Callback URL: `/api/v1/auth/google/callback`
  - State parameter for CSRF protection

- **Session Management**:
  - MongoDB-based session storage
  - Session ID in JWT payload
  - Device fingerprinting (IP, User-Agent, browser, OS)
  - Active session tracking
  - TTL index for automatic expiration

**2. Authorization Layer**
- **Role-Based Access Control (RBAC)**:
  - Roles: `user`, `premium`, `admin`, `enterprise`, `root_admin`
  - Middleware: `requireRole(['admin', 'premium'])`
  - Resource ownership validation
  - Subscription tier enforcement

**3. Input Validation**
- **Zod Schema Validation**:
  - All request bodies validated
  - Type-safe validation
  - Custom error messages
  - Sanitization of inputs

- **File Upload Validation**:
  - MIME type checking
  - File size limits (10MB for resumes)
  - Virus scanning (planned)
  - Extension whitelist: PDF, DOC, DOCX

**4. Rate Limiting**
- **Tier-Based Limits**:
  - Free: 100 requests / 15 minutes
  - Premium: 1000 requests / 15 minutes
  - Enterprise: Custom limits

- **Endpoint-Specific Limits**:
  - Auth endpoints: 10 attempts / 15 minutes
  - File uploads: 10 uploads / 15 minutes
  - Job sync: 5 syncs / hour (admin only)

- **Implementation**:
  - In-memory rate limit store (production: Redis)
  - IP-based for unauthenticated requests
  - User ID-based for authenticated requests
  - Response headers: `X-RateLimit-Limit`, `X-RateLimit-Remaining`, `X-RateLimit-Reset`

**5. Security Headers (Helmet.js)**
```javascript
{
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      fontSrc: ["'self'"],
      connectSrc: ["'self'"]
    }
  },
  crossOriginEmbedderPolicy: false,
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  },
  noSniff: true,
  xssFilter: true,
  frameguard: { action: 'deny' }
}
```

**6. CORS Configuration**
```javascript
{
  origin: [
    'https://jobs-app-ydwim.ondigitalocean.app',
    'https://stingray-app-7geup.ondigitalocean.app',
    'http://localhost:3000',
    'http://localhost:5173'
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  exposedHeaders: ['X-Request-ID'],
  maxAge: 86400
}
```

**7. Password Security**
- **Hashing**: bcrypt with 10 salt rounds
- **Policy**:
  - Minimum 8 characters
  - Must include: uppercase, lowercase, number
  - No common passwords (dictionary check planned)
  - Password history (prevent reuse of last 5 passwords - planned)

- **Storage**:
  - Never stored in plain text
  - `select: false` in Mongoose schema
  - Only retrieved when explicitly needed

**8. Data Encryption**
- **In Transit**: TLS 1.3 (enforced in production)
- **At Rest**:
  - MongoDB Atlas encryption
  - AWS S3 server-side encryption (AES-256)
  - Sensitive fields encrypted with AES-256 (planned)

**9. Security Event Logging**
```typescript
{
  userId: ObjectId,
  eventType: 'login' | 'logout' | 'password_change' | 'failed_login' | 'account_locked',
  ipAddress: string,
  userAgent: string,
  metadata: {
    success: boolean,
    reason: string,
    // ... event-specific data
  },
  createdAt: Date
}
```

**10. Account Protection**
- **Failed Login Handling**:
  - Track failed attempts per email
  - 5 failed attempts → 15-minute account lockout
  - Exponential backoff for repeated failures
  - Email notification on suspicious activity

- **Session Security**:
  - Automatic logout after 7 days of inactivity
  - Concurrent session limit (5 active sessions)
  - Device-based session management
  - Logout from all devices option

**11. API Security**
- **Request Validation**:
  - JSON schema validation
  - SQL injection prevention (MongoDB parameterized queries)
  - NoSQL injection prevention (input sanitization)
  - XSS prevention (output encoding)

- **Error Handling**:
  - Generic error messages in production
  - Detailed errors in development
  - No stack traces exposed to clients
  - Error logging with context

**12. Compliance & Privacy**
- **GDPR Compliance**:
  - Right to access (data export)
  - Right to deletion (soft delete with 30-day retention)
  - Right to rectification (profile updates)
  - Consent management
  - Data portability (JSON export)

- **Data Retention**:
  - Active users: indefinite
  - Deleted accounts: 30 days then permanent deletion
  - Security logs: 90 days
  - Application data: tied to user account

---

### System Architecture & Data Flow

**Microservices Architecture Diagram**

```
┌─────────────────────────────────────────────────────────────────┐
│                         FRONTEND LAYER                          │
│  ┌──────────────────────────────────────────────────────────┐  │
│  │  React SPA (Port 5173 dev / 8080 prod)                   │  │
│  │  - React 18 + TypeScript                                 │  │
│  │  - Vite Build Tool                                       │  │
│  │  - Tailwind CSS                                          │  │
│  │  - React Query (TanStack Query)                          │  │
│  │  - Axios HTTP Client                                     │  │
│  └──────────────────────────────────────────────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
                              ↓ HTTPS
┌─────────────────────────────────────────────────────────────────┐
│                      API GATEWAY (Port 3000)                    │
│  ┌──────────────────────────────────────────────────────────┐  │
│  │  - Request Routing                                       │  │
│  │  - Rate Limiting (tier-based)                            │  │
│  │  - CORS Handling                                         │  │
│  │  - Security Headers (Helmet.js)                          │  │
│  │  - Request/Response Logging                              │  │
│  │  - Health Check Aggregation                              │  │
│  └──────────────────────────────────────────────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
         ↓              ↓              ↓              ↓
┌────────────────┬────────────────┬────────────────┬────────────────┐
│  Auth Service  │  User Service  │  Job Service   │ Resume Service │
│   (Port 3001)  │   (Port 3002)  │  (Port 3003)   │  (Port 3005)   │
├────────────────┼────────────────┼────────────────┼────────────────┤
│ - Registration │ - Profile CRUD │ - Job CRUD     │ - Upload       │
│ - Login/Logout │ - Analytics    │ - Applications │ - Parse PDF    │
│ - JWT Tokens   │ - Preferences  │ - Job Sync     │ - ATS Analysis │
│ - OAuth (Google│ - Skills Mgmt  │ - Search       │ - Optimization │
│ - Sessions     │ - Avatar       │ - Dashboard    │ - S3 Storage   │
│ - Password Mgmt│                │ - External APIs│                │
└────────────────┴────────────────┴────────────────┴────────────────┘
         ↓              ↓              ↓              ↓
┌─────────────────────────────────────────────────────────────────┐
│                      DATA LAYER                                 │
│  ┌──────────────────────┐  ┌──────────────────────┐            │
│  │  MongoDB Atlas       │  │  Redis (Planned)     │            │
│  │  - Users             │  │  - Sessions          │            │
│  │  - Jobs              │  │  - Rate Limits       │            │
│  │  - Applications      │  │  - Cache             │            │
│  │  - Resumes           │  │  - Job Queue         │            │
│  │  - Sessions          │  └──────────────────────┘            │
│  │  - Security Events   │                                      │
│  └──────────────────────┘                                      │
└─────────────────────────────────────────────────────────────────┘
                              ↓
┌─────────────────────────────────────────────────────────────────┐
│                    EXTERNAL SERVICES                            │
│  ┌──────────────┬──────────────┬──────────────┬──────────────┐ │
│  │  AWS S3      │  Adzuna API  │  Indeed API  │  SendGrid    │ │
│  │  (Resumes)   │  (Jobs)      │  (Jobs)      │  (Email)     │ │
│  └──────────────┴──────────────┴──────────────┴──────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

**Service Communication Patterns**

1. **Synchronous HTTP Communication**:
   - All inter-service communication via HTTP REST APIs
   - No shared libraries or direct database access
   - Each service is independently deployable
   - Timeout: 10 seconds per request
   - Retry logic: Exponential backoff (3 attempts)

2. **Request Flow Example - Job Application**:
   ```
   User → Frontend → API Gateway → Job Service
                                      ↓
                                   Validate Job Exists
                                      ↓
                                   Check User Limits (User Service)
                                      ↓
                                   Validate Resume (Resume Service)
                                      ↓
                                   Create Application
                                      ↓
                                   Update Analytics (User Service)
                                      ↓
                                   Send Notification (Notification Service - planned)
                                      ↓
                                   Return Success
   ```

3. **Data Flow - Resume Upload & Analysis**:
   ```
   User uploads PDF → Frontend
                        ↓
                     API Gateway
                        ↓
                   Resume Service
                        ↓
                   Validate File (type, size)
                        ↓
                   Upload to S3 → Get URL
                        ↓
                   Parse PDF Content (pdf-lib)
                        ↓
                   Extract: Personal Info, Experience, Education, Skills
                        ↓
                   ATS Analysis Engine
                        ↓
                   Calculate Scores:
                   - Overall Score (0-100)
                   - ATS Compatibility (0-100)
                   - Readability (0-100)
                   - Keyword Match (0-100)
                        ↓
                   Generate Recommendations
                        ↓
                   Store in MongoDB
                        ↓
                   Return Analysis to User
   ```

4. **Job Sync Flow (External API Integration)**:
   ```
   Cron Job / Manual Trigger → Job Service
                                    ↓
                              Job Sync Service
                                    ↓
                    ┌───────────────┴───────────────┐
                    ↓                               ↓
              Adzuna API                      Indeed API
              (Parallel Fetch)                (Parallel Fetch)
                    ↓                               ↓
              Transform Jobs                  Transform Jobs
                    ↓                               ↓
                    └───────────────┬───────────────┘
                                    ↓
                              Deduplicate by externalId
                                    ↓
                              Validate & Sanitize
                                    ↓
                              Bulk Insert/Update MongoDB
                                    ↓
                              Update Sync Metrics
                                    ↓
                              Log Results
   ```

**Error Handling & Resilience**

1. **Circuit Breaker Pattern** (Planned):
   - Prevent cascade failures
   - Open circuit after 5 consecutive failures
   - Half-open state after 30 seconds
   - Close circuit after 3 successful requests

2. **Graceful Degradation**:
   - Resume service continues without S3 (local storage fallback)
   - Job service continues without external APIs (use cached data)
   - Analytics service failure doesn't block core operations

3. **Error Response Format**:
   ```json
   {
     "success": false,
     "message": "User-friendly error message",
     "statusCode": 400,
     "errors": ["Detailed error 1", "Detailed error 2"],
     "path": "/api/v1/endpoint",
     "timestamp": "2025-09-30T10:00:00Z"
   }
   ```

---

### Deployment & Infrastructure

**Current Deployment: DigitalOcean App Platform**

**Infrastructure Components**:

1. **Frontend Service**:
   - **Platform**: DigitalOcean Static Site
   - **Build**: Vite production build
   - **CDN**: DigitalOcean CDN (automatic)
   - **URL**: `https://jobs-app-ydwim.ondigitalocean.app`
   - **Instance**: Basic (512MB RAM, 1 vCPU)
   - **Auto-scaling**: No (static site)

2. **Backend Services** (Microservices):
   - **Platform**: DigitalOcean App Platform (Docker containers)
   - **Services**:
     - API Gateway: `resume-automator-services-api-ga` (Port 3000)
     - Auth Service: `resume-automator-services-auth-s` (Port 3001)
     - User Service: `resume-automator-services-user-s` (Port 3002)
     - Job Service: `resume-automator-services-job-se` (Port 3003)
     - Resume Service: `resume-automator-services-resume` (Port 3005)
   - **Instance Size**: Basic XXS (512MB RAM, 1 vCPU each)
   - **Auto-scaling**: Enabled (1-3 instances per service)
   - **Health Checks**: `/health` endpoint (30s interval)

3. **Database**:
   - **Platform**: MongoDB Atlas (Managed)
   - **Tier**: M0 Sandbox (Free tier for development)
   - **Region**: AWS us-east-1
   - **Connection**: TLS/SSL encrypted
   - **Backup**: Automatic daily snapshots
   - **Scaling**: Vertical scaling available (M2, M5, M10+)

4. **File Storage**:
   - **Platform**: AWS S3
   - **Bucket**: `resume-automator-uploads`
   - **Region**: us-east-1
   - **Encryption**: AES-256 server-side
   - **Access**: IAM role-based (least privilege)
   - **CDN**: CloudFront (planned)

5. **Caching** (Planned):
   - **Platform**: Redis Cloud or DigitalOcean Managed Redis
   - **Use Cases**: Sessions, rate limiting, job cache
   - **Eviction**: LRU (Least Recently Used)
   - **Persistence**: RDB snapshots

**Environment Configuration**

**Development Environment**:
```bash
NODE_ENV=development
PORT=3000-3005 (service-specific)
API_VERSION=1.0.0

# Database
MONGODB_URI=mongodb+srv://user:<EMAIL>/db?retryWrites=true&w=majority
REDIS_URL=redis://localhost:6379

# JWT
JWT_SECRET=dev-secret-key-32-chars-minimum
JWT_REFRESH_SECRET=dev-refresh-secret-32-chars-minimum

# AWS S3
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=AKIA...
AWS_SECRET_ACCESS_KEY=secret...
AWS_S3_BUCKET=resume-automator-dev

# External APIs
ADZUNA_APP_ID=your-app-id
ADZUNA_API_KEY=your-api-key
INDEED_PUBLISHER_ID=your-publisher-id

# Google OAuth
GOOGLE_CLIENT_ID=your-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-client-secret
GOOGLE_CALLBACK_URL=http://localhost:3000/api/v1/auth/google/callback

# Email
SENDGRID_API_KEY=SG.your-api-key
FROM_EMAIL=<EMAIL>

# Frontend
FRONTEND_URL=http://localhost:5173

# CORS
CORS_ORIGIN=http://localhost:3000,http://localhost:5173

# Logging
LOG_LEVEL=debug
```

**Production Environment**:
```bash
NODE_ENV=production
PORT=8080 (all services)
API_VERSION=1.0.0

# Database (MongoDB Atlas)
MONGODB_URI=mongodb+srv://prod-user:<EMAIL>/prod_db?retryWrites=true&w=majority

# Redis (Managed)
REDIS_URL=redis://:password@redis-cluster:6379

# JWT (Strong secrets)
JWT_SECRET=production-secret-key-64-chars-minimum-random-string
JWT_REFRESH_SECRET=production-refresh-secret-64-chars-minimum-random-string

# AWS S3 (Production bucket)
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=AKIA...
AWS_SECRET_ACCESS_KEY=secret...
AWS_S3_BUCKET=resume-automator-prod

# External APIs (Production keys)
ADZUNA_APP_ID=prod-app-id
ADZUNA_API_KEY=prod-api-key
INDEED_PUBLISHER_ID=prod-publisher-id

# Google OAuth (Production)
GOOGLE_CLIENT_ID=prod-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=prod-client-secret
GOOGLE_CALLBACK_URL=https://api.resume-automator.com/api/v1/auth/google/callback

# Email (Production)
SENDGRID_API_KEY=SG.prod-api-key
FROM_EMAIL=<EMAIL>

# Frontend
FRONTEND_URL=https://resume-automator.com

# CORS (Production domains)
CORS_ORIGIN=https://resume-automator.com,https://www.resume-automator.com

# Logging
LOG_LEVEL=info

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
PREMIUM_RATE_LIMIT_MAX_REQUESTS=1000
```

**Docker Configuration**

**Development (docker-compose.yml)**:
```yaml
version: '3.8'

services:
  # MongoDB (Local development)
  mongodb:
    image: mongo:7.0
    container_name: job-platform-mongodb
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: jobplatform2024
    volumes:
      - mongodb_data:/data/db
      - ./scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js

  # Redis (Local development)
  redis:
    image: redis:7.4-alpine
    container_name: job-platform-redis
    ports:
      - "6379:6379"
    command: redis-server --requirepass jobplatform2024
    volumes:
      - redis_data:/data

  # API Gateway
  api-gateway:
    build:
      context: .
      dockerfile: services/api-gateway/Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - MONGODB_URI=***************************************************************************
      - REDIS_URL=redis://:jobplatform2024@redis:6379
    depends_on:
      - mongodb
      - redis

  # ... other services

volumes:
  mongodb_data:
  redis_data:
```

**Production (docker-compose.prod.yml)**:
- Multi-stage builds (build → production)
- Health checks for all services
- Resource limits (CPU, memory)
- Restart policies (unless-stopped)
- Network isolation
- Volume persistence

**Deployment Process**

**Manual Deployment**:
```bash
# 1. Build all services
npm run build

# 2. Run tests
npm test

# 3. Build Docker images
docker-compose -f docker-compose.prod.yml build

# 4. Push to registry (DigitalOcean Container Registry)
docker tag resume-automator/api-gateway registry.digitalocean.com/resume-automator/api-gateway:latest
docker push registry.digitalocean.com/resume-automator/api-gateway:latest

# 5. Deploy to DigitalOcean App Platform
doctl apps create --spec .do/app.yaml
```

**CI/CD Pipeline** (Planned - GitHub Actions):
```yaml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '22'
      - run: npm install
      - run: npm test
      - run: npm run lint

  build-and-deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Build Docker images
        run: docker-compose -f docker-compose.prod.yml build
      - name: Push to registry
        run: |
          echo ${{ secrets.DIGITALOCEAN_TOKEN }} | docker login registry.digitalocean.com -u ${{ secrets.DIGITALOCEAN_TOKEN }} --password-stdin
          docker-compose -f docker-compose.prod.yml push
      - name: Deploy to DigitalOcean
        uses: digitalocean/app_action@v1
        with:
          app_name: resume-automator
          token: ${{ secrets.DIGITALOCEAN_TOKEN }}
```

**Scaling Strategy**

**Horizontal Scaling**:
- **Auto-scaling**: 1-5 instances per service based on CPU/memory
- **Load Balancing**: DigitalOcean App Platform (automatic)
- **Stateless Services**: All services are stateless (session in DB)
- **Database**: MongoDB Atlas auto-scaling (M10+ tier)

**Vertical Scaling**:
- **Current**: Basic XXS (512MB RAM, 1 vCPU)
- **Next Tier**: Basic XS (1GB RAM, 1 vCPU)
- **Production**: Professional (2GB RAM, 2 vCPU)
- **Enterprise**: Dedicated (4GB+ RAM, 4+ vCPU)

**Cost Estimates**:

**Development (Current)**:
- Frontend: $5/month (static site)
- Backend Services (5 × $5): $25/month
- MongoDB Atlas: $0 (M0 free tier)
- AWS S3: ~$1/month (< 1GB storage)
- **Total**: ~$31/month

**Production (Projected)**:
- Frontend: $12/month (professional tier)
- Backend Services (5 × $12): $60/month
- MongoDB Atlas: $57/month (M10 tier)
- Redis: $15/month (managed)
- AWS S3: ~$5/month (10GB storage + transfer)
- SendGrid: $15/month (40K emails)
- **Total**: ~$164/month

**At Scale (10K users)**:
- Frontend: $25/month (CDN + bandwidth)
- Backend Services (5 × $25): $125/month (auto-scaled)
- MongoDB Atlas: $180/month (M30 tier)
- Redis: $50/month (high availability)
- AWS S3: ~$50/month (100GB storage)
- SendGrid: $80/month (300K emails)
- **Total**: ~$510/month

---

### Error Handling & Logging Strategy

**Error Classification**

1. **Operational Errors** (Expected, Recoverable):
   - Invalid user input
   - Authentication failures
   - Resource not found
   - Rate limit exceeded
   - External API failures
   - **Handling**: Return user-friendly error, log warning

2. **Programmer Errors** (Unexpected, Bugs):
   - Null reference errors
   - Type errors
   - Unhandled promise rejections
   - **Handling**: Log error with stack trace, return generic 500 error

3. **Infrastructure Errors**:
   - Database connection failures
   - Redis connection failures
   - S3 upload failures
   - **Handling**: Retry with exponential backoff, fallback to degraded mode

**Error Handling Middleware**

```typescript
// services/*/src/middleware/error.middleware.ts
export const errorHandler = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  logger.error('Error occurred:', {
    message: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userId: req.user?.id
  });

  // Known operational errors
  if (error instanceof BaseError) {
    return res.status(error.statusCode).json(
      ResponseUtil.error(error.message, error.statusCode)
    );
  }

  // Validation errors (Zod)
  if (error instanceof ZodError) {
    return res.status(400).json(
      ResponseUtil.error('Validation failed', 400, error.errors)
    );
  }

  // JWT errors
  if (error.name === 'JsonWebTokenError') {
    return res.status(401).json(
      ResponseUtil.error('Invalid token', 401)
    );
  }

  if (error.name === 'TokenExpiredError') {
    return res.status(401).json(
      ResponseUtil.error('Token expired', 401)
    );
  }

  // MongoDB duplicate key
  if (error.code === 11000) {
    return res.status(409).json(
      ResponseUtil.error('Resource already exists', 409)
    );
  }

  // Unknown errors (don't expose details in production)
  const message = process.env.NODE_ENV === 'production'
    ? 'Internal server error'
    : error.message;

  res.status(500).json(
    ResponseUtil.error(message, 500)
  );
};
```

**Logging Architecture**

**Winston Logger Configuration**:
```typescript
// services/*/src/utils/logger.ts
import winston from 'winston';

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'service-name' },
  transports: [
    // Error logs
    new winston.transports.File({
      filename: 'logs/error.log',
      level: 'error',
      maxsize: 10485760, // 10MB
      maxFiles: 5
    }),
    // Combined logs
    new winston.transports.File({
      filename: 'logs/combined.log',
      maxsize: 10485760,
      maxFiles: 10
    })
  ]
});

// Console logging in development
if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.simple()
  }));
}
```

**Log Levels**:
- **error**: Application errors, exceptions
- **warn**: Warnings, deprecated features, rate limits
- **info**: General information, request logs, business events
- **debug**: Detailed debugging information (development only)

**Structured Logging**:
```typescript
// Request logging
logger.info('Request started', {
  method: req.method,
  url: req.url,
  ip: req.ip,
  userAgent: req.get('User-Agent'),
  userId: req.user?.id
});

// Business event logging
logger.info('User registered', {
  userId: user.id,
  email: user.email,
  registrationMethod: 'google',
  timestamp: new Date().toISOString()
});

// Error logging
logger.error('Database query failed', {
  error: error.message,
  stack: error.stack,
  query: 'User.findById',
  userId: userId
});
```

**Log Aggregation** (Planned):
- **Platform**: Datadog, Loggly, or ELK Stack
- **Features**:
  - Centralized log collection from all services
  - Real-time log search and filtering
  - Alerting on error patterns
  - Log retention: 30 days

---

### Performance Optimization

**Database Optimization**

1. **Indexing Strategy**:
   ```javascript
   // User Collection
   - email: unique, sparse (login queries)
   - googleId: unique, sparse (OAuth queries)
   - role: 1 (authorization queries)
   - subscriptionTier: 1 (feature access queries)
   - isDeleted: 1, isActive: 1 (filtering)
   - createdAt: -1 (sorting)
   - Compound: profile.location.country: 1, profile.location.city: 1 (location search)

   // Job Collection
   - status: 1, createdAt: -1 (active jobs listing)
   - externalId: 1, unique, sparse (deduplication)
   - Compound: status: 1, type: 1, location: 1 (filtered search)
   - Compound: skills: 1, status: 1, createdAt: -1 (skill-based search)
   - Text index: title, description, company (full-text search)

   // Application Collection
   - userId: 1 (user's applications)
   - jobId: 1 (job's applications)
   - status: 1 (status filtering)
   - appliedAt: -1 (chronological sorting)
   - Compound: userId: 1, status: 1 (user's filtered applications)

   // Resume Collection
   - userId: 1 (user's resumes)
   - isDefault: 1 (default resume lookup)
   - createdAt: -1 (chronological sorting)
   ```

2. **Query Optimization**:
   - **Projection**: Only select needed fields
     ```javascript
     User.findById(id).select('firstName lastName email avatar role');
     ```
   - **Lean Queries**: Use `.lean()` for read-only operations (10x faster)
     ```javascript
     Job.find({ status: 'active' }).lean();
     ```
   - **Pagination**: Always paginate large result sets
     ```javascript
     const skip = (page - 1) * limit;
     Job.find().skip(skip).limit(limit);
     ```
   - **Aggregation Pipeline**: Use for complex queries
     ```javascript
     Application.aggregate([
       { $match: { userId: userId } },
       { $group: { _id: '$status', count: { $sum: 1 } } }
     ]);
     ```

3. **Connection Pooling**:
   ```javascript
   mongoose.connect(MONGODB_URI, {
     maxPoolSize: 10,
     minPoolSize: 2,
     socketTimeoutMS: 45000,
     serverSelectionTimeoutMS: 5000
   });
   ```

**Caching Strategy** (Redis - Planned)

1. **Cache Layers**:
   - **L1 - In-Memory Cache**: Node.js Map (per service instance)
     - TTL: 5 minutes
     - Size: 100MB max
     - Use: Frequently accessed data (user profiles, job listings)

   - **L2 - Redis Cache**: Distributed cache
     - TTL: 1 hour (configurable per key)
     - Use: Session data, rate limits, job listings, user analytics

2. **Cache Keys**:
   ```javascript
   // User profile
   `user:${userId}:profile` → TTL: 15 minutes

   // Job listings (paginated)
   `jobs:active:page:${page}:limit:${limit}` → TTL: 5 minutes

   // User's applications
   `user:${userId}:applications:page:${page}` → TTL: 2 minutes

   // Rate limit
   `ratelimit:${userId}:${endpoint}` → TTL: 15 minutes

   // Session
   `session:${sessionId}` → TTL: 7 days
   ```

3. **Cache Invalidation**:
   - **Write-through**: Update cache on write
   - **TTL-based**: Automatic expiration
   - **Event-based**: Invalidate on specific events
     ```javascript
     // On profile update
     await redis.del(`user:${userId}:profile`);

     // On new job posted
     await redis.del('jobs:active:*'); // Clear all job listing caches
     ```

4. **Cache-Aside Pattern**:
   ```javascript
   async function getUserProfile(userId: string) {
     // Try cache first
     const cached = await redis.get(`user:${userId}:profile`);
     if (cached) {
       return JSON.parse(cached);
     }

     // Cache miss - fetch from DB
     const user = await User.findById(userId).lean();

     // Store in cache
     await redis.setex(
       `user:${userId}:profile`,
       900, // 15 minutes
       JSON.stringify(user)
     );

     return user;
   }
   ```

**API Response Optimization**

1. **Compression**:
   - Gzip compression for responses > 1KB
   - Brotli compression for static assets
   - Reduces bandwidth by 70-80%

2. **Response Pagination**:
   ```javascript
   {
     "data": [...],
     "pagination": {
       "page": 1,
       "limit": 10,
       "total": 150,
       "totalPages": 15,
       "hasNext": true,
       "hasPrev": false
     }
   }
   ```

3. **Field Selection**:
   - Allow clients to specify fields: `?fields=id,title,company`
   - Reduces payload size by 50-70%

4. **ETags & Conditional Requests**:
   - Generate ETag for cacheable responses
   - Support `If-None-Match` header
   - Return 304 Not Modified when appropriate

**Frontend Optimization**

1. **Code Splitting**:
   - Route-based code splitting
   - Lazy loading for heavy components
   - Reduces initial bundle size by 60%

2. **Asset Optimization**:
   - Image optimization (WebP format)
   - SVG for icons
   - Font subsetting
   - Minification & tree-shaking

3. **React Query Caching**:
   ```javascript
   const { data } = useQuery({
     queryKey: ['jobs', page],
     queryFn: () => fetchJobs(page),
     staleTime: 5 * 60 * 1000, // 5 minutes
     cacheTime: 10 * 60 * 1000 // 10 minutes
   });
   ```

**Performance Targets**

- **API Response Time**:
  - P50: < 100ms
  - P95: < 300ms
  - P99: < 500ms

- **Database Query Time**:
  - Simple queries: < 10ms
  - Complex queries: < 50ms
  - Aggregations: < 100ms

- **Frontend Load Time**:
  - First Contentful Paint: < 1.5s
  - Time to Interactive: < 3.5s
  - Largest Contentful Paint: < 2.5s

- **Throughput**:
  - 1000 requests/second per service
  - 10,000 concurrent users

---

### Testing Strategy

**Testing Pyramid**

```
        /\
       /  \
      / E2E \         10% - End-to-End Tests
     /______\
    /        \
   / Integration\     30% - Integration Tests
  /____________\
 /              \
/   Unit Tests   \   60% - Unit Tests
/________________\
```

**1. Unit Tests** (60% of tests)

**Framework**: Jest + TypeScript

**Coverage Targets**:
- Overall: 80%+
- Critical paths: 95%+
- Utilities: 90%+

**Example - User Service**:
```typescript
// services/user-service/src/tests/user.service.test.ts
describe('UserService', () => {
  describe('createUser', () => {
    it('should create user with valid data', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'SecurePass123',
        firstName: 'John',
        lastName: 'Doe'
      };

      const user = await UserService.createUser(userData);

      expect(user).toBeDefined();
      expect(user.email).toBe(userData.email);
      expect(user.password).toBeUndefined(); // Should not return password
    });

    it('should throw error for duplicate email', async () => {
      await expect(
        UserService.createUser({ email: '<EMAIL>', ... })
      ).rejects.toThrow('Email already exists');
    });

    it('should hash password before saving', async () => {
      const user = await UserService.createUser({ password: 'plain', ... });
      const dbUser = await User.findById(user.id).select('+password');

      expect(dbUser.password).not.toBe('plain');
      expect(dbUser.password).toMatch(/^\$2[aby]\$/); // bcrypt hash
    });
  });
});
```

**2. Integration Tests** (30% of tests)

**Framework**: Jest + Supertest

**Scope**: API endpoints, database interactions, external service mocks

**Example - Auth Routes**:
```typescript
// services/auth-service/src/tests/auth.routes.test.ts
describe('POST /api/v1/auth/register', () => {
  it('should register new user and return tokens', async () => {
    const response = await request(app)
      .post('/api/v1/auth/register')
      .send({
        email: '<EMAIL>',
        password: 'SecurePass123',
        firstName: 'John',
        lastName: 'Doe'
      })
      .expect(201);

    expect(response.body.success).toBe(true);
    expect(response.body.data.user).toBeDefined();
    expect(response.body.data.tokens.accessToken).toBeDefined();
    expect(response.body.data.tokens.refreshToken).toBeDefined();
  });

  it('should return 400 for invalid email', async () => {
    const response = await request(app)
      .post('/api/v1/auth/register')
      .send({ email: 'invalid-email', password: 'Pass123' })
      .expect(400);

    expect(response.body.success).toBe(false);
    expect(response.body.message).toContain('validation');
  });

  it('should enforce rate limiting', async () => {
    // Make 11 requests (limit is 10)
    for (let i = 0; i < 11; i++) {
      const response = await request(app)
        .post('/api/v1/auth/register')
        .send({ email: `user${i}@example.com`, password: 'Pass123' });

      if (i === 10) {
        expect(response.status).toBe(429);
        expect(response.body.message).toContain('Too many requests');
      }
    }
  });
});
```

**3. End-to-End Tests** (10% of tests)

**Framework**: Playwright or Cypress

**Scope**: Critical user flows

**Example - Job Application Flow**:
```typescript
// e2e/job-application.spec.ts
test('User can apply to a job', async ({ page }) => {
  // Login
  await page.goto('/login');
  await page.fill('[name="email"]', '<EMAIL>');
  await page.fill('[name="password"]', 'TestPass123');
  await page.click('button[type="submit"]');

  // Navigate to jobs
  await page.click('text=Browse Jobs');
  await page.waitForSelector('.job-card');

  // Select a job
  await page.click('.job-card:first-child');
  await page.waitForSelector('.job-details');

  // Apply
  await page.click('button:has-text("Apply Now")');
  await page.selectOption('[name="resumeId"]', { index: 0 });
  await page.fill('[name="coverLetter"]', 'I am interested in this position...');
  await page.click('button:has-text("Submit Application")');

  // Verify success
  await expect(page.locator('.success-message')).toContainText('Application submitted');

  // Verify in applications list
  await page.click('text=My Applications');
  await expect(page.locator('.application-card:first-child')).toBeVisible();
});
```

**4. Performance Tests**

**Framework**: Artillery or k6

**Example - Load Test**:
```yaml
# artillery-load-test.yml
config:
  target: 'https://api.resume-automator.com'
  phases:
    - duration: 60
      arrivalRate: 10
      name: Warm up
    - duration: 300
      arrivalRate: 50
      name: Sustained load
    - duration: 120
      arrivalRate: 100
      name: Peak load

scenarios:
  - name: Browse and apply to jobs
    flow:
      - post:
          url: '/api/v1/auth/login'
          json:
            email: '{{ $randomEmail }}'
            password: 'TestPass123'
          capture:
            - json: '$.data.tokens.accessToken'
              as: 'token'
      - get:
          url: '/api/v1/jobs?page=1&limit=10'
          headers:
            Authorization: 'Bearer {{ token }}'
      - post:
          url: '/api/v1/applications'
          headers:
            Authorization: 'Bearer {{ token }}'
          json:
            jobId: '{{ $randomJobId }}'
            resumeId: '{{ $randomResumeId }}'
```

**5. Security Tests**

**Tools**: OWASP ZAP, Burp Suite

**Tests**:
- SQL/NoSQL injection
- XSS vulnerabilities
- CSRF protection
- Authentication bypass
- Authorization flaws
- Rate limiting effectiveness
- Sensitive data exposure

**Test Automation**

**CI/CD Integration**:
```yaml
# .github/workflows/test.yml
name: Test Suite

on: [push, pull_request]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '22'
      - run: npm install
      - run: npm run test:unit
      - run: npm run test:coverage
      - uses: codecov/codecov-action@v3
        with:
          files: ./coverage/coverage-final.json

  integration-tests:
    runs-on: ubuntu-latest
    services:
      mongodb:
        image: mongo:7.0
        ports:
          - 27017:27017
      redis:
        image: redis:7.4
        ports:
          - 6379:6379
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: npm install
      - run: npm run test:integration

  e2e-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: npm install
      - run: npx playwright install
      - run: npm run test:e2e
```

**Current Test Coverage**:
- Unit Tests: ~40% (Target: 80%)
- Integration Tests: ~20% (Target: 60%)
- E2E Tests: 0% (Target: 30%)

---

### Monitoring & Observability

**Health Checks**

**Endpoint**: `GET /health`

**Response**:
```json
{
  "status": "healthy",
  "timestamp": "2025-09-30T10:00:00Z",
  "uptime": 86400,
  "service": "api-gateway",
  "version": "1.0.0",
  "checks": {
    "database": {
      "status": "healthy",
      "responseTime": 5,
      "message": "Connected to MongoDB"
    },
    "redis": {
      "status": "degraded",
      "responseTime": 150,
      "message": "High latency detected"
    },
    "externalApis": {
      "adzuna": "healthy",
      "indeed": "healthy"
    }
  }
}
```

**Metrics Collection** (Planned)

**Application Metrics**:
- Request rate (requests/second)
- Response time (P50, P95, P99)
- Error rate (errors/second)
- Active connections
- Database query time
- Cache hit/miss ratio

**Business Metrics**:
- User registrations (daily, weekly, monthly)
- Job applications submitted
- Resume uploads
- Active users (DAU, MAU)
- Conversion rates (signup → application)
- Premium subscriptions

**Infrastructure Metrics**:
- CPU usage
- Memory usage
- Disk I/O
- Network I/O
- Database connections
- Redis memory usage

**Logging Best Practices**

1. **Structured Logging**: Always use JSON format
2. **Correlation IDs**: Track requests across services
3. **Log Levels**: Use appropriate levels (error, warn, info, debug)
4. **Sensitive Data**: Never log passwords, tokens, PII
5. **Context**: Include userId, requestId, service name

**Alerting Strategy** (Planned)

**Critical Alerts** (Immediate notification):
- Service down (health check fails)
- Error rate > 5%
- Response time P95 > 1s
- Database connection failures
- Disk space > 90%

**Warning Alerts** (15-minute delay):
- Error rate > 1%
- Response time P95 > 500ms
- Cache hit ratio < 70%
- Memory usage > 80%

**Info Alerts** (Daily digest):
- New user registrations
- Application submissions
- System performance summary

**Monitoring Tools** (Planned):
- **APM**: Datadog, New Relic, or AppDynamics
- **Logs**: ELK Stack (Elasticsearch, Logstash, Kibana)
- **Uptime**: Pingdom or UptimeRobot
- **Errors**: Sentry or Rollbar

---

### Development Workflow

**Local Development Setup**

**Prerequisites**:
- Node.js 22.x
- npm 10.x
- Docker & Docker Compose
- Git
- MongoDB Compass (optional)
- Postman or Insomnia (optional)

**Setup Steps**:
```bash
# 1. Clone repository
git clone https://github.com/your-org/resume-automator.git
cd resume-automator

# 2. Install dependencies (all services)
npm run install:all

# 3. Copy environment template
cp env-templates/main.env .env

# 4. Configure environment variables
# Edit .env with your local settings

# 5. Start infrastructure (MongoDB, Redis)
docker-compose up -d mongodb redis

# 6. Run database migrations (if any)
npm run migrate

# 7. Seed database with test data
npm run seed

# 8. Start all services in development mode
npm run dev

# Services will be available at:
# - Frontend: http://localhost:5173
# - API Gateway: http://localhost:3000
# - Auth Service: http://localhost:3001
# - User Service: http://localhost:3002
# - Job Service: http://localhost:3003
# - Resume Service: http://localhost:3005
```

**Development Scripts**:
```json
{
  "scripts": {
    "dev": "concurrently \"npm run dev:gateway\" \"npm run dev:auth\" \"npm run dev:user\" \"npm run dev:job\" \"npm run dev:resume\" \"npm run dev:frontend\"",
    "dev:gateway": "cd services/api-gateway && npm run dev",
    "dev:auth": "cd services/auth-service && npm run dev",
    "dev:user": "cd services/user-service && npm run dev",
    "dev:job": "cd services/job-service && npm run dev",
    "dev:resume": "cd services/resume-service && npm run dev",
    "dev:frontend": "cd services/frontend-service && npm run dev",

    "build": "npm run build:gateway && npm run build:auth && npm run build:user && npm run build:job && npm run build:resume && npm run build:frontend",

    "test": "npm run test:unit && npm run test:integration",
    "test:unit": "jest --coverage",
    "test:integration": "jest --config jest.integration.config.js",
    "test:e2e": "playwright test",

    "lint": "eslint . --ext .ts,.tsx",
    "lint:fix": "eslint . --ext .ts,.tsx --fix",
    "format": "prettier --write \"**/*.{ts,tsx,json,md}\"",

    "migrate": "node scripts/migrate.js",
    "seed": "node scripts/seed.js",
    "clean": "node scripts/clean.js"
  }
}
```

**Git Workflow**

**Branch Strategy**:
- `main`: Production-ready code
- `develop`: Integration branch
- `feature/*`: New features
- `bugfix/*`: Bug fixes
- `hotfix/*`: Production hotfixes

**Commit Convention** (Conventional Commits):
```
<type>(<scope>): <subject>

<body>

<footer>
```

**Types**:
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation
- `style`: Code style (formatting)
- `refactor`: Code refactoring
- `test`: Tests
- `chore`: Build/tooling

**Example**:
```
feat(auth): add Google OAuth integration

- Implement OAuth 2.0 flow
- Add Google strategy to Passport
- Create callback handler
- Update user model with googleId

Closes #123
```

**Pull Request Process**:
1. Create feature branch from `develop`
2. Implement changes with tests
3. Run linter and tests locally
4. Push branch and create PR
5. Code review (2 approvals required)
6. CI/CD pipeline passes
7. Merge to `develop`
8. Deploy to staging
9. QA testing
10. Merge to `main` for production

**Code Review Checklist**:
- [ ] Code follows style guide
- [ ] Tests added/updated
- [ ] Documentation updated
- [ ] No security vulnerabilities
- [ ] Performance considerations
- [ ] Error handling implemented
- [ ] Logging added
- [ ] Breaking changes documented

---

### Cost Analysis & Scaling

**Current Costs** (Development):

| Service | Provider | Tier | Monthly Cost |
|---------|----------|------|--------------|
| Frontend Hosting | DigitalOcean | Basic | $5 |
| API Gateway | DigitalOcean | Basic XXS | $5 |
| Auth Service | DigitalOcean | Basic XXS | $5 |
| User Service | DigitalOcean | Basic XXS | $5 |
| Job Service | DigitalOcean | Basic XXS | $5 |
| Resume Service | DigitalOcean | Basic XXS | $5 |
| MongoDB Atlas | MongoDB | M0 (Free) | $0 |
| AWS S3 | AWS | Pay-as-you-go | ~$1 |
| **Total** | | | **~$31/month** |

**Projected Costs at Scale**:

**1,000 Users** (~$100/month):
- Frontend: $12 (Professional)
- Backend Services: $60 (5 × $12)
- MongoDB: $25 (M10 Shared)
- Redis: $15 (Basic)
- S3: $3 (5GB storage)
- SendGrid: $15 (10K emails)

**10,000 Users** (~$500/month):
- Frontend: $25 (CDN + bandwidth)
- Backend Services: $125 (5 × $25, auto-scaled)
- MongoDB: $180 (M30 Dedicated)
- Redis: $50 (High availability)
- S3: $50 (100GB storage)
- SendGrid: $80 (300K emails)
- Monitoring: $50 (Datadog)

**100,000 Users** (~$3,000/month):
- Frontend: $100 (CDN + high bandwidth)
- Backend Services: $1,000 (5 × $200, multi-instance)
- MongoDB: $800 (M60 Dedicated cluster)
- Redis: $200 (Cluster mode)
- S3: $400 (1TB storage + transfer)
- SendGrid: $500 (3M emails)
- Monitoring: $200 (Datadog Pro)
- CDN: $300 (CloudFront)
- Support: $500 (Premium support)

**Revenue Model**:

**Free Tier** ($0/month):
- 5 job applications/month
- 1 resume upload
- Basic job search
- Email notifications

**Basic Tier** ($9.99/month):
- 50 job applications/month
- 5 resume uploads
- Resume ATS analysis
- Priority support

**Premium Tier** ($29.99/month):
- Unlimited applications
- Unlimited resumes
- AI resume optimization
- Cover letter generation
- Application tracking
- Interview preparation
- Priority job alerts

**Enterprise Tier** (Custom pricing):
- All Premium features
- Dedicated account manager
- Custom integrations
- API access
- White-label option
- SLA guarantees

**Break-Even Analysis**:

**Monthly Operating Costs** (10K users): $500

**Required Paid Users**:
- Basic ($9.99): 50 users
- Premium ($29.99): 17 users
- Mix (10% conversion, 70% Basic, 30% Premium):
  - 700 Basic + 300 Premium = $6,993 + $8,997 = **$15,990/month**
  - **Profit**: $15,490/month

**Customer Acquisition Cost (CAC)**:
- Target: $20/user
- Channels: Google Ads, LinkedIn Ads, Content Marketing
- Lifetime Value (LTV): $360 (12 months × $30 avg)
- LTV:CAC Ratio: 18:1 (Excellent)

---

### Success Metrics & KPIs

**User Acquisition Metrics**:
- **New Signups**: Daily, Weekly, Monthly
  - Target: 100/day (3,000/month)
- **Activation Rate**: % of signups who complete profile
  - Target: 70%
- **Conversion Rate**: Free → Paid
  - Target: 10%
- **Referral Rate**: % of users who refer others
  - Target: 15%

**Engagement Metrics**:
- **Daily Active Users (DAU)**: Unique users per day
  - Target: 5,000
- **Monthly Active Users (MAU)**: Unique users per month
  - Target: 20,000
- **DAU/MAU Ratio**: Stickiness metric
  - Target: 25%
- **Session Duration**: Average time per session
  - Target: 8 minutes
- **Sessions per User**: Average sessions per week
  - Target: 3

**Feature Usage Metrics**:
- **Resume Uploads**: Uploads per user
  - Target: 2.5 avg
- **Job Applications**: Applications per user per month
  - Target: 15 (free), 40 (premium)
- **Job Searches**: Searches per session
  - Target: 5
- **Profile Completeness**: % of profile fields filled
  - Target: 80%

**Business Metrics**:
- **Monthly Recurring Revenue (MRR)**: Total subscription revenue
  - Target: $50,000/month (Year 1)
- **Annual Recurring Revenue (ARR)**: MRR × 12
  - Target: $600,000 (Year 1)
- **Churn Rate**: % of users who cancel
  - Target: < 5%/month
- **Customer Lifetime Value (LTV)**: Average revenue per user
  - Target: $360
- **Customer Acquisition Cost (CAC)**: Cost to acquire one user
  - Target: $20

**Product Quality Metrics**:
- **Application Success Rate**: % of applications that get responses
  - Target: 25%
- **Interview Rate**: % of applications that lead to interviews
  - Target: 10%
- **Offer Rate**: % of applications that lead to offers
  - Target: 3%
- **Resume ATS Score**: Average ATS compatibility score
  - Target: 75/100

**Technical Metrics**:
- **API Response Time**: P95 response time
  - Target: < 300ms
- **Uptime**: Service availability
  - Target: 99.9%
- **Error Rate**: % of requests that error
  - Target: < 0.1%
- **Page Load Time**: Time to interactive
  - Target: < 3s

**Support Metrics**:
- **First Response Time**: Time to first support response
  - Target: < 2 hours
- **Resolution Time**: Time to resolve issue
  - Target: < 24 hours
- **Customer Satisfaction (CSAT)**: Support satisfaction score
  - Target: > 90%
- **Net Promoter Score (NPS)**: Likelihood to recommend
  - Target: > 50

**Measurement Tools**:
- **Analytics**: Google Analytics, Mixpanel
- **Product Analytics**: Amplitude, Heap
- **User Feedback**: Intercom, Zendesk
- **A/B Testing**: Optimizely, VWO
- **Heatmaps**: Hotjar, FullStory

---

### Detailed Feature Roadmap

**Phase 1: MVP** (Completed ~80%)
- [x] User authentication (email/password, Google OAuth)
- [x] User profile management
- [x] Resume upload and parsing
- [x] Basic ATS analysis
- [x] Job aggregation (Adzuna, Indeed)
- [x] Job search and filtering
- [x] Job application submission
- [x] Application tracking
- [x] Basic analytics dashboard
- [ ] Email notifications (mocked)
- [ ] Redis caching (disabled)

**Phase 2: Core Features** (Q4 2025)
- [ ] Email service integration (SendGrid)
- [ ] Redis caching implementation
- [ ] Advanced resume parsing (AI-powered)
- [ ] Resume optimization suggestions
- [ ] Cover letter generation (AI)
- [ ] Job matching algorithm
- [ ] Application status tracking (external)
- [ ] Interview preparation resources
- [ ] Salary insights
- [ ] Company reviews integration

**Phase 3: Premium Features** (Q1 2026)
- [ ] AI-powered resume builder
- [ ] Custom resume templates
- [ ] LinkedIn profile optimization
- [ ] Portfolio website generator
- [ ] Video interview practice (AI)
- [ ] Skill assessments
- [ ] Career path recommendations
- [ ] Networking suggestions
- [ ] Job market insights
- [ ] Personalized job alerts

**Phase 4: Enterprise Features** (Q2 2026)
- [ ] Bulk resume management
- [ ] Team collaboration
- [ ] Recruiter dashboard
- [ ] Applicant tracking system (ATS)
- [ ] API access for integrations
- [ ] White-label solution
- [ ] Custom branding
- [ ] SSO integration
- [ ] Advanced analytics
- [ ] Compliance tools (GDPR, EEOC)

**Phase 5: Advanced AI** (Q3 2026)
- [ ] Predictive job matching
- [ ] Interview outcome prediction
- [ ] Salary negotiation assistant
- [ ] Career trajectory analysis
- [ ] Skills gap analysis
- [ ] Learning path recommendations
- [ ] Automated follow-ups
- [ ] Smart scheduling
- [ ] Sentiment analysis (company reviews)
- [ ] Market trend predictions

**Phase 6: Ecosystem** (Q4 2026)
- [ ] Mobile apps (iOS, Android)
- [ ] Browser extension
- [ ] Slack/Teams integration
- [ ] Calendar integration
- [ ] CRM integration
- [ ] Background check integration
- [ ] Reference check automation
- [ ] Offer letter generation
- [ ] Onboarding automation
- [ ] Alumni network

**Feature Prioritization Matrix**:

| Feature | Impact | Effort | Priority |
|---------|--------|--------|----------|
| Email notifications | High | Low | P0 |
| Redis caching | High | Medium | P0 |
| AI resume optimization | High | High | P1 |
| Cover letter generation | High | Medium | P1 |
| Job matching algorithm | High | High | P1 |
| Mobile apps | Medium | High | P2 |
| Video interview practice | Medium | High | P2 |
| API access | Low | Medium | P3 |

---

### Risk Assessment & Mitigation

**Technical Risks**:

1. **Database Scalability**:
   - **Risk**: MongoDB performance degrades with large datasets
   - **Impact**: High
   - **Probability**: Medium
   - **Mitigation**:
     - Implement sharding strategy
     - Use read replicas
     - Optimize indexes
     - Consider migration to PostgreSQL for relational data

2. **External API Reliability**:
   - **Risk**: Adzuna/Indeed API downtime or rate limits
   - **Impact**: High
   - **Probability**: Medium
   - **Mitigation**:
     - Cache job listings (Redis)
     - Implement fallback to other APIs
     - Store historical data
     - Build internal job posting system

3. **File Storage Costs**:
   - **Risk**: S3 costs escalate with user growth
   - **Impact**: Medium
   - **Probability**: High
   - **Mitigation**:
     - Implement file size limits
     - Compress files before upload
     - Archive old resumes to Glacier
     - Consider alternative storage (DigitalOcean Spaces)

**Business Risks**:

1. **Low User Adoption**:
   - **Risk**: Users don't see value, high churn
   - **Impact**: High
   - **Probability**: Medium
   - **Mitigation**:
     - Continuous user feedback
     - A/B testing features
     - Improve onboarding
     - Offer free trial of premium features

2. **Competition**:
   - **Risk**: Established players (LinkedIn, Indeed) add similar features
   - **Impact**: High
   - **Probability**: Medium
   - **Mitigation**:
     - Focus on niche (AI-powered automation)
     - Build strong brand
     - Rapid feature development
     - Superior user experience

3. **Regulatory Compliance**:
   - **Risk**: GDPR, CCPA, employment law violations
   - **Impact**: High
   - **Probability**: Low
   - **Mitigation**:
     - Legal review of features
     - Privacy-first design
     - Data retention policies
     - Regular compliance audits

**Security Risks**:

1. **Data Breach**:
   - **Risk**: Unauthorized access to user data
   - **Impact**: Critical
   - **Probability**: Low
   - **Mitigation**:
     - Regular security audits
     - Penetration testing
     - Encryption at rest and in transit
     - Incident response plan

2. **Account Takeover**:
   - **Risk**: Weak authentication, credential stuffing
   - **Impact**: High
   - **Probability**: Medium
   - **Mitigation**:
     - Enforce strong passwords
     - Implement 2FA
     - Monitor suspicious activity
     - Rate limiting on auth endpoints

---

## APPENDICES

### Appendix A: Glossary

- **ATS**: Applicant Tracking System - Software used by employers to manage job applications
- **JWT**: JSON Web Token - Secure method for transmitting information between parties
- **OAuth**: Open Authorization - Standard for access delegation
- **CORS**: Cross-Origin Resource Sharing - Security feature for web browsers
- **CDN**: Content Delivery Network - Distributed server network for fast content delivery
- **TTL**: Time To Live - Duration data is valid in cache
- **RBAC**: Role-Based Access Control - Access control based on user roles
- **SLA**: Service Level Agreement - Commitment to service availability
- **MRR**: Monthly Recurring Revenue - Predictable monthly revenue
- **ARR**: Annual Recurring Revenue - MRR × 12
- **CAC**: Customer Acquisition Cost - Cost to acquire one customer
- **LTV**: Lifetime Value - Total revenue from a customer
- **DAU**: Daily Active Users - Unique users per day
- **MAU**: Monthly Active Users - Unique users per month

### Appendix B: API Error Codes

| Code | Message | Description |
|------|---------|-------------|
| 400 | Bad Request | Invalid request format or parameters |
| 401 | Unauthorized | Missing or invalid authentication token |
| 403 | Forbidden | Insufficient permissions |
| 404 | Not Found | Resource does not exist |
| 409 | Conflict | Resource already exists (duplicate) |
| 422 | Unprocessable Entity | Validation failed |
| 429 | Too Many Requests | Rate limit exceeded |
| 500 | Internal Server Error | Unexpected server error |
| 502 | Bad Gateway | Upstream service error |
| 503 | Service Unavailable | Service temporarily down |

### Appendix C: Environment Variables Reference

See `env-templates/main.env` for complete list with descriptions.

### Appendix D: Database Migration Scripts

Located in `scripts/migrations/` directory.

### Appendix E: API Postman Collection

Available at: `docs/postman/Resume-Automator-API.postman_collection.json`

---

**Document Prepared By**: Alex, Principal Full-Stack Engineer
**For**: Resume-Automator Development Team
**Last Updated**: 2025-09-30
**Next Review**: 2025-10-30
**Version**: 2.0 (Comprehensive Edition)

---

**Document Status**: ✅ **COMPLETE**

This comprehensive Product Requirements Document now includes:
- ✅ Vision & Purpose (WHY)
- ✅ Core Requirements (WHAT)
- ✅ Technical Implementation (HOW)
- ✅ Business Requirements
- ✅ Database Schema Documentation
- ✅ Complete API Reference
- ✅ Security Architecture
- ✅ System Architecture & Data Flow
- ✅ Deployment & Infrastructure
- ✅ Error Handling & Logging
- ✅ Performance Optimization
- ✅ Testing Strategy
- ✅ Monitoring & Observability
- ✅ Development Workflow
- ✅ Cost Analysis & Scaling
- ✅ Success Metrics & KPIs
- ✅ Detailed Feature Roadmap
- ✅ Risk Assessment & Mitigation
- ✅ Appendices & References

**Total Lines**: ~2,900 lines of comprehensive documentation

