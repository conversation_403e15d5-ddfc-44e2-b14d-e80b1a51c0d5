import React from 'react';
import { cn } from '@/utils/cn';

export interface CheckboxProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string | React.ReactNode;
  description?: string;
  error?: string;
}

const Checkbox = React.forwardRef<HTMLInputElement, CheckboxProps>(
  ({ className, label, description, error, id, ...props }, ref) => {
    const checkboxId = id || `checkbox-${Math.random().toString(36).substring(2, 11)}`;
    
    return (
      <div className="flex items-start space-x-3">
        <div className="flex items-center h-5">
          <input
            id={checkboxId}
            type="checkbox"
            className={cn(
              'h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded',
              error && 'border-red-300 focus:ring-red-500',
              className
            )}
            ref={ref}
            {...props}
          />
        </div>
        <div className="text-sm">
          {label && (
            <label
              htmlFor={checkboxId}
              className={cn(
                'font-medium text-gray-900 cursor-pointer',
                error && 'text-red-700'
              )}
            >
              {label}
            </label>
          )}
          {description && (
            <p className={cn(
              'text-gray-500',
              error && 'text-red-600'
            )}>
              {description}
            </p>
          )}
          {error && (
            <p className="text-red-600 text-sm mt-1">{error}</p>
          )}
        </div>
      </div>
    );
  }
);

Checkbox.displayName = 'Checkbox';

export { Checkbox };