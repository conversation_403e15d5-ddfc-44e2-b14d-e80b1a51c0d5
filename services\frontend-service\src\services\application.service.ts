import { apiService } from './api';
import { 
  Application, 
  CreateApplicationRequest, 
  ApplicationSearchParams, 
  PaginatedResponse,
  UserAnalytics,
  ApplicationWithTimeline
} from '@/types/api';

class ApplicationService {
  // Application management
  async getApplications(params?: ApplicationSearchParams): Promise<PaginatedResponse<Application>> {
    return apiService.get<PaginatedResponse<Application>>('/applications', { params });
  }

  async getApplicationById(id: string): Promise<Application> {
    return apiService.get<Application>(`/applications/${id}`);
  }

  async createApplication(applicationData: CreateApplicationRequest): Promise<Application> {
    return apiService.post<Application>('/applications', applicationData);
  }

  async updateApplication(id: string, applicationData: {
    coverLetter?: string;
    customFields?: Record<string, any>;
  }): Promise<Application> {
    return apiService.put<Application>(`/applications/${id}`, applicationData);
  }

  async withdrawApplication(id: string, reason?: string): Promise<Application> {
    return apiService.post<Application>(`/applications/${id}/withdraw`, { reason });
  }

  async deleteApplication(id: string): Promise<void> {
    return apiService.delete(`/applications/${id}`);
  }

  // Application status tracking
  async getApplicationTimeline(id: string): Promise<ApplicationWithTimeline['timeline']> {
    return apiService.get(`/applications/${id}/timeline`);
  }

  async addTimelineEvent(id: string, event: {
    type: string;
    message: string;
    status?: Application['status'];
  }): Promise<void> {
    return apiService.post(`/applications/${id}/timeline`, event);
  }

  // Bulk operations
  async bulkWithdraw(applicationIds: string[], reason?: string): Promise<void> {
    return apiService.post('/applications/bulk/withdraw', { 
      applicationIds, 
      reason 
    });
  }

  async bulkDelete(applicationIds: string[]): Promise<void> {
    return apiService.delete('/applications/bulk', { 
      data: { applicationIds } 
    });
  }

  // Application analytics
  async getApplicationAnalytics(): Promise<UserAnalytics> {
    return apiService.get<UserAnalytics>('/applications/analytics');
  }

  async getApplicationStats(dateRange?: { start: string; end: string }): Promise<{
    totalApplications: number;
    applicationsByStatus: Record<Application['status'], number>;
    applicationsByMonth: Array<{ month: string; count: number }>;
    topCompanies: Array<{ company: string; count: number }>;
    averageResponseTime: number;
    responseRate: number;
  }> {
    return apiService.get('/applications/stats', { params: dateRange });
  }

  // Application templates
  async getCoverLetterTemplates(): Promise<Array<{
    id: string;
    name: string;
    content: string;
    category: string;
    isDefault: boolean;
  }>> {
    return apiService.get('/applications/templates/cover-letters');
  }

  async createCoverLetterTemplate(template: {
    name: string;
    content: string;
    category: string;
  }): Promise<{ id: string }> {
    return apiService.post('/applications/templates/cover-letters', template);
  }

  async updateCoverLetterTemplate(id: string, template: {
    name?: string;
    content?: string;
    category?: string;
  }): Promise<void> {
    return apiService.put(`/applications/templates/cover-letters/${id}`, template);
  }

  async deleteCoverLetterTemplate(id: string): Promise<void> {
    return apiService.delete(`/applications/templates/cover-letters/${id}`);
  }

  // AI-powered features
  async generateCoverLetter(params: {
    jobId: string;
    resumeId: string;
    tone?: 'professional' | 'casual' | 'enthusiastic';
    length?: 'short' | 'medium' | 'long';
    focusAreas?: string[];
  }): Promise<{ content: string; suggestions: string[] }> {
    return apiService.post('/applications/generate-cover-letter', params);
  }

  async optimizeApplication(applicationId: string): Promise<{
    suggestions: Array<{
      type: 'cover_letter' | 'resume' | 'skills';
      message: string;
      priority: 'high' | 'medium' | 'low';
    }>;
    optimizedCoverLetter?: string;
  }> {
    return apiService.post(`/applications/${applicationId}/optimize`);
  }

  // Application tracking
  async trackApplicationView(applicationId: string): Promise<void> {
    return apiService.post(`/applications/${applicationId}/track-view`);
  }

  async getApplicationInsights(applicationId: string): Promise<{
    viewedByEmployer: boolean;
    lastViewedAt?: string;
    timeSpentViewing?: number;
    documentsDownloaded: string[];
    similarApplications: number;
    competitionLevel: 'low' | 'medium' | 'high';
  }> {
    return apiService.get(`/applications/${applicationId}/insights`);
  }

  // Export and reporting
  async exportApplications(format: 'csv' | 'pdf' | 'excel', filters?: ApplicationSearchParams): Promise<void> {
    return apiService.download('/applications/export', `applications.${format}`, {
      params: { format, ...filters }
    });
  }

  async generateApplicationReport(type: 'summary' | 'detailed' | 'analytics'): Promise<void> {
    return apiService.download(`/applications/reports/${type}`, `application-report-${type}.pdf`);
  }

  // Interview scheduling
  async scheduleInterview(applicationId: string, interviewData: {
    type: 'phone' | 'video' | 'in_person';
    scheduledAt: string;
    duration: number;
    location?: string;
    meetingLink?: string;
    notes?: string;
  }): Promise<void> {
    return apiService.post(`/applications/${applicationId}/interview`, interviewData);
  }

  async getUpcomingInterviews(): Promise<Array<{
    id: string;
    applicationId: string;
    jobTitle: string;
    company: string;
    type: string;
    scheduledAt: string;
    duration: number;
    location?: string;
    meetingLink?: string;
    status: 'scheduled' | 'completed' | 'cancelled';
  }>> {
    return apiService.get('/applications/interviews');
  }

  async updateInterviewStatus(interviewId: string, status: 'completed' | 'cancelled', notes?: string): Promise<void> {
    return apiService.put(`/applications/interviews/${interviewId}`, { status, notes });
  }
}

export const applicationService = new ApplicationService();
