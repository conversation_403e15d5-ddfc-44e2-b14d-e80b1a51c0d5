{"compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "allowJs": true, "skipLibCheck": true, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "noImplicitOverride": true, "exactOptionalPropertyTypes": true, "forceConsistentCasingInFileNames": true, "module": "CommonJS", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "outDir": "dist", "rootDir": ".", "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "baseUrl": ".", "paths": {"@shared/*": ["shared/*"], "@services/*": ["services/*"]}}, "include": ["services/api-gateway/**/*", "services/auth-service/**/*", "services/job-service/**/*", "services/resume-service/**/*", "services/user-service/**/*", "shared/**/*", "*.ts"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts", "services/frontend-service/**/*"], "ts-node": {"esm": true, "experimentalSpecifierResolution": "node"}}