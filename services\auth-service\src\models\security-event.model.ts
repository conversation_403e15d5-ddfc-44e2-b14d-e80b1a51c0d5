import { Schema, model } from 'mongoose';

const securityEventSchema = new Schema(
  {
    userId: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: 'User',
    },
    eventType: {
      type: String,
      required: true,
      enum: [
        'login',
        'logout',
        'failed_login',
        'password_change',
        'account_locked',
        'suspicious_activity',
        '2fa_enabled',
        '2fa_disabled',
        'password_reset',
        'user_registered',
      ],
    },
    ipAddress: {
      type: String,
      required: true,
    },
    userAgent: {
      type: String,
      required: true,
    },
    location: {
      country: String,
      region: String,
      city: String,
    },
    metadata: {
      type: Schema.Types.Mixed,
    },
    riskLevel: {
      type: String,
      enum: ['low', 'medium', 'high'],
      default: 'low',
    },
  },
  {
    timestamps: true,
    versionKey: false,
  }
);

// Indexes
securityEventSchema.index({ userId: 1, createdAt: -1 });
securityEventSchema.index({ eventType: 1 });
securityEventSchema.index({ riskLevel: 1, createdAt: -1 });

export const SecurityEvent = model('SecurityEvent', securityEventSchema);
