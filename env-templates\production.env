# Production Environment Variables for DigitalOcean

# API Gateway Configuration
NODE_ENV=production
PORT=3000

# Service URLs - Update these to match your actual service URLs
AUTH_SERVICE_URL=https://jobs-app-ydwim.ondigitalocean.app
USER_SERVICE_URL=https://jobs-app-ydwim.ondigitalocean.app/user
JOB_SERVICE_URL=https://jobs-app-ydwim.ondigitalocean.app/jobs
RESUME_SERVICE_URL=https://jobs-app-ydwim.ondigitalocean.app/resume
ANALYTICS_SERVICE_URL=https://jobs-app-ydwim.ondigitalocean.app/analytics
NOTIFICATION_SERVICE_URL=http://localhost:3006      
INTEGRATION_SERVICE_URL=http://localhost:3007
PAYMENT_SERVICE_URL=http://localhost:3008

# Database Configuration
MONGODB_URI=mongodb+srv://ResumeAdmin:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0ResumePlatform
REDIS_URL=redis://localhost:6379

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production-must-be-32-chars-minimum
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-in-production-must-be-32-chars

# CORS Configuration
CORS_ORIGIN=https://stingray-app-7geup.ondigitalocean.app

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
