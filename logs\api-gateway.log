ExpressSlowDownWarning: The behaviour of the 'delayMs' option was changed in express-slow-down v2:
- For the old behavior, change the delayMs option to:

  delayMs: (used, req) => {
	  const delayAfter = req.slowDown.limit;
	  return (used - delayAfter) * 500;
  },

- For the new behavior, change the delayMs option to:

	delayMs: () => 500,

Or set 'options.validate: {delayMs: false}' to disable this message. See https://express-rate-limit.github.io/WRN_ESD_DELAYMS/ for more information.
    at slowDown (/Users/<USER>/Developer/JobApplicationAutomator/Backend/node_modules/express-slow-down/dist/index.cjs:78:18)
    at rateLimit (/Users/<USER>/Developer/JobApplicationAutomator/Backend/services/api-gateway/src/middleware/rate-limit.middleware.ts:36:37)
    at Object.<anonymous> (/Users/<USER>/Developer/JobApplicationAutomator/Backend/services/api-gateway/src/middleware/rate-limit.middleware.ts:91:2)
    at Module._compile (node:internal/modules/cjs/loader:1688:14)
    at Object.transformer (/Users/<USER>/Developer/JobApplicationAutomator/Backend/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)
    at Module.load (node:internal/modules/cjs/loader:1423:32)
    at Function._load (node:internal/modules/cjs/loader:1246:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1445:12) {
  code: 'WRN_ESD_DELAYMS',
  help: 'https://express-rate-limit.github.io/WRN_ESD_DELAYMS/'
}
ChangeWarning: The onLimitReached configuration option is deprecated and has been removed in express-rate-limit v7. See https://express-rate-limit.github.io/WRN_ERL_DEPRECATED_ON_LIMIT_REACHED/ for more information.
    at Object.onLimitReached (/Users/<USER>/Developer/JobApplicationAutomator/Backend/node_modules/express-rate-limit/dist/index.cjs:289:13)
    at wrappedValidations.<computed> [as onLimitReached] (/Users/<USER>/Developer/JobApplicationAutomator/Backend/node_modules/express-rate-limit/dist/index.cjs:397:22)
    at parseOptions (/Users/<USER>/Developer/JobApplicationAutomator/Backend/node_modules/express-rate-limit/dist/index.cjs:626:16)
    at rateLimit (/Users/<USER>/Developer/JobApplicationAutomator/Backend/node_modules/express-rate-limit/dist/index.cjs:697:18)
    at slowDown (/Users/<USER>/Developer/JobApplicationAutomator/Backend/node_modules/express-slow-down/dist/index.cjs:124:50)
    at rateLimit (/Users/<USER>/Developer/JobApplicationAutomator/Backend/services/api-gateway/src/middleware/rate-limit.middleware.ts:36:37)
    at Object.<anonymous> (/Users/<USER>/Developer/JobApplicationAutomator/Backend/services/api-gateway/src/middleware/rate-limit.middleware.ts:91:2)
    at Module._compile (node:internal/modules/cjs/loader:1688:14)
    at Object.transformer (/Users/<USER>/Developer/JobApplicationAutomator/Backend/node_modules/tsx/dist/register-D46fvsV_.cjs:3:1104)
    at Module.load (node:internal/modules/cjs/loader:1423:32) {
  code: 'WRN_ERL_DEPRECATED_ON_LIMIT_REACHED',
  help: 'https://express-rate-limit.github.io/WRN_ERL_DEPRECATED_ON_LIMIT_REACHED/'
}
info: Registered 8 services {"service":"api-gateway","timestamp":"2025-09-21T10:47:47.712Z"}
info: Health checks started with 30000ms interval {"service":"api-gateway","timestamp":"2025-09-21T10:47:47.714Z"}
info: MongoDB connected successfully {"service":"api-gateway","timestamp":"2025-09-21T10:47:48.482Z"}
info: Database connected successfully {"service":"api-gateway","timestamp":"2025-09-21T10:47:48.484Z"}
info: 🚀 API Gateway running on port 3000 {"service":"api-gateway","timestamp":"2025-09-21T10:47:48.487Z"}
info: Environment: development {"service":"api-gateway","timestamp":"2025-09-21T10:47:48.488Z"}
info: Available routes: {"service":"api-gateway","timestamp":"2025-09-21T10:47:48.488Z"}
info:   GET  /health - Health check {"service":"api-gateway","timestamp":"2025-09-21T10:47:48.489Z"}
info:   GET  /metrics - Service metrics {"service":"api-gateway","timestamp":"2025-09-21T10:47:48.489Z"}
info:   GET  /api - API information {"service":"api-gateway","timestamp":"2025-09-21T10:47:48.489Z"}
info:   POST /api/v1/auth/* - Authentication {"service":"api-gateway","timestamp":"2025-09-21T10:47:48.490Z"}
info:   *    /api/v1/users/* - User management {"service":"api-gateway","timestamp":"2025-09-21T10:47:48.490Z"}
info:   *    /api/v1/jobs/* - Job management {"service":"api-gateway","timestamp":"2025-09-21T10:47:48.490Z"}
info:   *    /api/v1/resumes/* - Resume management {"service":"api-gateway","timestamp":"2025-09-21T10:47:48.490Z"}
info:   *    /api/v1/applications/* - Application management {"service":"api-gateway","timestamp":"2025-09-21T10:47:48.490Z"}
warn: Health check failed for analytics-service: fetch failed {"service":"api-gateway","timestamp":"2025-09-21T10:48:17.737Z"}
warn: Health check failed for notification-service: fetch failed {"service":"api-gateway","timestamp":"2025-09-21T10:48:17.738Z"}
warn: Health check failed for payment-service: fetch failed {"service":"api-gateway","timestamp":"2025-09-21T10:48:17.742Z"}
warn: Health check failed for integration-service: fetch failed {"service":"api-gateway","timestamp":"2025-09-21T10:48:17.742Z"}
debug: Health check completed: 4/8 services healthy {"service":"api-gateway","timestamp":"2025-09-21T10:48:17.742Z"}
warn: Health check failed for analytics-service: fetch failed {"service":"api-gateway","timestamp":"2025-09-21T10:48:47.722Z"}
warn: Health check failed for notification-service: fetch failed {"service":"api-gateway","timestamp":"2025-09-21T10:48:47.723Z"}
warn: Health check failed for integration-service: fetch failed {"service":"api-gateway","timestamp":"2025-09-21T10:48:47.723Z"}
warn: Health check failed for payment-service: fetch failed {"service":"api-gateway","timestamp":"2025-09-21T10:48:47.723Z"}
debug: Health check completed: 4/8 services healthy {"service":"api-gateway","timestamp":"2025-09-21T10:48:47.724Z"}
warn: Health check failed for notification-service: fetch failed {"service":"api-gateway","timestamp":"2025-09-21T10:49:17.721Z"}
warn: Health check failed for integration-service: fetch failed {"service":"api-gateway","timestamp":"2025-09-21T10:49:17.722Z"}
warn: Health check failed for analytics-service: fetch failed {"service":"api-gateway","timestamp":"2025-09-21T10:49:17.725Z"}
warn: Health check failed for payment-service: fetch failed {"service":"api-gateway","timestamp":"2025-09-21T10:49:17.725Z"}
debug: Health check completed: 4/8 services healthy {"service":"api-gateway","timestamp":"2025-09-21T10:49:17.726Z"}
warn: Health check failed for analytics-service: fetch failed {"service":"api-gateway","timestamp":"2025-09-21T10:49:47.721Z"}
warn: Health check failed for notification-service: fetch failed {"service":"api-gateway","timestamp":"2025-09-21T10:49:47.722Z"}
warn: Health check failed for integration-service: fetch failed {"service":"api-gateway","timestamp":"2025-09-21T10:49:47.722Z"}
warn: Health check failed for payment-service: fetch failed {"service":"api-gateway","timestamp":"2025-09-21T10:49:47.722Z"}
debug: Health check completed: 4/8 services healthy {"service":"api-gateway","timestamp":"2025-09-21T10:49:47.722Z"}
warn: Health check failed for analytics-service: fetch failed {"service":"api-gateway","timestamp":"2025-09-21T10:50:17.724Z"}
warn: Health check failed for notification-service: fetch failed {"service":"api-gateway","timestamp":"2025-09-21T10:50:17.726Z"}
warn: Health check failed for integration-service: fetch failed {"service":"api-gateway","timestamp":"2025-09-21T10:50:17.726Z"}
warn: Health check failed for payment-service: fetch failed {"service":"api-gateway","timestamp":"2025-09-21T10:50:17.727Z"}
debug: Health check completed: 4/8 services healthy {"service":"api-gateway","timestamp":"2025-09-21T10:50:17.727Z"}
warn: Health check failed for analytics-service: fetch failed {"service":"api-gateway","timestamp":"2025-09-21T10:50:47.725Z"}
warn: Health check failed for notification-service: fetch failed {"service":"api-gateway","timestamp":"2025-09-21T10:50:47.726Z"}
warn: Health check failed for integration-service: fetch failed {"service":"api-gateway","timestamp":"2025-09-21T10:50:47.726Z"}
warn: Health check failed for payment-service: fetch failed {"service":"api-gateway","timestamp":"2025-09-21T10:50:47.727Z"}
debug: Health check completed: 4/8 services healthy {"service":"api-gateway","timestamp":"2025-09-21T10:50:47.727Z"}
info: Shutting down API Gateway gracefully... {"service":"api-gateway","timestamp":"2025-09-21T10:51:15.989Z"}
info: HTTP server closed {"service":"api-gateway","timestamp":"2025-09-21T10:51:15.990Z"}
warn: MongoDB disconnected {"service":"api-gateway","timestamp":"2025-09-21T10:51:16.004Z"}
info: MongoDB disconnected successfully {"service":"api-gateway","timestamp":"2025-09-21T10:51:16.004Z"}
info: Database disconnected {"service":"api-gateway","timestamp":"2025-09-21T10:51:16.004Z"}
