#!/bin/bash

# DigitalOcean Setup Script for Resume Automator
# Run this script ONCE on your DigitalOcean droplet to prepare it for auto-deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${YELLOW}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_status "🚀 Setting up DigitalOcean droplet for Resume Automator auto-deployment..."

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   print_error "This script should not be run as root for security reasons"
   exit 1
fi

# Update system packages
print_status "Updating system packages..."
sudo apt update && sudo apt upgrade -y

# Install Node.js 18
print_status "Installing Node.js 18..."
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2 globally
print_status "Installing PM2..."
sudo npm install -g pm2

# Install Git if not already installed
print_status "Installing Git..."
sudo apt install -y git

# Create application directory
APP_DIR="/home/<USER>/resume-automator"
print_status "Creating application directory: $APP_DIR"
mkdir -p "$APP_DIR"
cd "$APP_DIR"

# Clone the repository (you'll need to update this with your actual repo URL)
print_status "Cloning repository..."
git clone https://github.com/PeretzNiro/resume-automator.git .

# Install dependencies
print_status "Installing dependencies..."
npm install

# Create production environment file
print_status "Creating production environment file..."
cat > .env << 'EOF'
# Production Environment Variables for DigitalOcean

# API Gateway Configuration
NODE_ENV=production
PORT=3000

# Service URLs
AUTH_SERVICE_URL=http://localhost:3001
USER_SERVICE_URL=http://localhost:3002
JOB_SERVICE_URL=http://localhost:3003
RESUME_SERVICE_URL=http://localhost:3004
ANALYTICS_SERVICE_URL=http://localhost:3005
NOTIFICATION_SERVICE_URL=http://localhost:3006
INTEGRATION_SERVICE_URL=http://localhost:3007
PAYMENT_SERVICE_URL=http://localhost:3008

# Database Configuration
MONGODB_URI=mongodb+srv://ResumeAdmin:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0ResumePlatform
REDIS_URL=redis://localhost:6379

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production-must-be-32-chars-minimum
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-in-production-must-be-32-chars

# CORS Configuration
CORS_ORIGIN=https://stingray-app-7geup.ondigitalocean.app

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
EOF

# Create PM2 ecosystem file
print_status "Creating PM2 ecosystem configuration..."
cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [
    {
      name: 'api-gateway',
      script: 'services/api-gateway/dist/index.js',
      cwd: '/home/<USER>/resume-automator',
      env: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      instances: 1,
      exec_mode: 'fork',
      watch: false,
      max_memory_restart: '1G',
      error_file: './logs/api-gateway-error.log',
      out_file: './logs/api-gateway-out.log',
      log_file: './logs/api-gateway-combined.log',
      time: true
    },
    {
      name: 'auth-service',
      script: 'services/auth-service/dist/index.js',
      cwd: '/home/<USER>/resume-automator',
      env: {
        NODE_ENV: 'production',
        PORT: 3001
      },
      instances: 1,
      exec_mode: 'fork',
      watch: false,
      max_memory_restart: '1G',
      error_file: './logs/auth-service-error.log',
      out_file: './logs/auth-service-out.log',
      log_file: './logs/auth-service-combined.log',
      time: true
    },
    {
      name: 'user-service',
      script: 'services/user-service/dist/index.js',
      cwd: '/home/<USER>/resume-automator',
      env: {
        NODE_ENV: 'production',
        PORT: 3002
      },
      instances: 1,
      exec_mode: 'fork',
      watch: false,
      max_memory_restart: '1G',
      error_file: './logs/user-service-error.log',
      out_file: './logs/user-service-out.log',
      log_file: './logs/user-service-combined.log',
      time: true
    },
    {
      name: 'job-service',
      script: 'services/job-service/dist/index.js',
      cwd: '/home/<USER>/resume-automator',
      env: {
        NODE_ENV: 'production',
        PORT: 3003
      },
      instances: 1,
      exec_mode: 'fork',
      watch: false,
      max_memory_restart: '1G',
      error_file: './logs/job-service-error.log',
      out_file: './logs/job-service-out.log',
      log_file: './logs/job-service-combined.log',
      time: true
    },
    {
      name: 'resume-service',
      script: 'services/resume-service/dist/index.js',
      cwd: '/home/<USER>/resume-automator',
      env: {
        NODE_ENV: 'production',
        PORT: 3004
      },
      instances: 1,
      exec_mode: 'fork',
      watch: false,
      max_memory_restart: '1G',
      error_file: './logs/resume-service-error.log',
      out_file: './logs/resume-service-out.log',
      log_file: './logs/resume-service-combined.log',
      time: true
    }
  ]
};
EOF

# Create logs directory
mkdir -p logs

# Build the application
print_status "Building the application..."
npm run build

# Start services with PM2
print_status "Starting services with PM2..."
pm2 start ecosystem.config.js

# Save PM2 configuration
pm2 save

# Setup PM2 to start on boot
pm2 startup

print_success "Setup completed successfully!"
print_status "Services are now running. Check status with: pm2 status"
print_status "View logs with: pm2 logs"
print_status "Restart services with: pm2 restart all"
print_status "Stop services with: pm2 stop all"

echo ""
print_status "Next steps:"
echo "1. Update the JWT_SECRET and JWT_REFRESH_SECRET in .env with secure values"
echo "2. Update CORS_ORIGIN to match your frontend URL"
echo "3. Configure your reverse proxy (nginx) to route traffic to the API Gateway on port 3000"
echo "4. Set up SSL certificates for HTTPS"
echo "5. Configure firewall rules to only allow necessary ports"
echo "6. Add the following secrets to your GitHub repository:"
echo "   - DIGITALOCEAN_HOST: Your droplet's IP address"
echo "   - DIGITALOCEAN_USERNAME: Your droplet's username (usually 'ubuntu')"
echo "   - DIGITALOCEAN_SSH_KEY: Your private SSH key for accessing the droplet"
