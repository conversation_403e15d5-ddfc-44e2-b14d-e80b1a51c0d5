# Frontend Service Deployment Guide

## 🏗️ Frontend Service Overview

The frontend service is a modern React application built with TypeScript, Vite, and Tailwind CSS. It provides a complete user interface for the Job Application Automator platform.

### 📁 Service Location
```
/services/frontend-service/
```

### 🚀 Tech Stack
- **Frontend**: React 18 + TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS
- **State Management**: React Query + Context API
- **Routing**: React Router v6
- **HTTP Client**: Axios with automatic token refresh

## 🏃‍♂️ Quick Start

### Development Mode

1. **Navigate to the frontend service**:
   ```bash
   cd services/frontend-service
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Create environment file**:
   ```bash
   cp .env.example .env
   ```

4. **Start development server**:
   ```bash
   npm run dev
   # or use the convenience script
   ./scripts/dev.sh
   ```

5. **Access the application**:
   - Frontend: http://localhost:3010
   - API Gateway (backend): http://localhost:3000

### Production Build

1. **Build for production**:
   ```bash
   npm run build
   # or use the build script
   ./scripts/build.sh
   ```

2. **Preview production build**:
   ```bash
   npm run preview
   ```

## 🔧 Configuration

### Environment Variables

Create a `.env` file in the frontend service directory:

```env
# API Configuration
VITE_API_BASE_URL=http://localhost:3000

# App Configuration
VITE_APP_NAME=Job Application Automator
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=Streamline your job search with AI-powered tools

# Environment
VITE_NODE_ENV=development

# Optional: Google OAuth
VITE_GOOGLE_CLIENT_ID=your_google_client_id_here
```

### API Integration

The frontend automatically connects to your backend API gateway at `http://localhost:3000`. All API calls are proxied through Vite's development server for CORS handling.

## 🐳 Docker Deployment

### Build Docker Image

```bash
cd services/frontend-service
docker build -t job-automator-frontend .
```

### Run Container

```bash
docker run -p 80:80 job-automator-frontend
```

### Docker Compose Integration

Add to your main `docker-compose.yml`:

```yaml
services:
  frontend:
    build: ./services/frontend-service
    ports:
      - "80:80"
    depends_on:
      - api-gateway
    environment:
      - VITE_API_BASE_URL=http://localhost:3000
    networks:
      - job-platform-network
```

## 🏗️ Production Deployment

### Option 1: Nginx + Static Files

1. **Build the application**:
   ```bash
   npm run build
   ```

2. **Copy built files to web server**:
   ```bash
   cp -r dist/* /var/www/html/
   ```

3. **Configure Nginx** (see `nginx.conf` for reference):
   ```nginx
   server {
       listen 80;
       server_name your-domain.com;
       root /var/www/html;
       index index.html;

       # API proxy
       location /api/ {
           proxy_pass http://your-backend:3000;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
       }

       # Client-side routing
       location / {
           try_files $uri $uri/ /index.html;
       }
   }
   ```

### Option 2: CDN Deployment

1. **Build and upload to CDN**:
   ```bash
   npm run build
   aws s3 sync dist/ s3://your-bucket --delete
   ```

2. **Configure CloudFront** for SPA routing and API proxying

### Option 3: Vercel/Netlify

1. **Connect your repository**
2. **Set build command**: `npm run build`
3. **Set publish directory**: `dist`
4. **Configure redirects** for SPA routing

## 🔐 Security Considerations

### Content Security Policy

The application includes CSP headers in production:

```
Content-Security-Policy: default-src 'self' http: https: data: blob: 'unsafe-inline'
```

### API Security

- JWT tokens with automatic refresh
- Secure token storage
- CORS protection
- Request/response interceptors for error handling

## 📱 Features Overview

### Authentication
- Login/Register forms with validation
- Google OAuth integration
- Password reset functionality
- Automatic token refresh
- Protected routes

### Dashboard
- Application statistics
- Recent applications overview
- Quick action cards
- Responsive design

### Job Management
- Job search and filtering
- Job details view
- Save/unsave jobs
- Application tracking

### Resume Management
- Resume upload and processing
- ATS compatibility analysis
- Resume optimization suggestions
- Multiple resume versions

### Profile Management
- User profile editing
- Skills and experience management
- Education history
- Contact information

### Settings
- Account settings
- Notification preferences
- Privacy controls
- Data export/deletion

## 🎨 UI/UX Features

### Design System
- Consistent color palette
- Responsive typography
- Accessible components
- Loading states
- Error handling

### User Experience
- Mobile-first design
- Intuitive navigation
- Real-time notifications
- Progressive loading
- Offline support (planned)

## 🧪 Testing

```bash
# Run unit tests
npm run test

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch
```

## 📊 Performance

### Optimization Features
- Code splitting with React.lazy()
- Tree shaking
- Bundle size optimization
- Image optimization
- Caching strategies

### Monitoring
- Performance metrics
- Error tracking
- User analytics (optional)
- Core Web Vitals monitoring

## 🔍 Troubleshooting

### Common Issues

1. **API Connection Issues**:
   - Ensure backend services are running
   - Check CORS configuration
   - Verify API_BASE_URL in .env

2. **Build Errors**:
   - Clear node_modules and reinstall
   - Check TypeScript errors
   - Verify all dependencies

3. **Authentication Issues**:
   - Check token expiration
   - Verify JWT configuration
   - Clear browser storage

### Debug Mode

Enable debug logging:
```env
VITE_DEBUG=true
```

## 📝 Development Workflow

### Code Quality
```bash
# Linting
npm run lint
npm run lint:fix

# Type checking
npm run type-check

# Formatting
npm run format
```

### Git Hooks (Recommended)
```bash
# Install husky for pre-commit hooks
npm install --save-dev husky
npx husky install
npx husky add .husky/pre-commit "npm run lint && npm run type-check"
```

## 🚀 Deployment Checklist

- [ ] Environment variables configured
- [ ] Backend services running
- [ ] Build passes without errors
- [ ] Tests passing
- [ ] Security headers configured
- [ ] CDN/caching configured
- [ ] Monitoring setup
- [ ] SSL certificate installed
- [ ] Domain configured
- [ ] Error tracking enabled

## 🆘 Support

For frontend-specific issues:
1. Check browser console for errors
2. Verify network requests in DevTools
3. Check backend service logs
4. Review environment configuration
5. Test with different browsers

## 📚 Additional Resources

- [React Documentation](https://reactjs.org/docs)
- [TypeScript Handbook](https://www.typescriptlang.org/docs)
- [Vite Guide](https://vitejs.dev/guide)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [React Router Documentation](https://reactrouter.com/docs)

---

The frontend service is now ready to provide a complete user interface for your Job Application Automator platform! 🎉
