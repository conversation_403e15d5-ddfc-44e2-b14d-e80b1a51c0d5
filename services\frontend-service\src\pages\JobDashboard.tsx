import React, { useState, useEffect } from 'react';
import { useRootAdmin } from '@/hooks/useRootAdmin';
import { Navigate } from 'react-router-dom';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '../components/ui/card';
import { Button } from '../components/ui/Button';
import { Input } from '../components/ui/Input';
import { Label } from '../components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../components/ui/select';
import { Badge } from '../components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { Alert, AlertDescription } from '../components/ui/alert';
import {
  Activity,
  Database,
  RefreshCw,
  TrendingUp,
  Briefcase,
  MapPin,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Settings,
  Trash2
} from 'lucide-react';

interface DashboardOverview {
  system: {
    status: 'healthy' | 'degraded' | 'unhealthy';
    uptime: number;
    memory: {
      rss: string;
      heapTotal: string;
      heapUsed: string;
      external: string;
    };
    database: {
      connected: boolean;
      responseTime: number;
      jobCount: number;
    };
  };
  sync: {
    totalJobs: number;
    externalJobs: number;
    internalJobs: number;
    activeJobs: number;
    lastSync: string | null;
  };
  metrics: {
    totalSyncs: number;
    successfulSyncs: number;
    failedSyncs: number;
    successRate: string;
    averageSyncTime: number;
  };
  recentActivity: Array<{
    _id: string;
    title: string;
    company: string;
    source: string;
    createdAt: string;
    status: string;
  }>;
  apiKeys: {
    adzuna: boolean;
    indeed: boolean;
    linkedin: boolean;
    glassdoor: boolean;
  };
}

interface Job {
  _id: string;
  title: string;
  company: string;
  location: string;
  type: string;
  source: string;
  status: string;
  createdAt: string;
  viewCount: number;
  applicationCount: number;
}

const JobDashboard: React.FC = () => {
  const { isRootAdmin } = useRootAdmin();
  const [overview, setOverview] = useState<DashboardOverview | null>(null);
  const [jobs, setJobs] = useState<Job[]>([]);
  const [loading, setLoading] = useState(true);
  const [syncing, setSyncing] = useState(false);
  const [filters, setFilters] = useState({
    page: 1,
    limit: 20,
    status: 'active',
    source: '',
    type: '',
    location: '',
    company: '',
    sortBy: 'createdAt',
    sortOrder: 'desc'
  });

  // Redirect if not ROOT Admin
  if (!isRootAdmin) {
    return <Navigate to="/unauthorized" replace />;
  }

  // Fetch dashboard overview
  const fetchOverview = async () => {
    try {
      const response = await fetch('/api/v1/dashboard/overview');
      const data = await response.json();
      if (data.success) {
        setOverview(data.data);
      }
    } catch (error) {
      console.error('Failed to fetch overview:', error);
    }
  };

  // Fetch jobs with filters
  const fetchJobs = async () => {
    try {
      const params = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value) params.append(key, value.toString());
      });

      const response = await fetch(`/api/v1/dashboard/jobs?${params}`);
      const data = await response.json();
      if (data.success) {
        setJobs(data.data.jobs);
      }
    } catch (error) {
      console.error('Failed to fetch jobs:', error);
    }
  };

  // Start job sync
  const startSync = async (query: string, location: string, maxJobs: number, categories: boolean = false) => {
    setSyncing(true);
    try {
      const response = await fetch('/api/v1/dashboard/sync/start', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ query, location, maxJobs, categories })
      });
      
      const data = await response.json();
      if (data.success) {
        // Refresh data after sync
        await Promise.all([fetchOverview(), fetchJobs()]);
      }
    } catch (error) {
      console.error('Sync failed:', error);
    } finally {
      setSyncing(false);
    }
  };

  // System cleanup
  const cleanupSystem = async (daysOld: number = 30) => {
    try {
      const response = await fetch('/api/v1/dashboard/system/cleanup', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ daysOld })
      });
      
      const data = await response.json();
      if (data.success) {
        await fetchOverview();
      }
    } catch (error) {
      console.error('Cleanup failed:', error);
    }
  };

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await Promise.all([fetchOverview(), fetchJobs()]);
      setLoading(false);
    };
    
    loadData();
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(loadData, 30000);
    return () => clearInterval(interval);
  }, [filters]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'bg-green-500';
      case 'degraded': return 'bg-yellow-500';
      case 'unhealthy': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getSourceColor = (source: string) => {
    switch (source) {
      case 'adzuna': return 'bg-blue-500';
      case 'indeed': return 'bg-green-500';
      case 'linkedin': return 'bg-blue-600';
      case 'glassdoor': return 'bg-purple-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <h1 className="text-2xl sm:text-3xl font-bold">Job Service Dashboard</h1>
        <div className="flex flex-col sm:flex-row gap-2">
          <Button
            onClick={() => startSync('developer', 'us', 50)}
            disabled={syncing}
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${syncing ? 'animate-spin' : ''}`} />
            {syncing ? 'Syncing...' : 'Sync Jobs'}
          </Button>
          <Button
            onClick={() => cleanupSystem(30)}
            variant="outline"
            className="flex items-center gap-2"
          >
            <Trash2 className="h-4 w-4" />
            Cleanup
          </Button>
        </div>
      </div>

      {/* System Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">System Status</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <div className={`w-3 h-3 rounded-full ${getStatusColor(overview?.system.status || '')}`} />
              <span className="text-2xl font-bold capitalize">{overview?.system.status}</span>
            </div>
            <p className="text-xs text-muted-foreground">
              Uptime: {Math.floor((overview?.system.uptime || 0) / 3600)}h
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Jobs</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overview?.sync.totalJobs || 0}</div>
            <p className="text-xs text-muted-foreground">
              {overview?.sync.activeJobs || 0} active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">External Jobs</CardTitle>
            <Briefcase className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overview?.sync.externalJobs || 0}</div>
            <p className="text-xs text-muted-foreground">
              {overview?.sync.internalJobs || 0} internal
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Sync Success Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overview?.metrics.successRate || '0%'}</div>
            <p className="text-xs text-muted-foreground">
              {overview?.metrics.totalSyncs || 0} total syncs
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Database Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Database Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center space-x-2">
              {overview?.system.database.connected ? (
                <CheckCircle className="h-4 w-4 text-green-500" />
              ) : (
                <XCircle className="h-4 w-4 text-red-500" />
              )}
              <span className="text-sm">
                {overview?.system.database.connected ? 'Connected' : 'Disconnected'}
              </span>
            </div>
            <div className="text-sm">
              Response Time: {overview?.system.database.responseTime || 0}ms
            </div>
            <div className="text-sm">
              Job Count: {overview?.system.database.jobCount || 0}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* API Key Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            API Key Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <span className="text-sm font-medium">Adzuna</span>
              {overview?.apiKeys.adzuna ? (
                <CheckCircle className="h-4 w-4 text-green-500" />
              ) : (
                <XCircle className="h-4 w-4 text-red-500" />
              )}
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <span className="text-sm font-medium">Indeed</span>
              {overview?.apiKeys.indeed ? (
                <CheckCircle className="h-4 w-4 text-green-500" />
              ) : (
                <XCircle className="h-4 w-4 text-red-500" />
              )}
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <span className="text-sm font-medium">LinkedIn</span>
              {overview?.apiKeys.linkedin ? (
                <CheckCircle className="h-4 w-4 text-green-500" />
              ) : (
                <XCircle className="h-4 w-4 text-red-500" />
              )}
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <span className="text-sm font-medium">Glassdoor</span>
              {overview?.apiKeys.glassdoor ? (
                <CheckCircle className="h-4 w-4 text-green-500" />
              ) : (
                <XCircle className="h-4 w-4 text-red-500" />
              )}
            </div>
          </div>
          {overview?.apiKeys && Object.values(overview.apiKeys).every(status => !status) && (
            <Alert className="mt-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                No API keys configured. Job sync will not work until API keys are added to environment variables.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Memory Usage */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Memory Usage
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div>
              <div className="text-sm font-medium">RSS</div>
              <div className="text-lg">{overview?.system.memory.rss}</div>
            </div>
            <div>
              <div className="text-sm font-medium">Heap Total</div>
              <div className="text-lg">{overview?.system.memory.heapTotal}</div>
            </div>
            <div>
              <div className="text-sm font-medium">Heap Used</div>
              <div className="text-lg">{overview?.system.memory.heapUsed}</div>
            </div>
            <div>
              <div className="text-sm font-medium">External</div>
              <div className="text-lg">{overview?.system.memory.external}</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Jobs Management */}
      <Tabs defaultValue="jobs" className="space-y-4">
        <TabsList>
          <TabsTrigger value="jobs">Jobs</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="recent">Recent Activity</TabsTrigger>
        </TabsList>

        <TabsContent value="jobs" className="space-y-4">
          {/* Filters */}
          <Card>
            <CardHeader>
              <CardTitle>Job Filters</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
                <div>
                  <Label htmlFor="status">Status</Label>
                  <Select
                    id="status"
                    name="status"
                    value={filters.status}
                    onValueChange={(value: string) => setFilters({ ...filters, status: value })}
                    aria-label="Filter jobs by status"
                  >
                    <SelectTrigger title="Select job status filter">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="paused">Paused</SelectItem>
                      <SelectItem value="closed">Closed</SelectItem>
                      <SelectItem value="draft">Draft</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="source">Source</Label>
                  <Select
                    id="source"
                    name="source"
                    value={filters.source}
                    onValueChange={(value: string) => setFilters({ ...filters, source: value })}
                    aria-label="Filter jobs by source"
                  >
                    <SelectTrigger title="Select job source filter">
                      <SelectValue placeholder="All sources" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">All sources</SelectItem>
                      <SelectItem value="adzuna">Adzuna</SelectItem>
                      <SelectItem value="indeed">Indeed</SelectItem>
                      <SelectItem value="linkedin">LinkedIn</SelectItem>
                      <SelectItem value="glassdoor">Glassdoor</SelectItem>
                      <SelectItem value="internal">Internal</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="type">Type</Label>
                  <Select
                    id="type"
                    name="type"
                    value={filters.type}
                    onValueChange={(value: string) => setFilters({ ...filters, type: value })}
                    aria-label="Filter jobs by type"
                  >
                    <SelectTrigger title="Select job type filter">
                      <SelectValue placeholder="All types" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">All types</SelectItem>
                      <SelectItem value="full_time">Full Time</SelectItem>
                      <SelectItem value="part_time">Part Time</SelectItem>
                      <SelectItem value="contract">Contract</SelectItem>
                      <SelectItem value="internship">Internship</SelectItem>
                      <SelectItem value="freelance">Freelance</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="location">Location</Label>
                  <Input
                    id="location"
                    name="location"
                    placeholder="Filter by location"
                    value={filters.location}
                    onChange={(e) => setFilters({ ...filters, location: e.target.value })}
                    title="Filter jobs by location"
                    aria-label="Filter jobs by location"
                  />
                </div>

                <div>
                  <Label htmlFor="company">Company</Label>
                  <Input
                    id="company"
                    name="company"
                    placeholder="Filter by company"
                    value={filters.company}
                    onChange={(e) => setFilters({ ...filters, company: e.target.value })}
                    title="Filter jobs by company name"
                    aria-label="Filter jobs by company name"
                  />
                </div>

                <div>
                  <Label htmlFor="sort">Sort By</Label>
                  <Select
                    id="sort"
                    name="sortBy"
                    value={filters.sortBy}
                    onValueChange={(value: string) => setFilters({ ...filters, sortBy: value })}
                    aria-label="Sort jobs by"
                  >
                    <SelectTrigger title="Select how to sort the jobs">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="createdAt">Created Date</SelectItem>
                      <SelectItem value="title">Title</SelectItem>
                      <SelectItem value="company">Company</SelectItem>
                      <SelectItem value="viewCount">Views</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Jobs List */}
          <div className="space-y-4">
            {jobs.map((job) => (
              <Card key={job._id}>
                <CardContent className="p-4">
                  <div className="flex items-start justify-between">
                    <div className="space-y-2">
                      <h3 className="font-semibold">{job.title}</h3>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <Briefcase className="h-4 w-4" />
                        {job.company}
                        <MapPin className="h-4 w-4 ml-2" />
                        {job.location}
                        <Clock className="h-4 w-4 ml-2" />
                        {new Date(job.createdAt).toLocaleDateString()}
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className={getSourceColor(job.source)}>
                          {job.source}
                        </Badge>
                        <Badge variant="outline">{job.type}</Badge>
                        <Badge variant={job.status === 'active' ? 'default' : 'secondary'}>
                          {job.status}
                        </Badge>
                      </div>
                    </div>
                    <div className="text-right text-sm text-muted-foreground">
                      <div>{job.viewCount} views</div>
                      <div>{job.applicationCount} applications</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="recent" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {overview?.recentActivity.map((activity) => (
                  <div key={activity._id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <div className="font-medium">{activity.title}</div>
                      <div className="text-sm text-muted-foreground">{activity.company}</div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className={getSourceColor(activity.source)}>
                        {activity.source}
                      </Badge>
                      <span className="text-sm text-muted-foreground">
                        {new Date(activity.createdAt).toLocaleString()}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default JobDashboard;
