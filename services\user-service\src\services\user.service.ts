import { User } from '../models/user.model';
import { Types } from 'mongoose';
import { logger } from '../utils/logger';
import { imageService } from './image.service';

export class UserService {
  /**
   * Find user by ID
   */
  async findById(userId: string): Promise<any> {
    try {
      const user = await User.findById(userId).select('-analytics');
      return user;
    } catch (error) {
      logger.error('Error finding user by ID:', error);
      throw error;
    }
  }

  /**
   * Find user by email
   */
  async findByEmail(email: string): Promise<any> {
    try {
      const user = await User.findOne({ email: email.toLowerCase() }).select('-analytics');
      return user;
    } catch (error) {
      logger.error('Error finding user by email:', error);
      throw error;
    }
  }

  /**
   * Find user by Google ID
   */
  async findByGoogleId(googleId: string): Promise<any> {
    try {
      const user = await User.findOne({ googleId }).select('-analytics');
      return user;
    } catch (error) {
      logger.error('Error finding user by Google ID:', error);
      throw error;
    }
  }

  /**
   * Create new user
   */
  async create(userData: any): Promise<any> {
    try {
      const user = new User(userData);
      await user.save();
      return user;
    } catch (error) {
      logger.error('Error creating user:', error);
      throw error;
    }
  }

  /**
   * Update user by ID
   */
  async updateById(userId: string, updateData: any): Promise<any> {
    try {
      // Handle nested updates properly for MongoDB
      const updateQuery: any = { updatedAt: new Date() };
      
      // Process each field and handle nested objects
      for (const [key, value] of Object.entries(updateData)) {
        if (value !== undefined) {
          if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
            // Handle nested objects like profile, preferences
            for (const [nestedKey, nestedValue] of Object.entries(value)) {
              if (nestedValue !== undefined) {
                updateQuery[`${key}.${nestedKey}`] = nestedValue;
              }
            }
          } else {
            // Handle simple fields
            updateQuery[key] = value;
          }
        }
      }

      const user = await User.findByIdAndUpdate(
        userId,
        updateQuery,
        { new: true, runValidators: true }
      ).select('-analytics');
      
      return user;
    } catch (error) {
      logger.error('Error updating user:', error);
      throw error;
    }
  }

  /**
   * Link Google account to existing user
   */
  async linkGoogleAccount(userId: string, googleId: string): Promise<any> {
    try {
      const user = await User.findByIdAndUpdate(
        userId,
        { 
          googleId,
          updatedAt: new Date()
        },
        { new: true, runValidators: true }
      ).select('-analytics');
      
      logger.info(`Google account linked for user ${userId}`);
      return user;
    } catch (error) {
      logger.error('Error linking Google account:', error);
      throw error;
    }
  }

  /**
   * Unlink Google account from user
   */
  async unlinkGoogleAccount(userId: string): Promise<any> {
    try {
      const user = await User.findByIdAndUpdate(
        userId,
        { 
          $unset: { googleId: 1 },
          updatedAt: new Date()
        },
        { new: true, runValidators: true }
      ).select('-analytics');
      
      logger.info(`Google account unlinked for user ${userId}`);
      return user;
    } catch (error) {
      logger.error('Error unlinking Google account:', error);
      throw error;
    }
  }

  /**
   * Update user profile section
   */
  async updateProfileSection(userId: string, section: string, data: any): Promise<any> {
    try {
      const updateQuery: any = { updatedAt: new Date() };
      
      // Map section data to database fields
      if (section === 'basic') {
        if (data.bio !== undefined) updateQuery['profile.bio'] = data.bio;
        if (data.phoneNumber !== undefined) updateQuery['profile.phoneNumber'] = data.phoneNumber;
        if (data.location !== undefined) updateQuery['profile.location'] = data.location;
        if (data.dateOfBirth !== undefined) updateQuery['profile.dateOfBirth'] = data.dateOfBirth;
        if (data.nationality !== undefined) updateQuery['profile.nationality'] = data.nationality;
      } else if (section === 'professional') {
        if (data.currentPosition !== undefined) updateQuery['profile.currentPosition'] = data.currentPosition;
        if (data.currentCompany !== undefined) updateQuery['profile.currentCompany'] = data.currentCompany;
        if (data.yearsOfExperience !== undefined) updateQuery['profile.yearsOfExperience'] = data.yearsOfExperience;
        if (data.expectedSalary !== undefined) updateQuery['profile.expectedSalary'] = data.expectedSalary;
        if (data.experience !== undefined) updateQuery['profile.experience'] = data.experience;
      } else if (section === 'education') {
        if (data.education !== undefined) updateQuery['profile.education'] = data.education;
      } else if (section === 'skills') {
        if (data.skills !== undefined) updateQuery['profile.skills'] = data.skills;
      } else if (section === 'contact') {
        if (data.website !== undefined) updateQuery['profile.website'] = data.website;
        if (data.linkedin !== undefined) updateQuery['profile.linkedin'] = data.linkedin;
        if (data.github !== undefined) updateQuery['profile.github'] = data.github;
        if (data.portfolio !== undefined) updateQuery['profile.portfolio'] = data.portfolio;
      } else if (section === 'preferences') {
        if (data.notifications !== undefined) updateQuery['preferences.notifications'] = data.notifications;
        if (data.privacy !== undefined) updateQuery['preferences.privacy'] = data.privacy;
        if (data.jobAlerts !== undefined) updateQuery['preferences.jobAlerts'] = data.jobAlerts;
      }

      const user = await User.findByIdAndUpdate(
        userId,
        updateQuery,
        { new: true, runValidators: true }
      ).select('-analytics');
      
      return user;
    } catch (error) {
      logger.error('Error updating profile section:', error);
      throw error;
    }
  }

  /**
   * Update user avatar
   */
  async updateAvatar(userId: string, avatarUrl: string): Promise<any> {
    try {
      const user = await User.findByIdAndUpdate(
        userId,
        { 
          avatar: avatarUrl,
          updatedAt: new Date()
        },
        { new: true, runValidators: true }
      ).select('-analytics');
      
      return user;
    } catch (error) {
      logger.error('Error updating avatar:', error);
      throw error;
    }
  }

  /**
   * Upload and update user avatar with Cloudinary
   */
  async uploadAvatar(userId: string, file: Buffer, username: string): Promise<any> {
    try {
      // Get current user to check for existing avatar
      const currentUser = await this.findById(userId);
      if (!currentUser) {
        throw new Error('User not found');
      }

      // Delete old avatar if exists
      if (currentUser.avatar && currentUser.avatar.includes('cloudinary.com')) {
        const oldPublicId = this.extractPublicIdFromUrl(currentUser.avatar);
        if (oldPublicId) {
          try {
            await imageService.deleteImage(oldPublicId);
            logger.info(`Old avatar deleted for user ${userId}`);
          } catch (deleteError) {
            logger.warn('Failed to delete old avatar:', deleteError);
            // Continue with upload even if deletion fails
          }
        }
      }

      let avatarUrl: string;

      try {
        // Try to upload to Cloudinary first with extended timeout
        const uploadPromise = imageService.uploadAvatar(file, userId, username);
        const timeoutPromise = new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Upload timeout')), 30000) // 30 second timeout
        );
        
        const uploadResult = await Promise.race([uploadPromise, timeoutPromise]) as any;
        avatarUrl = uploadResult.secureUrl;
        
        logger.info(`Avatar uploaded to Cloudinary for user ${userId}`, {
          publicId: uploadResult.publicId,
          url: uploadResult.secureUrl
        });
      } catch (cloudinaryError: any) {
        logger.warn('Cloudinary upload failed, using fallback:', cloudinaryError);
        
        // Fallback: Create a data URL for temporary storage
        const base64String = file.toString('base64');
        const mimeType = this.detectMimeTypeFromBuffer(file);
        avatarUrl = `data:${mimeType};base64,${base64String}`;
        
        logger.info(`Avatar stored as data URL for user ${userId}`);
      }
      
      // Update user with new avatar URL
      const user = await User.findByIdAndUpdate(
        userId,
        { 
          avatar: avatarUrl,
          updatedAt: new Date()
        },
        { new: true, runValidators: true }
      ).select('-analytics');
      
      return user;
    } catch (error) {
      logger.error('Error uploading avatar:', error);
      throw error;
    }
  }

  /**
   * Detect MIME type from buffer
   */
  private detectMimeTypeFromBuffer(buffer: Buffer): string {
    // Check file signatures
    if (buffer[0] === 0xFF && buffer[1] === 0xD8) return 'image/jpeg';
    if (buffer[0] === 0x89 && buffer[1] === 0x50 && buffer[2] === 0x4E && buffer[3] === 0x47) return 'image/png';
    if (buffer[0] === 0x47 && buffer[1] === 0x49 && buffer[2] === 0x46) return 'image/gif';
    if (buffer[0] === 0x52 && buffer[1] === 0x49 && buffer[2] === 0x46 && buffer[3] === 0x46) return 'image/webp';
    
    // Default to JPEG
    return 'image/jpeg';
  }

  /**
   * Delete user avatar
   */
  async deleteAvatar(userId: string): Promise<any> {
    try {
      const user = await this.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Delete from Cloudinary if it's a Cloudinary URL
      if (user.avatar && user.avatar.includes('cloudinary.com')) {
        const publicId = this.extractPublicIdFromUrl(user.avatar);
        if (publicId) {
          await imageService.deleteImage(publicId);
        }
      }

      // Remove avatar from user record
      const updatedUser = await User.findByIdAndUpdate(
        userId,
        { 
          $unset: { avatar: 1 },
          updatedAt: new Date()
        },
        { new: true, runValidators: true }
      ).select('-analytics');

      logger.info(`Avatar deleted for user ${userId}`);
      
      return updatedUser;
    } catch (error) {
      logger.error('Error deleting avatar:', error);
      throw error;
    }
  }

  /**
   * Get optimized avatar URL
   */
  getOptimizedAvatarUrl(avatarUrl: string, size: number = 300): string {
    if (!avatarUrl) {
      return '';
    }

    // If it's already a Cloudinary URL, return optimized version
    if (avatarUrl.includes('cloudinary.com')) {
      const publicId = this.extractPublicIdFromUrl(avatarUrl);
      if (publicId) {
        return imageService.getAvatarUrl(publicId, size);
      }
    }

    // Return original URL if not Cloudinary
    return avatarUrl;
  }

  /**
   * Extract public ID from Cloudinary URL
   */
  private extractPublicIdFromUrl(url: string): string | null {
    try {
      const match = url.match(/\/v\d+\/(.+?)\./);
      return match?.[1] || null;
    } catch (error) {
      logger.error('Error extracting public ID from URL:', error);
      return null;
    }
  }

  /**
   * Update last login
   */
  async updateLastLogin(userId: string): Promise<void> {
    try {
      await User.findByIdAndUpdate(
        userId,
        { 
          lastLoginAt: new Date(),
          'analytics.lastActiveAt': new Date()
        }
      );
    } catch (error) {
      logger.error('Error updating last login:', error);
      throw error;
    }
  }

  /**
   * Verify user email
   */
  async verifyEmail(userId: string): Promise<any> {
    try {
      const user = await User.findByIdAndUpdate(
        userId,
        { 
          isVerified: true,
          updatedAt: new Date()
        },
        { new: true, runValidators: true }
      ).select('-analytics');
      
      return user;
    } catch (error) {
      logger.error('Error verifying email:', error);
      throw error;
    }
  }

  /**
   * Search users
   */
  async searchUsers(query: any): Promise<any> {
    try {
      const { q, skills, location, experience, page = 1, limit = 10 } = query;
      
      const searchQuery: any = { isActive: true };
      
      if (q) {
        searchQuery.$or = [
          { firstName: { $regex: q, $options: 'i' } },
          { lastName: { $regex: q, $options: 'i' } },
          { 'profile.currentPosition': { $regex: q, $options: 'i' } },
          { 'profile.currentCompany': { $regex: q, $options: 'i' } }
        ];
      }
      
      if (skills && skills.length > 0) {
        searchQuery['profile.skills.name'] = { $in: skills };
      }
      
      if (location) {
        searchQuery['profile.location.city'] = { $regex: location, $options: 'i' };
      }
      
      if (experience) {
        searchQuery['profile.yearsOfExperience'] = { $gte: parseInt(experience) };
      }
      
      const users = await User.find(searchQuery)
        .select('-analytics')
        .skip((page - 1) * limit)
        .limit(limit)
        .sort({ updatedAt: -1 });
      
      const total = await User.countDocuments(searchQuery);
      
      return {
        users,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      };
    } catch (error) {
      logger.error('Error searching users:', error);
      throw error;
    }
  }

  /**
   * Get user analytics
   */
  async getUserAnalytics(userId: string): Promise<any> {
    try {
      const user = await User.findById(userId).select('analytics');
      if (!user) {
        throw new Error('User not found');
      }

      return user.analytics || {
        profileViews: 0,
        searchAppearances: 0,
        applicationsSent: 0,
        interviewsScheduled: 0,
        offersReceived: 0,
        loginStreak: 0,
        totalLogins: 0,
        averageSessionDuration: 0,
        responseRate: 0,
        interviewRate: 0,
        offerRate: 0,
      };
    } catch (error) {
      logger.error('Error getting user analytics:', error);
      throw error;
    }
  }
}

export const userService = new UserService();
