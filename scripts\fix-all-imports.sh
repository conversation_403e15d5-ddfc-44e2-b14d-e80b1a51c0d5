#!/bin/bash

# Comprehensive script to fix ALL shared imports

set -e

echo "🔧 Fixing all shared imports..."

# Function to fix imports in a file
fix_imports_in_file() {
    local file=$1
    echo "Fixing: $file"
    
    # Skip dist files
    if [[ $file == *"/dist/"* ]]; then
        return
    fi
    
    # Replace all shared imports with relative paths
    sed -i '' "s|from '@job-platform/shared/config'|from './config/environment'|g" "$file"
    sed -i '' "s|from '@job-platform/shared/database'|from './database/connection'|g" "$file"
    sed -i '' "s|from '@job-platform/shared/utils'|from './utils/logger'|g" "$file"
    sed -i '' "s|from '@job-platform/shared/utils/logger'|from './utils/logger'|g" "$file"
    sed -i '' "s|from '@job-platform/shared/utils/response'|from './utils/response'|g" "$file"
    sed -i '' "s|from '@job-platform/shared/utils/errors'|from './utils/errors'|g" "$file"
    sed -i '' "s|from '@job-platform/shared/types'|from './types/auth.types'|g" "$file"
    sed -i '' "s|from '@job-platform/shared/middleware'|from './middleware'|g" "$file"
    sed -i '' "s|from '@job-platform/shared/middleware/validation.middleware'|from './middleware/validation.middleware'|g" "$file"
    sed -i '' "s|from '@job-platform/shared/middleware/error.middleware'|from './middleware/error.middleware'|g" "$file"
    sed -i '' "s|from '@job-platform/shared/utils/validation'|from './utils/validation'|g" "$file"
    sed -i '' "s|from '@job-platform/shared/database/schemas/base.schema'|from './database/schemas/base.schema'|g" "$file"
    
    # Fix specific imports
    sed -i '' "s|import { logger } from '@job-platform/shared/utils';|import { logger } from '../utils/logger';|g" "$file"
    sed -i '' "s|import { ResponseUtil } from '@job-platform/shared/utils/response';|import { ResponseUtil } from '../utils/response';|g" "$file"
    sed -i '' "s|import { database } from '@job-platform/shared/database';|import { database } from '../database/connection';|g" "$file"
    
    # Fix config imports
    sed -i '' "s|import { appConfig, databaseConfig } from '@job-platform/shared/config';|import { appConfig, databaseConfig } from './config/environment';|g" "$file"
    sed -i '' "s|import { redisConfig } from '@job-platform/shared/config';|import { redisConfig } from './config/environment';|g" "$file"
    
    # Remove require() calls
    sed -i '' "s|require('@job-platform/shared/utils')|require('./utils/encryption')|g" "$file"
}

# Find and fix all TypeScript files with shared imports
find services/ -name "*.ts" -not -path "*/dist/*" | while read -r file; do
    if grep -q "@job-platform/shared" "$file"; then
        fix_imports_in_file "$file"
    fi
done

echo "✅ All imports fixed!"