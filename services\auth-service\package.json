{"name": "@job-platform/auth-service", "version": "1.0.0", "description": "Authentication service for job application platform", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "prestart": "npm install --production", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "setup": "npm install"}, "dependencies": {"axios": "^1.12.2", "bcryptjs": "^3.0.2", "compression": "^1.8.1", "cors": "^2.8.5", "dotenv": "^17.2.2", "express": "^5.1.0", "express-rate-limit": "^8.1.0", "express-validator": "^7.2.1", "handlebars": "^4.7.8", "helmet": "^8.1.0", "ioredis": "^5.7.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.18.1", "morgan": "^1.10.1", "nodemailer": "^7.0.6", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "qrcode": "^1.5.4", "speakeasy": "^2.0.0", "uuid": "^13.0.0", "winston": "^3.17.0", "zod": "^4.1.11"}, "devDependencies": {"@types/bcryptjs": "^3.0.0", "@types/compression": "^1.8.1", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.10", "@types/node": "^24.5.2", "@types/nodemailer": "^7.0.1", "@types/passport": "^1.0.17", "@types/passport-google-oauth20": "^2.0.16", "@types/passport-jwt": "^4.0.1", "@types/qrcode": "^1.5.5", "@types/speakeasy": "^2.0.10", "@types/uuid": "^11.0.0", "jest": "^30.1.3", "ts-jest": "^29.4.4", "tsx": "^4.20.5", "typescript": "^5.9.2"}, "engines": {"node": ">=22.18.0", "npm": ">=10.0.0"}}