import { apiService } from './api';
import { Job, CreateJobRequest, JobSearchParams, PaginatedResponse } from '@/types/api';

class JobService {
  // Job browsing
  async getJobs(params?: JobSearchParams): Promise<PaginatedResponse<Job>> {
    return apiService.get<PaginatedResponse<Job>>('/jobs', { params });
  }

  async getJobById(id: string): Promise<Job> {
    return apiService.get<Job>(`/jobs/${id}`);
  }

  async searchJobs(params: JobSearchParams): Promise<PaginatedResponse<Job>> {
    return apiService.get<PaginatedResponse<Job>>('/jobs/search', { params });
  }

  async getFeaturedJobs(): Promise<Job[]> {
    return apiService.get<Job[]>('/jobs/featured');
  }

  async getRecommendedJobs(): Promise<Job[]> {
    return apiService.get<Job[]>('/jobs/recommended');
  }

  async getRecentJobs(limit = 10): Promise<Job[]> {
    return apiService.get<Job[]>('/jobs/recent', { params: { limit } });
  }

  // Job management (Admin/Employer)
  async createJob(jobData: CreateJobRequest): Promise<Job> {
    return apiService.post<Job>('/jobs', jobData);
  }

  async updateJob(id: string, jobData: Partial<CreateJobRequest>): Promise<Job> {
    return apiService.put<Job>(`/jobs/${id}`, jobData);
  }

  async deleteJob(id: string): Promise<void> {
    return apiService.delete(`/jobs/${id}`);
  }

  async publishJob(id: string): Promise<Job> {
    return apiService.post<Job>(`/jobs/${id}/publish`);
  }

  async unpublishJob(id: string): Promise<Job> {
    return apiService.post<Job>(`/jobs/${id}/unpublish`);
  }

  async extendJobExpiry(id: string, newExpiryDate: string): Promise<Job> {
    return apiService.put<Job>(`/jobs/${id}/extend`, { expiresAt: newExpiryDate });
  }

  // Job statistics
  async getJobStats(id: string): Promise<{
    views: number;
    applications: number;
    viewsThisWeek: number;
    applicationsThisWeek: number;
    topSkills: Array<{ skill: string; count: number }>;
    applicationsByDate: Array<{ date: string; count: number }>;
  }> {
    return apiService.get(`/jobs/${id}/stats`);
  }

  // Job categories and filters
  async getJobCategories(): Promise<Array<{ name: string; count: number }>> {
    return apiService.get('/jobs/categories');
  }

  async getJobLocations(): Promise<Array<{ location: string; count: number }>> {
    return apiService.get('/jobs/locations');
  }

  async getJobTypes(): Promise<Array<{ type: string; count: number }>> {
    return apiService.get('/jobs/types');
  }

  async getCompanies(): Promise<Array<{ name: string; logo?: string; jobCount: number }>> {
    return apiService.get('/jobs/companies');
  }

  // Saved jobs
  async saveJob(jobId: string): Promise<void> {
    return apiService.post(`/jobs/${jobId}/save`);
  }

  async unsaveJob(jobId: string): Promise<void> {
    return apiService.delete(`/jobs/${jobId}/save`);
  }

  async getSavedJobs(): Promise<Job[]> {
    return apiService.get('/jobs/saved');
  }

  // Job alerts
  async createJobAlert(alertData: {
    name: string;
    query?: string;
    location?: string;
    type?: string;
    salary?: { min?: number; max?: number };
    frequency: 'daily' | 'weekly' | 'monthly';
  }): Promise<{ id: string }> {
    return apiService.post('/jobs/alerts', alertData);
  }

  async getJobAlerts(): Promise<Array<{
    id: string;
    name: string;
    query?: string;
    location?: string;
    type?: string;
    salary?: { min?: number; max?: number };
    frequency: string;
    isActive: boolean;
    createdAt: string;
    lastSent?: string;
  }>> {
    return apiService.get('/jobs/alerts');
  }

  async updateJobAlert(id: string, alertData: {
    name?: string;
    query?: string;
    location?: string;
    type?: string;
    salary?: { min?: number; max?: number };
    frequency?: 'daily' | 'weekly' | 'monthly';
    isActive?: boolean;
  }): Promise<void> {
    return apiService.put(`/jobs/alerts/${id}`, alertData);
  }

  async deleteJobAlert(id: string): Promise<void> {
    return apiService.delete(`/jobs/alerts/${id}`);
  }

  // Job matching
  async getJobMatches(): Promise<Array<{
    job: Job;
    matchScore: number;
    matchReasons: string[];
  }>> {
    return apiService.get('/jobs/matches');
  }

  async getJobMatchScore(jobId: string): Promise<{
    score: number;
    reasons: string[];
    missingSkills: string[];
    suggestions: string[];
  }> {
    return apiService.get(`/jobs/${jobId}/match-score`);
  }
}

export const jobService = new JobService();
