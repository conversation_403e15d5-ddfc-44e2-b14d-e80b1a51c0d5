import { Request, Response, NextFunction } from 'express';
import { z } from 'zod';
import { ResponseUtil } from '../utils/response';
import { logger } from '../utils/logger';

// Validation schemas
const uploadResumeSchema = z.object({
  body: z.object({
    name: z.string().min(1).max(100).optional(),
    isDefault: z.string().optional()
  })
});

const updateResumeSchema = z.object({
  body: z.object({
    title: z.string().min(1).max(100).optional(),
    isDefault: z.boolean().optional(),
    tags: z.array(z.string()).optional(),
    content: z.object({
      personalInfo: z.object({
        firstName: z.string().optional(),
        lastName: z.string().optional(),
        email: z.string().email().optional(),
        phone: z.string().optional(),
        location: z.string().optional(),
        website: z.string().url().optional(),
        linkedin: z.string().optional(),
        github: z.string().optional()
      }).optional(),
      summary: z.string().optional(),
      experience: z.array(z.any()).optional(),
      education: z.array(z.any()).optional(),
      skills: z.array(z.string()).optional()
    }).optional()
  }),
  params: z.object({
    id: z.string().regex(/^[0-9a-fA-F]{24}$/, 'Invalid MongoDB ObjectId')
  })
});

const analyzeResumeSchema = z.object({
  body: z.object({
    jobDescription: z.object({
      title: z.string().min(1),
      requirements: z.array(z.string()),
      skills: z.array(z.string()),
      experience: z.string(),
      education: z.string()
    }).optional()
  }),
  params: z.object({
    id: z.string().regex(/^[0-9a-fA-F]{24}$/, 'Invalid MongoDB ObjectId')
  })
});

const mongoIdSchema = z.object({
  params: z.object({
    id: z.string().regex(/^[0-9a-fA-F]{24}$/, 'Invalid MongoDB ObjectId')
  })
});

/**
 * Generic validation middleware factory
 */
function validateSchema(schema: z.ZodSchema) {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      schema.parse({
        body: req.body,
        query: req.query,
        params: req.params
      });
      next();
    } catch (error) {
      if (error instanceof z.ZodError) {
        logger.warn('Validation error:', {
          errors: error.issues,
          path: req.path,
          method: req.method
        });
        
        const response = ResponseUtil.error(
          `Validation failed: ${error.issues.map((e: any) => e.message).join(', ')}`,
          400
        );
        res.status(response.statusCode).json(response);
        return;
      } else {
        logger.error('Unexpected validation error:', error);
        const response = ResponseUtil.error('Validation failed', 400);
        res.status(response.statusCode).json(response);
        return;
      }
    }
  };
}

// Export validation middlewares
export const validateUploadResume = validateSchema(uploadResumeSchema);
export const validateUpdateResume = validateSchema(updateResumeSchema);
export const validateAnalyzeResume = validateSchema(analyzeResumeSchema);
export const validateMongoId = validateSchema(mongoIdSchema);

/**
 * Sanitize input data
 */
export const sanitizeInput = (req: Request, res: Response, next: NextFunction) => {
  // Sanitize string inputs
  const sanitizeString = (str: string): string => {
    return str.trim().replace(/[<>]/g, '');
  };

  const sanitizeObject = (obj: any): any => {
    if (typeof obj === 'string') {
      return sanitizeString(obj);
    }
    if (Array.isArray(obj)) {
      return obj.map(sanitizeObject);
    }
    if (obj && typeof obj === 'object') {
      const sanitized: any = {};
      for (const [key, value] of Object.entries(obj)) {
        sanitized[key] = sanitizeObject(value);
      }
      return sanitized;
    }
    return obj;
  };

  if (req.body) {
    req.body = sanitizeObject(req.body);
  }

  next();
};

/**
 * Rate limiting for file uploads
 */
export const rateLimitUploads = (req: Request, res: Response, next: NextFunction) => {
  // Simple rate limiting - in production, use Redis
  const userId = (req as any).user?.id;
  if (!userId) {
    const response = ResponseUtil.error('Authentication required', 401);
    return res.status(response.statusCode).json(response);
  }

  // For now, allow all uploads (implement proper rate limiting in production)
  next();
  return;
};
