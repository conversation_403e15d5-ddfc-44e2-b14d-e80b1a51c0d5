import { logger } from '../utils/logger';
import http from 'http';

interface ServiceConfig {
  name: string;
  url: string;
  healthCheck: string;
  isHealthy: boolean;
  lastHealthCheck: Date | null;
}

class ServiceRegistry {
  private services: Map<string, ServiceConfig> = new Map();
  private healthCheckInterval: ReturnType<typeof setInterval> | null = null;

  constructor() {
    this.registerDefaultServices();
    this.startHealthChecks();
  }

  private registerDefaultServices(): void {
    // Use service names for Docker container communication, localhost for local dev
    const getServiceUrl = (serviceName: string, port: number): string => {
      const envVar = `${serviceName.toUpperCase().replace('-', '_')}_URL`;
      const defaultHost = process.env.NODE_ENV === 'production' ? serviceName : 'localhost';
      return process.env[envVar] ?? `http://${defaultHost}:${port}`;
    };

    const services: Omit<ServiceConfig, 'isHealthy' | 'lastHealthCheck'>[] = [
      {
        name: 'auth-service',
        url: getServiceUrl('auth-service', 3001),
        healthCheck: '/health',
      },
      {
        name: 'user-service',
        url: getServiceUrl('user-service', 3002),
        healthCheck: '/health',
      },
      {
        name: 'job-service',
        url: getServiceUrl('job-service', 3003),
        healthCheck: '/health',
      },
      {
        name: 'resume-service',
        url: getServiceUrl('resume-service', 3004),
        healthCheck: '/health',
      },
      {
        name: 'analytics-service',
        url: getServiceUrl('analytics-service', 3005),
        healthCheck: '/health',
      },
      {
        name: 'notification-service',
        url: getServiceUrl('notification-service', 3006),
        healthCheck: '/health',
      },
      {
        name: 'integration-service',
        url: getServiceUrl('integration-service', 3007),
        healthCheck: '/health',
      },
      {
        name: 'payment-service',
        url: getServiceUrl('payment-service', 3008),
        healthCheck: '/health',
      },
    ];

    services.forEach(service => {
      this.services.set(service.name, {
        ...service,
        isHealthy: false,
        lastHealthCheck: null,
      });
    });

    logger.info(`Registered ${services.length} services`);
  }

  public getServiceUrl(serviceName: string): string {
    const service = this.services.get(serviceName);
    if (!service) {
      throw new Error(`Service ${serviceName} not found`);
    }
    return service.url;
  }

  public getServiceList(): string[] {
    return Array.from(this.services.keys());
  }

  public getHealthyServices(): string[] {
    return Array.from(this.services.entries())
      .filter(([, service]) => service.isHealthy)
      .map(([name]) => name);
  }

  public getServiceHealth(): Record<
    string,
    { isHealthy: boolean; lastCheck: Date | null }
  > {
    const health: Record<
      string,
      { isHealthy: boolean; lastCheck: Date | null }
    > = {};

    this.services.forEach((service, name) => {
      health[name] = {
        isHealthy: service.isHealthy,
        lastCheck: service.lastHealthCheck,
      };
    });

    return health;
  }

  private async checkServiceHealth(serviceName: string): Promise<boolean> {
    const service = this.services.get(serviceName);
    if (!service) return false;

    try {
      const url = new URL(`${service.url}${service.healthCheck}`);
      
      const isHealthy = await new Promise<boolean>((resolve) => {
        const req = http.request({
          hostname: url.hostname,
          port: url.port || (url.protocol === 'https:' ? 443 : 80),
          path: url.pathname + url.search,
          method: 'GET',
          timeout: 5000,
        }, (res) => {
          resolve((res.statusCode ?? 500) >= 200 && (res.statusCode ?? 500) < 300);
        });

        req.on('error', () => {
          resolve(false);
        });

        req.on('timeout', () => {
          req.destroy();
          resolve(false);
        });

        req.end();
      });

      this.services.set(serviceName, {
        ...service,
        isHealthy,
        lastHealthCheck: new Date(),
      });

      return isHealthy;
    } catch (error) {
      logger.warn(
        `Health check failed for ${serviceName}: ${error instanceof Error ? error.message : 'Unknown error'}`
      );

      this.services.set(serviceName, {
        ...service,
        isHealthy: false,
        lastHealthCheck: new Date(),
      });

      return false;
    }
  }

  private startHealthChecks(): void {
    const interval = parseInt(process.env.HEALTH_CHECK_INTERVAL ?? '30000');

    this.healthCheckInterval = setInterval(() => {
      void (async (): Promise<void> => {
        const healthPromises = Array.from(this.services.keys()).map(
          serviceName => this.checkServiceHealth(serviceName)
        );

        await Promise.allSettled(healthPromises);

        const healthyCount = this.getHealthyServices().length;
        const totalCount = this.services.size;

        logger.debug(
          `Health check completed: ${healthyCount}/${totalCount} services healthy`
        );
      })();
    }, interval);

    logger.info(`Health checks started with ${interval}ms interval`);
  }

  public stopHealthChecks(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
      logger.info('Health checks stopped');
    }
  }
}

export const serviceRegistry = new ServiceRegistry();
