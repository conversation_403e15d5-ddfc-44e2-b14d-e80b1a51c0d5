import { Router, RequestHandler } from 'express';
import passport from 'passport';
import { z } from 'zod';
import { validateBody } from '../middleware/validation.middleware';
import {
  UserValidationSchemas,
  CommonValidationSchemas,
} from '../utils/validation';
// import { ResponseUtil } from '../utils/response';
import { AuthController } from '../controllers/auth.controller';

const router = Router();
const authController = new AuthController();

// Registration
router.post(
  '/register',
  validateBody(UserValidationSchemas.register),
  authController.register
);

// Login
router.post(
  '/login',
  validateBody(UserValidationSchemas.login),
  authController.login
);

// Google OAuth
router.get(
  '/google',
  passport.authenticate('google', {
    scope: ['profile', 'email'],
  }) as RequestHandler
);

router.get(
  '/google/callback',
  passport.authenticate('google', { session: false }) as RequestHandler,
  authController.googleCallback
);

// Token refresh
router.post(
  '/refresh',
  validateBody(CommonValidationSchemas.refreshToken),
  authController.refreshToken
);

// Logout
router.post('/logout', authController.logout);

// Email verification
router.post(
  '/verify-email',
  validateBody(
    z.object({
      email: z.email(),
      token: z.string().min(1),
    })
  ),
  authController.verifyEmail
);

// Password reset
router.post(
  '/forgot-password',
  validateBody(UserValidationSchemas.resetPassword),
  authController.forgotPassword
);

router.post(
  '/reset-password',
  validateBody(UserValidationSchemas.confirmResetPassword),
  authController.resetPassword
);

// Change password (authenticated)
router.post(
  '/change-password',
  validateBody(UserValidationSchemas.changePassword),
  authController.changePassword
);

// Resend verification email
router.post(
  '/resend-verification',
  validateBody(
    z.object({
      email: z.email(),
    })
  ),
  authController.resendVerification
);

// Account linking endpoints
router.post(
  '/link-google',
  validateBody(
    z.object({
      googleId: z.string().min(1),
      email: z.string().email(),
    })
  ),
  authController.linkGoogleAccount
);

router.post(
  '/unlink-google',
  authController.unlinkGoogleAccount
);

router.get(
  '/linked-accounts',
  authController.getLinkedAccounts
);

export { router as authRoutes };
