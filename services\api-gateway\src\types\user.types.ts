import { Request } from 'express';

export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: 'admin' | 'user' | 'premium' | 'enterprise';
  subscriptionTier: 'free' | 'basic' | 'premium' | 'enterprise';
  isVerified: boolean;
  isActive: boolean;
  isSuspended: boolean;
  createdAt: Date;
  updatedAt: Date;
  lastLoginAt?: Date;
  avatar?: string;
  googleId?: string;
  analytics?: {
    jobApplications: number;
    resumesCreated: number;
    profileViews: number;
    lastActivityAt: Date;
  } | null;
}

// Create a custom Request interface that extends Express Request
export interface AuthenticatedRequest extends Request {
  user?: User;
}

// Define our own Request interface to avoid conflicts
export interface AppRequest extends Request {
  user?: User;
}

// Note: Global Express namespace extension removed to avoid ESLint namespace warning
// Use AuthenticatedRequest or AppRequest interfaces instead
