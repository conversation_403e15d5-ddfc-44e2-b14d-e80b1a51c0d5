import { Types } from 'mongoose';
import { SecurityEvent } from '../models/security-event.model';
import { logger } from '../utils/logger';

export class SecurityEventService {
  /**
   * Log a security event
   */
  public async logEvent(eventData: {
    userId: Types.ObjectId;
    eventType: string;
    ipAddress: string;
    userAgent: string;
    metadata?: Record<string, unknown>;
  }): Promise<void> {
    try {
      const securityEvent = new SecurityEvent({
        ...eventData,
        riskLevel: this.calculateRiskLevel(eventData.eventType),
        location: await this.getLocationFromIP(),
      });

      await securityEvent.save();

      logger.info(
        `Security event logged: ${eventData.eventType} for user ${String(
          eventData.userId
        )}`
      );
    } catch (error) {
      logger.error('Failed to log security event:', error);
      // Don't throw error to avoid breaking the main flow
    }
  }

  /**
   * Calculate risk level based on event type
   */
  private calculateRiskLevel(eventType: string): 'low' | 'medium' | 'high' {
    const highRiskEvents = [
      'account_locked',
      'suspicious_activity',
      'multiple_failed_logins',
    ];
    const mediumRiskEvents = [
      'failed_login',
      'password_change',
      '2fa_disabled',
    ];

    if (highRiskEvents.includes(eventType)) {
      return 'high';
    } else if (mediumRiskEvents.includes(eventType)) {
      return 'medium';
    }

    return 'low';
  }

  /**
   * Get location from IP address (mock implementation)
   */
  private getLocationFromIP(): Promise<unknown> {
    // Mock implementation - in production, use a geolocation service
    return Promise.resolve({
      country: 'US',
      region: 'CA',
      city: 'San Francisco',
    });
  }

  /**
   * Get security events for a user
   */
  public async getUserSecurityEvents(
    userId: Types.ObjectId,
    limit: number = 50
  ): Promise<unknown[]> {
    return await SecurityEvent.find({ userId })
      .sort({ createdAt: -1 })
      .limit(limit)
      .lean();
  }

  /**
   * Get high-risk security events
   */
  public async getHighRiskEvents(hours: number = 24): Promise<unknown[]> {
    const since = new Date(Date.now() - hours * 60 * 60 * 1000);

    return await SecurityEvent.find({
      riskLevel: 'high',
      createdAt: { $gte: since },
    })
      .sort({ createdAt: -1 })
      .lean();
  }
}
