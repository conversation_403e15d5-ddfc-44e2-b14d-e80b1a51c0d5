# Job Service Environment Variables

# Basic Configuration
NODE_ENV=development
PORT=3003
MONGODB_URI=mongodb://localhost:27017/job_platform
REDIS_URL=redis://localhost:6379

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key
JWT_REFRESH_SECRET=your-super-secret-refresh-key
JWT_ACCESS_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d
BCRYPT_ROUNDS=12

# Service URLs
EMAIL_SERVICE_URL=http://notification-service:8080
USER_SERVICE_URL=http://user-service:8080

# Google OAuth (Optional)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# CORS Configuration
CORS_ORIGIN=*
LOG_LEVEL=info

# Job API Keys - Get these from the respective services
# Adzuna API (Free) - https://developer.adzuna.com/
ADZUNA_APP_ID=your-adzuna-app-id
ADZUNA_API_KEY=your-adzuna-api-key

# Indeed API (Free) - https://ads.indeed.com/jobroll/xmlfeed
INDEED_PUBLISHER_ID=your-indeed-publisher-id

# LinkedIn API (Paid) - https://developer.linkedin.com/
LINKEDIN_CLIENT_ID=your-linkedin-client-id
LINKEDIN_CLIENT_SECRET=your-linkedin-client-secret

# Glassdoor API (Paid) - https://www.glassdoor.com/developer/
GLASSDOOR_PARTNER_ID=your-glassdoor-partner-id
GLASSDOOR_API_KEY=your-glassdoor-api-key
