import { Schema } from 'mongoose';

export const baseSchema = new Schema(
  {
    isDeleted: {
      type: Boolean,
      default: false,
    },
    deletedAt: {
      type: Date,
    },
    deletedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
  },
  {
    timestamps: true,
    versionKey: false,
  }
);

// Add common methods
baseSchema.methods.softDelete = function (
  deletedBy?: string
): Promise<unknown> {
  this.isDeleted = true;
  this.deletedAt = new Date();
  if (deletedBy) {
    this.deletedBy = deletedBy;
  }
  return (this as { save: () => Promise<unknown> }).save();
};

baseSchema.methods.restore = function (): Promise<unknown> {
  this.isDeleted = false;
  this.deletedAt = undefined;
  this.deletedBy = undefined;
  return (this as { save: () => Promise<unknown> }).save();
};
