🚀 Starting Job Application Platform - True Microservices Architecture
[0;32m[SUCCESS][0m Node.js version: v22.18.0 ✓
[0;32m[SUCCESS][0m Environment files exist ✓
[0;34m[INFO][0m Starting infrastructure (Redis)...
time="2025-09-21T13:43:29+03:00" level=warning msg="/Users/<USER>/Developer/JobApplicationAutomator/Backend/docker-compose.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion"
 Network backend_job-platform-network  Creating
 Network backend_job-platform-network  Created
time="2025-09-21T13:43:29+03:00" level=warning msg="Found orphan containers ([job-platform-mongodb]) for this project. If you removed or renamed this service in your compose file, you can run this command with the --remove-orphans flag to clean it up."
 Container job-platform-redis  Creating
 Container job-platform-redis  Created
 Container job-platform-redis  Starting
 Container job-platform-redis  Started
[0;34m[INFO][0m Waiting for infrastructure to be ready...
[0;32m[SUCCESS][0m MongoDB Atlas connection configured ✓
[0;32m[SUCCESS][0m Redis is ready ✓
[0;34m[INFO][0m Installing dependencies for all services...
[0;34m[INFO][0m Installing root dependencies...

up to date, audited 873 packages in 1s

146 packages are looking for funding
  run `npm fund` for details

found 0 vulnerabilities
[0;34m[INFO][0m Installing dependencies for api-gateway...

up to date, audited 873 packages in 903ms

146 packages are looking for funding
  run `npm fund` for details

found 0 vulnerabilities
[0;32m[SUCCESS][0m api-gateway dependencies installed ✓
[0;34m[INFO][0m Installing dependencies for auth-service...

up to date, audited 873 packages in 1s

146 packages are looking for funding
  run `npm fund` for details

found 0 vulnerabilities
[0;32m[SUCCESS][0m auth-service dependencies installed ✓
[0;34m[INFO][0m Installing dependencies for user-service...

up to date, audited 873 packages in 878ms

146 packages are looking for funding
  run `npm fund` for details

found 0 vulnerabilities
[0;32m[SUCCESS][0m user-service dependencies installed ✓
[0;34m[INFO][0m Installing dependencies for job-service...

up to date, audited 873 packages in 1s

146 packages are looking for funding
  run `npm fund` for details

found 0 vulnerabilities
[0;32m[SUCCESS][0m job-service dependencies installed ✓
[0;34m[INFO][0m Installing dependencies for resume-service...

up to date, audited 873 packages in 906ms

146 packages are looking for funding
  run `npm fund` for details

found 0 vulnerabilities
[0;32m[SUCCESS][0m resume-service dependencies installed ✓
[0;32m[SUCCESS][0m All dependencies installed ✓
[0;34m[INFO][0m Starting all microservices...
[0;34m[INFO][0m Starting api-gateway on port 3000...
[0;32m[SUCCESS][0m api-gateway started successfully (PID: 2142) ✓
[0;34m[INFO][0m Starting auth-service on port 3001...
[0;32m[SUCCESS][0m auth-service started successfully (PID: 2173) ✓
[0;34m[INFO][0m Starting user-service on port 3002...
[0;32m[SUCCESS][0m user-service started successfully (PID: 2271) ✓
[0;34m[INFO][0m Starting job-service on port 3003...
[0;32m[SUCCESS][0m job-service started successfully (PID: 2307) ✓
[0;34m[INFO][0m Starting resume-service on port 3004...
[0;32m[SUCCESS][0m resume-service started successfully (PID: 2342) ✓
[0;32m[SUCCESS][0m All services started ✓
[0;34m[INFO][0m Performing health checks...
[0;32m[SUCCESS][0m API Gateway is healthy ✓
[0;32m[SUCCESS][0m Auth Service is healthy ✓
[0;32m[SUCCESS][0m User Service is healthy ✓
[0;32m[SUCCESS][0m Job Service is healthy ✓
[0;32m[SUCCESS][0m Resume Service is healthy ✓
[0;32m[SUCCESS][0m All services are healthy ✓

==========================================
🎉 Job Application Platform is Running!
==========================================

🌐 Services (True Microservices):
   API Gateway:    http://localhost:3000
   Auth Service:   http://localhost:3001
   User Service:   http://localhost:3002
   Job Service:    http://localhost:3003
   Resume Service: http://localhost:3004

🔍 Health Checks:
   curl http://localhost:3000/health
   curl http://localhost:3001/health
   curl http://localhost:3002/health

📊 Infrastructure:
   MongoDB:        MongoDB Atlas (Cloud)
   Redis:          redis://localhost:6379

📝 View Logs:
   tail -f logs/api-gateway.log
   tail -f logs/auth-service.log
   tail -f logs/user-service.log

🛑 Stop Services:
   ./scripts/deploy-microservices.sh stop

✨ Each service is completely independent!
   No shared libraries, true microservices architecture

Press Ctrl+C to stop all services
[0;34m[INFO][0m Services running... (Sun Sep 21 13:44:48 IDT 2025)
[0;34m[INFO][0m Services running... (Sun Sep 21 13:45:18 IDT 2025)
[0;34m[INFO][0m Services running... (Sun Sep 21 13:45:48 IDT 2025)
[0;34m[INFO][0m Services running... (Sun Sep 21 13:46:18 IDT 2025)
[0;34m[INFO][0m Services running... (Sun Sep 21 13:46:48 IDT 2025)
[0;34m[INFO][0m Services running... (Sun Sep 21 13:47:18 IDT 2025)
[0;34m[INFO][0m Services running... (Sun Sep 21 13:47:48 IDT 2025)
[0;34m[INFO][0m Services running... (Sun Sep 21 13:48:18 IDT 2025)
[0;34m[INFO][0m Services running... (Sun Sep 21 13:48:48 IDT 2025)
[0;34m[INFO][0m Services running... (Sun Sep 21 13:49:18 IDT 2025)
[0;34m[INFO][0m Services running... (Sun Sep 21 13:49:48 IDT 2025)
[0;34m[INFO][0m Services running... (Sun Sep 21 13:50:18 IDT 2025)
[0;34m[INFO][0m Services running... (Sun Sep 21 13:50:48 IDT 2025)
[0;34m[INFO][0m Services running... (Sun Sep 21 13:51:18 IDT 2025)
[0;34m[INFO][0m Services running... (Sun Sep 21 13:51:48 IDT 2025)
[0;34m[INFO][0m Services running... (Sun Sep 21 13:52:18 IDT 2025)
[0;34m[INFO][0m Stopping all services...
./scripts/deploy-microservices.sh: line 247:  2173 Hangup: 1               PORT=$port NODE_ENV=development npx tsx src/index.ts > "../../logs/$service.log" 2>&1  (wd: ~/Developer/JobApplicationAutomator/Backend/services/auth-service)
./scripts/deploy-microservices.sh: line 247:  2271 Hangup: 1               PORT=$port NODE_ENV=development npx tsx src/index.ts > "../../logs/$service.log" 2>&1  (wd: ~/Developer/JobApplicationAutomator/Backend/services/user-service)
./scripts/deploy-microservices.sh: line 247:  2307 Hangup: 1               PORT=$port NODE_ENV=development npx tsx src/index.ts > "../../logs/$service.log" 2>&1  (wd: ~/Developer/JobApplicationAutomator/Backend/services/job-service)
./scripts/deploy-microservices.sh: line 247:  2342 Hangup: 1               PORT=$port NODE_ENV=development npx tsx src/index.ts > "../../logs/$service.log" 2>&1  (wd: ~/Developer/JobApplicationAutomator/Backend/services/resume-service)
./scripts/deploy-microservices.sh: line 247:  7396 Hangup: 1               sleep 30
time="2025-09-21T13:52:45+03:00" level=warning msg="/Users/<USER>/Developer/JobApplicationAutomator/Backend/docker-compose.yml: the attribute `version` is obsolete, it will be ignored, please remove it to avoid potential confusion"
 Container job-platform-redis  Stopping
 Container job-platform-redis  Stopped
 Container job-platform-redis  Removing
 Container job-platform-redis  Removed
 Network backend_job-platform-network  Removing
 Network backend_job-platform-network  Removed
[0;32m[SUCCESS][0m All services stopped ✓
