#!/bin/bash

# Script to fix all shared library imports and convert to true microservices

set -e

echo "🔧 Converting to True Microservices Architecture..."

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Copy essential utilities to each service
copy_utils_to_service() {
    local service=$1
    local service_path="services/$service/src"
    
    if [ ! -d "$service_path" ]; then
        print_warning "Service $service not found, skipping..."
        return
    fi
    
    print_status "Copying utilities to $service..."
    
    # Create directories
    mkdir -p "$service_path/utils"
    mkdir -p "$service_path/types"
    mkdir -p "$service_path/config"
    mkdir -p "$service_path/database"
    mkdir -p "$service_path/middleware"
    
    # Copy logger
    if [ ! -f "$service_path/utils/logger.ts" ]; then
        cat > "$service_path/utils/logger.ts" << 'EOF'
import winston from 'winston';

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'SERVICE_NAME' },
  transports: [
    new winston.transports.File({ 
      filename: 'logs/error.log', 
      level: 'error' 
    }),
    new winston.transports.File({ 
      filename: 'logs/combined.log' 
    }),
  ],
});

if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.simple()
  }));
}

export { logger };
EOF
        # Replace SERVICE_NAME with actual service name
        sed -i '' "s/SERVICE_NAME/$service/g" "$service_path/utils/logger.ts"
    fi
    
    # Copy response utility
    if [ ! -f "$service_path/utils/response.ts" ]; then
        cat > "$service_path/utils/response.ts" << 'EOF'
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T | undefined;
  errors?: string[] | undefined;
  timestamp: string;
  path?: string | undefined;
  statusCode: number;
}

export class ResponseUtil {
  static success<T>(data: T, message: string = 'Success', statusCode: number = 200): ApiResponse<T> {
    return {
      success: true,
      message,
      data,
      timestamp: new Date().toISOString(),
      statusCode,
    };
  }

  static error(
    message: string = 'Error occurred',
    statusCode: number = 500,
    errors?: string[] | undefined,
    path?: string | undefined
  ): ApiResponse {
    return {
      success: false,
      message,
      errors: errors || [],
      timestamp: new Date().toISOString(),
      path,
      statusCode,
    };
  }

  static created<T>(data: T, message: string = 'Resource created successfully'): ApiResponse<T> {
    return this.success(data, message, 201);
  }

  static updated<T>(data: T, message: string = 'Resource updated successfully'): ApiResponse<T> {
    return this.success(data, message, 200);
  }

  static deleted(message: string = 'Resource deleted successfully'): ApiResponse {
    return this.success(null, message, 204);
  }
}
EOF
    fi
    
    # Copy database connection
    if [ ! -f "$service_path/database/connection.ts" ]; then
        cat > "$service_path/database/connection.ts" << 'EOF'
import mongoose from 'mongoose';
import { logger } from '../utils/logger';

class DatabaseConnection {
  private static instance: DatabaseConnection;
  private isConnected = false;

  private constructor() {}

  public static getInstance(): DatabaseConnection {
    if (!DatabaseConnection.instance) {
      DatabaseConnection.instance = new DatabaseConnection();
    }
    return DatabaseConnection.instance;
  }

  public async connect(): Promise<void> {
    if (this.isConnected) {
      logger.info('Database already connected');
      return;
    }

    try {
      const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/job_platform';
      const options: mongoose.ConnectOptions = {
        maxPoolSize: 10,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000,
        family: 4,
      };

      await mongoose.connect(mongoUri, options);
      this.isConnected = true;
      logger.info('MongoDB connected successfully');

      mongoose.connection.on('error', (error) => {
        logger.error('MongoDB connection error:', error);
        this.isConnected = false;
      });

      mongoose.connection.on('disconnected', () => {
        logger.warn('MongoDB disconnected');
        this.isConnected = false;
      });

      mongoose.connection.on('reconnected', () => {
        logger.info('MongoDB reconnected');
        this.isConnected = true;
      });

    } catch (error) {
      logger.error('MongoDB connection failed:', error);
      this.isConnected = false;
      throw error;
    }
  }

  public async disconnect(): Promise<void> {
    if (!this.isConnected) {
      return;
    }

    try {
      await mongoose.disconnect();
      this.isConnected = false;
      logger.info('MongoDB disconnected successfully');
    } catch (error) {
      logger.error('Error disconnecting from MongoDB:', error);
      throw error;
    }
  }

  public isHealthy(): boolean {
    return this.isConnected && mongoose.connection.readyState === 1;
  }
}

export const database = DatabaseConnection.getInstance();
EOF
    fi
    
    print_success "Utilities copied to $service ✓"
}

# Fix imports in service files
fix_imports_in_service() {
    local service=$1
    local service_path="services/$service/src"
    
    if [ ! -d "$service_path" ]; then
        return
    fi
    
    print_status "Fixing imports in $service..."
    
    # Find all TypeScript files and fix imports
    find "$service_path" -name "*.ts" -type f | while read -r file; do
        # Replace shared imports with local imports
        sed -i '' "s|from '@job-platform/shared/utils/logger'|from '../utils/logger'|g" "$file"
        sed -i '' "s|from '@job-platform/shared/utils/response'|from '../utils/response'|g" "$file"
        sed -i '' "s|from '@job-platform/shared/database'|from '../database/connection'|g" "$file"
        sed -i '' "s|from '@job-platform/shared/utils'|from '../utils/logger'|g" "$file"
        
        # More specific replacements
        sed -i '' "s|import { logger } from '@job-platform/shared/utils';|import { logger } from '../utils/logger';|g" "$file"
        sed -i '' "s|import { ResponseUtil } from '@job-platform/shared/utils/response';|import { ResponseUtil } from '../utils/response';|g" "$file"
        sed -i '' "s|import { database } from '@job-platform/shared/database';|import { database } from '../database/connection';|g" "$file"
    done
    
    print_success "Imports fixed in $service ✓"
}

# Update package.json for service
update_service_package_json() {
    local service=$1
    local package_file="services/$service/package.json"
    
    if [ ! -f "$package_file" ]; then
        return
    fi
    
    print_status "Updating package.json for $service..."
    
    # Remove shared dependency and add required packages
    if grep -q '"@job-platform/shared"' "$package_file"; then
        # Remove the shared dependency line
        sed -i '' '/"@job-platform\/shared"/d' "$package_file"
        
        # Add winston if not present
        if ! grep -q '"winston"' "$package_file"; then
            sed -i '' 's/"axios": "\^1\.7\.7"/"axios": "^1.7.7",\
    "winston": "^3.15.0"/' "$package_file"
        fi
    fi
    
    print_success "Package.json updated for $service ✓"
}

# Main execution
main() {
    print_status "Starting microservices conversion..."
    
    # List of services
    services=("api-gateway" "auth-service" "user-service" "job-service" "resume-service")
    
    for service in "${services[@]}"; do
        copy_utils_to_service "$service"
        fix_imports_in_service "$service"
        update_service_package_json "$service"
    done
    
    print_success "Microservices conversion completed! ✓"
    print_status "Each service is now independent with its own utilities"
    print_status "Run: ./scripts/deploy-microservices.sh to start the platform"
}

main "$@"