# API Gateway Analytics Endpoint Fix

## Problem Summary
The API Gateway was receiving a 400 Bad Request error when proxying requests to the user-service `/users/analytics` endpoint. The error message indicated "User ID is required", but the JWT token was present in the authorization header.

## Root Cause
The issue was that the microservices (user-service, resume-service, job-service) were expecting the `userId` as a query parameter instead of extracting it from the JWT token. The services lacked proper JWT authentication middleware to decode the token and extract user information.

## Solution Implemented

### 1. Added JWT Authentication Middleware
Created `auth.middleware.ts` in each service with:
- JWT token verification using the same secret as auth-service
- User context extraction from token payload
- Proper error handling for expired/invalid tokens
- TypeScript interface extensions for Express Request

### 2. Updated Analytics Endpoints
Modified the following endpoints to use JWT authentication:

#### User Service (`/users/analytics`)
- **Before**: Expected `userId` as query parameter
- **After**: Uses `req.user.id` from authenticated JWT token
- **Middleware**: `authenticateToken`

#### Resume Service (`/resumes/analytics`) 
- **Before**: Expected `userId` as query parameter
- **After**: Uses `req.user.id` from authenticated JWT token
- **Middleware**: `authenticateToken`

#### Job Service (`/applications/analytics`)
- **Before**: Expected `userId` as query parameter  
- **After**: Uses `req.user.id` from authenticated JWT token
- **Middleware**: `authenticateToken`

### 3. Dependencies Added
Added to each service:
```json
{
  "dependencies": {
    "passport": "^0.7.0",
    "passport-jwt": "^4.0.1", 
    "jsonwebtoken": "^9.0.2"
  },
  "devDependencies": {
    "@types/passport": "^1.0.16",
    "@types/passport-jwt": "^4.0.1",
    "@types/jsonwebtoken": "^9.0.6"
  }
}
```

## JWT Token Structure
The JWT tokens contain the following payload:
```json
{
  "userId": "507f1f77bcf86cd799439011",
  "email": "<EMAIL>", 
  "role": "user",
  "sessionId": "uuid-session-id",
  "iat": 1234567890,
  "exp": 1234568790,
  "iss": "job-platform-auth"
}
```

## API Usage Changes

### Before (Broken)
```bash
# Frontend had to somehow get userId and pass it
curl -H "Authorization: Bearer JWT_TOKEN" \
     "http://api-gateway/api/v1/users/analytics?userId=507f1f77bcf86cd799439011"
```

### After (Fixed)
```bash
# Frontend just needs to send JWT token
curl -H "Authorization: Bearer JWT_TOKEN" \
     "http://api-gateway/api/v1/users/analytics"
```

## Security Improvements
1. **User ID Validation**: Users can no longer manipulate their own userId
2. **Token Verification**: All analytics endpoints now properly verify JWT tokens
3. **Consistent Authentication**: All services use the same JWT verification logic
4. **Error Handling**: Proper 401 responses for authentication failures

## Testing
Use the provided test script to verify the fix:
```bash
node test-analytics-fix.js
```

Expected responses:
- **With valid JWT**: 200 OK with analytics data
- **Without JWT**: 401 "Access token required"  
- **Invalid JWT**: 401 "Invalid token"
- **Expired JWT**: 401 "Token expired"

## Files Modified

### User Service
- `src/middleware/auth.middleware.ts` (new)
- `src/routes/user.routes.ts` (updated)
- `package.json` (dependencies added)

### Resume Service  
- `src/middleware/auth.middleware.ts` (new)
- `src/routes/resume.routes.ts` (updated)
- `package.json` (dependencies added)

### Job Service
- `src/middleware/auth.middleware.ts` (new) 
- `src/routes/application.routes.ts` (updated)
- `package.json` (dependencies added)

## Deployment Notes
1. All services need to be rebuilt and redeployed
2. Ensure JWT_SECRET environment variable is consistent across all services
3. Frontend applications should continue working without changes
4. API Gateway requires no modifications

## Future Considerations
1. Consider implementing role-based access control (RBAC)
2. Add rate limiting per authenticated user
3. Implement token refresh logic in services if needed
4. Add comprehensive logging for security events
