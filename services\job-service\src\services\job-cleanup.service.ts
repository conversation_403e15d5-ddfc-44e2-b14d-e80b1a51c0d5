import { logger } from '../utils/logger';
import { Job } from '../models/job.model';
import { Application } from '../models/application.model';

export class JobCleanupService {
  /**
   * Clean up expired jobs
   */
  public async cleanupExpiredJobs(): Promise<void> {
    try {
      logger.info('Starting job cleanup process...');

      const now = new Date();
      
      // Find jobs where expiresAt < current date and status is active
      const expiredJobs = await Job.find({
        expiresAt: { $lt: now },
        status: 'active'
      });

      if (expiredJobs.length > 0) {
        // Update their status to 'expired'
        const result = await Job.updateMany(
          { _id: { $in: expiredJobs.map(job => job._id) } },
          { status: 'expired' }
        );
        
        logger.info(`Job cleanup completed. Expired ${result.modifiedCount} jobs.`);
      } else {
        logger.info('Job cleanup completed. No expired jobs found.');
      }
    } catch (error) {
      logger.error('Job cleanup failed:', error);
      throw error;
    }
  }

  /**
   * Archive old applications
   */
  public async archiveOldApplications(): Promise<void> {
    try {
      logger.info('Starting application archival process...');

      const sixMonthsAgo = new Date();
      sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
      
      // Find applications older than 6 months with final status
      const oldApplications = await Application.find({
        appliedAt: { $lt: sixMonthsAgo },
        status: { $in: ['rejected', 'accepted', 'withdrawn'] }
      });

      if (oldApplications.length > 0) {
        // Archive them by adding an archived flag
        const result = await Application.updateMany(
          { _id: { $in: oldApplications.map(app => app._id) } },
          { archived: true, archivedAt: new Date() }
        );
        
        logger.info(
          `Application archival completed. ` +
            `Archived ${result.modifiedCount} applications.`
        );
      } else {
        logger.info('Application archival completed. No old applications found.');
      }
    } catch (error) {
      logger.error('Application archival failed:', error);
      throw error;
    }
  }
}
