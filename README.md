# Job Application Platform - Backend

A production-ready microservices backend for a comprehensive job application platform built with Node.js, TypeScript, Express, and MongoDB.

## 🏗️ Architecture

This platform follows a microservices architecture with the following services:

- **API Gateway** (Port 3000) - Routes requests and handles cross-cutting concerns
- **Auth Service** (Port 3001) - Authentication, authorization, and session management
- **User Service** (Port 3002) - User management and profiles
- **Job Service** (Port 3003) - Job listings, applications, and matching
- **Resume Service** (Port 3004) - Resume upload, processing, and optimization
- **Analytics Service** (Port 3005) - User analytics and reporting
- **Notification Service** (Port 3006) - Multi-channel notifications
- **Integration Service** (Port 3007) - Third-party API integrations
- **Payment Service** (Port 3008) - Subscription and payment processing

## 🚀 Features

### Core Features
- **User Management**: Registration, authentication, profiles, and role-based access
- **Job Management**: Job listings, search, filtering, and recommendations
- **Application Tracking**: Complete application lifecycle management
- **Resume Processing**: AI-powered resume optimization and ATS compatibility
- **Real-time Notifications**: Email, push, SMS, and in-app notifications
- **Analytics**: Comprehensive user and platform analytics

### Advanced Features
- **AI/ML Integration**: Resume optimization, job matching, and market intelligence
- **Third-party Integrations**: LinkedIn, Indeed, Glassdoor APIs
- **Subscription Management**: Tiered pricing with Stripe integration
- **Security**: OWASP compliance, rate limiting, and comprehensive audit logging
- **Performance**: Caching, CDN integration, and horizontal scaling ready

## 🛠️ Technology Stack

- **Runtime**: Node.js 22.x
- **Language**: TypeScript 5.6+
- **Framework**: Express.js 5.x
- **Database**: MongoDB 7.0+ with Mongoose
- **Cache**: Redis 7.4+
- **Authentication**: JWT, OAuth2 (Google)
- **File Storage**: AWS S3 / Google Cloud Storage
- **Payment**: Stripe
- **Email**: SendGrid
- **Containerization**: Docker & Docker Compose
- **Testing**: Jest
- **Linting**: ESLint + Prettier

## 📋 Prerequisites

- Node.js 22.x or higher
- Docker and Docker Compose
- MongoDB 7.0+
- Redis 7.4+
- AWS account (for S3) or Google Cloud account
- Stripe account
- SendGrid account

## 🚀 Quick Start

### 1. Clone and Setup

```bash
git clone <repository-url>
cd job-application-platform-backend
```

### 2. Environment Configuration

```bash
cp .env.example .env
# Edit .env with your configuration values
```

### 3. Install Dependencies

```bash
npm install
```

### 4. Start Infrastructure (MongoDB, Redis)

```bash
docker-compose up -d mongodb redis
```

### 5. Initialize Database

The MongoDB container will automatically run the initialization script to create collections, indexes, and an admin user.

**Default Admin Credentials:**
- Email: `<EMAIL>`
- Password: `admin123`

### 6. Start All Services

```bash
# Development mode (all services)
npm run dev

# Or start individual services
npm run dev:api-gateway
npm run dev:auth
npm run dev:user
# ... etc
```

### 7. Verify Installation

Visit `http://localhost:3000/health` to check if all services are running.

## 📁 Project Structure

```
├── services/
│   ├── api-gateway/          # API Gateway service
│   ├── auth-service/         # Authentication service
│   ├── user-service/         # User management service
│   ├── job-service/          # Job management service
│   ├── resume-service/       # Resume processing service
│   ├── analytics-service/    # Analytics service
│   ├── notification-service/ # Notification service
│   ├── integration-service/  # Third-party integrations
│   └── payment-service/      # Payment processing service
├── shared/                   # Shared utilities and types
│   ├── types/               # TypeScript type definitions
│   ├── utils/               # Utility functions
│   ├── middleware/          # Express middleware
│   ├── database/            # Database connection and schemas
│   └── config/              # Configuration management
├── scripts/                 # Setup and utility scripts
├── docker-compose.yml       # Docker services configuration
└── package.json            # Root package.json
```

## 🔧 Development

### Running Tests

```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch
```

### Code Quality

```bash
# Lint all code
npm run lint

# Fix linting issues
npm run lint:fix

# Format code
npm run format
```

### Building for Production

```bash
# Build all services
npm run build

# Start in production mode
npm start
```

## 🐳 Docker Deployment

### Full Stack with Docker

```bash
# Start all services with Docker
docker-compose up -d

# View logs
docker-compose logs -f

# Stop all services
docker-compose down
```

### Production Deployment

```bash
# Build production images
docker-compose -f docker-compose.prod.yml build

# Deploy to production
docker-compose -f docker-compose.prod.yml up -d
```

## 📊 API Documentation

Once the services are running, API documentation is available at:

- API Gateway: `http://localhost:3000/api`
- Individual service health: `http://localhost:300X/health` (where X is the service port)

### Key Endpoints

```
# Authentication
POST /api/v1/auth/login
POST /api/v1/auth/register
POST /api/v1/auth/google
POST /api/v1/auth/refresh

# Users
GET /api/v1/users/profile
PUT /api/v1/users/profile
GET /api/v1/users/search

# Jobs
GET /api/v1/jobs
POST /api/v1/jobs/search
GET /api/v1/jobs/:id
POST /api/v1/jobs/:id/apply

# Resumes
POST /api/v1/resumes/upload
GET /api/v1/resumes
POST /api/v1/resumes/:id/optimize

# Applications
GET /api/v1/applications
PUT /api/v1/applications/:id
POST /api/v1/applications/bulk
```

## 🔒 Security Features

- **Authentication**: JWT with refresh tokens
- **Authorization**: Role-based access control (RBAC)
- **Rate Limiting**: Configurable per endpoint and user tier
- **Input Validation**: Comprehensive request validation
- **SQL Injection Protection**: MongoDB with Mongoose ODM
- **XSS Protection**: Helmet.js security headers
- **CORS**: Configurable cross-origin resource sharing
- **Encryption**: Bcrypt for passwords, AES-256 for sensitive data
- **Audit Logging**: Comprehensive security event logging

## 📈 Monitoring and Analytics

### Health Checks

- **Gateway**: `http://localhost:3000/health`
- **Detailed Health**: `http://localhost:3000/health/detailed`
- **Service Metrics**: `http://localhost:3000/metrics` (Admin only)

### Key Metrics Tracked

- User engagement and retention
- Application success rates
- System performance
- API response times
- Error rates and patterns
- Resource utilization

## 🎯 Subscription Tiers

### Free Tier
- Basic job search
- 5 applications per month
- 1 resume upload
- Basic analytics

### Premium Tier
- Unlimited applications
- AI resume optimization
- Priority support
- Advanced analytics
- "Be first to apply" feature

### Enterprise Tier
- Custom integrations
- Dedicated support
- Advanced reporting
- Custom branding
- API access

## 🔌 Third-Party Integrations

### LinkedIn Integration
- Profile synchronization
- Job application automation
- Network insights
- Company research

### Indeed Integration
- Job feed synchronization
- Application tracking
- Salary benchmarking

### Glassdoor Integration
- Company reviews and ratings
- Salary data
- Interview insights

## 🚨 Error Handling

The platform implements comprehensive error handling:

- **Structured Error Responses**: Consistent error format across all services
- **Error Logging**: Detailed error logging with context
- **Graceful Degradation**: Services continue operating when dependencies fail
- **Circuit Breakers**: Prevent cascade failures
- **Retry Logic**: Automatic retry for transient failures

## 📚 Development Guidelines

### Code Style
- Follow TypeScript strict mode
- Use ESLint and Prettier configurations
- Write comprehensive tests (>80% coverage)
- Document public APIs with JSDoc

### Git Workflow
- Feature branch development
- Pull request reviews required
- Automated testing on commits
- Semantic versioning

### Database Guidelines
- Use Mongoose for schema validation
- Implement soft deletes
- Create appropriate indexes
- Use transactions for multi-document operations

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Write tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📞 Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation wiki

---

**Built with ❤️ for modern job seekers and recruiters**