#!/bin/bash

# Development startup script for Frontend Service

set -e

echo "🚀 Starting Job Application Automator Frontend Service..."

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
  echo "📦 Installing dependencies..."
  npm install
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
  echo "📝 Creating .env file from template..."
  cp .env.example .env
  echo "⚠️  Please update .env file with your configuration"
fi

# Start development server
echo "🏃‍♂️ Starting development server on http://localhost:3010..."
npm run dev
