import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import { v4 as uuidv4 } from 'uuid';

const JWT_SECRET = process.env.JWT_SECRET ?? 'your-super-secret-jwt-key';
const JWT_REFRESH_SECRET =
  process.env.JWT_REFRESH_SECRET ?? 'your-super-secret-refresh-key';

export class EncryptionUtils {
  static async hashPassword(password: string): Promise<string> {
    const saltRounds = 12;
    return bcrypt.hash(password, saltRounds);
  }

  static async comparePassword(
    password: string,
    hashedPassword: string
  ): Promise<boolean> {
    return bcrypt.compare(password, hashedPassword);
  }

  static generateAccessToken(payload: Record<string, unknown>): string {
    return jwt.sign(payload, JWT_SECRET, {
      expiresIn: '15m',
      issuer: 'job-platform-auth',
    });
  }

  static generateRefreshToken(payload: Record<string, unknown>): string {
    return jwt.sign(payload, JWT_REFRESH_SECRET, {
      expiresIn: '7d',
      issuer: 'job-platform-auth',
    });
  }

  static verifyAccessToken(token: string): Record<string, unknown> {
    return jwt.verify(token, JWT_SECRET, {
      issuer: 'job-platform-auth',
    }) as Record<string, unknown>;
  }

  static verifyRefreshToken(token: string): Record<string, unknown> {
    return jwt.verify(token, JWT_REFRESH_SECRET, {
      issuer: 'job-platform-auth',
    }) as Record<string, unknown>;
  }

  static generateRandomToken(length: number = 32): string {
    return crypto.randomBytes(length).toString('hex');
  }

  static generateUUID(): string {
    return uuidv4();
  }

  static encrypt(text: string): string {
    const algorithm = 'aes-256-gcm';
    const key = crypto.scryptSync(JWT_SECRET, 'salt', 32);
    const iv = crypto.randomBytes(16);

    const cipher = crypto.createCipheriv(algorithm, key, iv);
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    const authTag = cipher.getAuthTag();

    return `${iv.toString('hex')}:${authTag.toString('hex')}:${encrypted}`;
  }

  static decrypt(encryptedData: string): string {
    const algorithm = 'aes-256-gcm';
    const key = crypto.scryptSync(JWT_SECRET, 'salt', 32);

    const parts = encryptedData.split(':');
    if (parts.length !== 3) {
      throw new Error('Invalid encrypted data format');
    }

    const iv = Buffer.from(parts[0]!, 'hex');
    const authTag = Buffer.from(parts[1]!, 'hex');
    const encrypted = parts[2]!;

    const decipher = crypto.createDecipheriv(algorithm, key, iv);
    decipher.setAuthTag(authTag);

    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    return decrypted;
  }
}
