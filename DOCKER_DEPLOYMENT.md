# Docker Deployment Guide for Resume-Automator

This guide explains how to deploy the Resume-Automator microservices using Docker, both locally and on DigitalOcean App Platform.

## 🐳 **Docker Configuration Overview**

### **Services and Ports**
All services now use port **8080** for consistency with DigitalOcean App Platform:

| Service | Local Port | Container Port | Health Check |
|---------|------------|----------------|--------------|
| API Gateway | 8080 | 8080 | ✅ |
| Auth Service | 8081 | 8080 | ✅ |
| User Service | 8082 | 8080 | ✅ |
| Job Service | 8083 | 8080 | ✅ |
| Resume Service | 8084 | 8080 | ✅ |
| Frontend Service | 8085 | 8080 | ✅ |

### **Key Features**
- ✅ Multi-stage builds for production optimization
- ✅ Non-root user security
- ✅ Health checks compatible with DigitalOcean
- ✅ Proper signal handling with dumb-init
- ✅ Layer caching for faster builds
- ✅ Environment variable configuration

## 🚀 **Local Development with Docker**

### **1. Prerequisites**
```bash
# Install Docker and Docker Compose
docker --version
docker-compose --version
```

### **2. Environment Setup**
```bash
# Copy the Docker environment template
cp .env.docker .env

# Edit the environment variables
nano .env
```

### **3. Build and Run Services**
```bash
# Build all services
docker-compose build

# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop all services
docker-compose down
```

### **4. Individual Service Management**
```bash
# Build specific service
docker-compose build api-gateway

# Start specific service
docker-compose up -d api-gateway

# View service logs
docker-compose logs -f api-gateway

# Restart service
docker-compose restart api-gateway
```

## 🌊 **DigitalOcean App Platform Deployment**

### **1. Switch to Docker Deployment**
In your DigitalOcean App Platform dashboard:

1. **Edit your app configuration**
2. **For each service, change:**
   - Source Type: `Docker`
   - Dockerfile Path: `services/[service-name]/Dockerfile`
   - Build Context: `/` (root directory)

### **2. Service Configuration**
Update each service in DigitalOcean:

#### **API Gateway**
```yaml
name: api-gateway
source_dir: /
dockerfile_path: services/api-gateway/Dockerfile
instance_count: 1
instance_size_slug: basic-xxs
http_port: 8080
```

#### **Auth Service**
```yaml
name: auth-service
source_dir: /
dockerfile_path: services/auth-service/Dockerfile
instance_count: 1
instance_size_slug: basic-xxs
http_port: 8080
```

#### **Frontend Service**
```yaml
name: frontend-service
source_dir: /
dockerfile_path: services/frontend-service/Dockerfile
instance_count: 1
instance_size_slug: basic-xxs
http_port: 8080
```

### **3. Environment Variables**
Set these in DigitalOcean App Platform:

```bash
# Database
MONGODB_URI=mongodb+srv://username:<EMAIL>/jobplatform?retryWrites=true&w=majority

# JWT
JWT_SECRET=your-super-secret-jwt-key-minimum-32-characters-long
JWT_REFRESH_SECRET=your-super-secret-refresh-key-minimum-32-characters-long

# External Services
GOOGLE_CLIENT_ID=your-google-oauth-client-id
GOOGLE_CLIENT_SECRET=your-google-oauth-client-secret
SENDGRID_API_KEY=your-sendgrid-api-key
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_S3_BUCKET=your-s3-bucket-name

# Frontend
VITE_API_BASE_URL=https://your-app.ondigitalocean.app/api/v1
```

## 📊 **Performance Benefits**

### **Docker vs NPM Deployment Speed**

| Phase | NPM Deployment | Docker Deployment | Improvement |
|-------|----------------|-------------------|-------------|
| Git Clone | 10-30s | 10-30s | Same |
| Runtime Setup | 30-60s | 30-60s (cached) | 50% faster after first build |
| Dependencies | 60-180s | 20-60s (cached) | 60% faster |
| Build | 30-60s | 15-30s | 50% faster |
| **Total** | **3-6 minutes** | **1.5-3 minutes** | **50-60% faster** |

### **Additional Benefits**
- ✅ **Consistent environments** across development and production
- ✅ **Faster dependency resolution** with layer caching
- ✅ **Parallel builds** for multiple services
- ✅ **Better resource utilization**
- ✅ **Easier rollbacks** with image versioning

## 🔧 **Troubleshooting**

### **Common Issues**

#### **Build Failures**
```bash
# Clear Docker cache
docker system prune -a

# Rebuild without cache
docker-compose build --no-cache
```

#### **Port Conflicts**
```bash
# Check port usage
netstat -tulpn | grep :8080

# Stop conflicting services
docker-compose down
```

#### **Health Check Failures**
```bash
# Check service health
docker-compose ps
docker-compose logs [service-name]

# Test health endpoint manually
curl http://localhost:8080/health
```

## 📝 **Next Steps**

1. **Test locally** with `docker-compose up`
2. **Update DigitalOcean** app configuration to use Docker
3. **Set environment variables** in DigitalOcean
4. **Deploy and monitor** the updated services
5. **Verify performance improvements**

The Docker setup is now ready for faster, more reliable deployments! 🎉
