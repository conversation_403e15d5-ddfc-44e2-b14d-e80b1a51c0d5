# Docker Environment Configuration for Resume-Automator
# Copy this file to .env for local Docker development
# For production, set these variables in your deployment environment

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
MONGO_ROOT_USERNAME=admin
MONGO_ROOT_PASSWORD=your-secure-mongo-password-change-in-production
MONGODB_URI=***********************************************************************************************************

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
REDIS_PASSWORD=your-secure-redis-password-change-in-production
REDIS_URL=redis://:your-secure-redis-password-change-in-production@redis:6379

# =============================================================================
# JWT CONFIGURATION
# =============================================================================
JWT_SECRET=your-super-secret-jwt-key-minimum-32-characters-long-change-in-production
JWT_REFRESH_SECRET=your-super-secret-refresh-key-minimum-32-characters-long-change-in-production
JWT_ACCESS_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
BCRYPT_ROUNDS=12
CORS_ORIGIN=*

# =============================================================================
# GOOGLE OAUTH CONFIGURATION
# =============================================================================
GOOGLE_CLIENT_ID=your-google-oauth-client-id
GOOGLE_CLIENT_SECRET=your-google-oauth-client-secret
GOOGLE_CALLBACK_URL=http://localhost:8081/api/v1/auth/google/callback

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================
SENDGRID_API_KEY=your-sendgrid-api-key
FROM_EMAIL=<EMAIL>

# =============================================================================
# AWS S3 CONFIGURATION
# =============================================================================
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-aws-access-key-id
AWS_SECRET_ACCESS_KEY=your-aws-secret-access-key
AWS_S3_BUCKET=job-platform-resumes-dev

# =============================================================================
# FRONTEND CONFIGURATION
# =============================================================================
FRONTEND_URL=http://localhost:8085
FRONTEND_API_BASE_URL=http://localhost:8080/api/v1

# =============================================================================
# EXTERNAL API CONFIGURATION (for future services)
# =============================================================================
LINKEDIN_CLIENT_ID=your-linkedin-client-id
LINKEDIN_CLIENT_SECRET=your-linkedin-client-secret
INDEED_API_KEY=your-indeed-api-key
GLASSDOOR_API_KEY=your-glassdoor-api-key

# =============================================================================
# PAYMENT CONFIGURATION (for future services)
# =============================================================================
STRIPE_SECRET_KEY=your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=your-stripe-webhook-secret

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_LEVEL=info
NODE_ENV=development
