import { Router, Request } from 'express';
import { ResponseUtil } from '../utils/response';
import { User } from '../models/user.model';
import { authenticateToken } from '../middleware/auth.middleware';
import { userService } from '../services/user.service';
import { uploadSingle, extractUserInfo } from '../middleware/upload.middleware';

const router = Router();

// Get user profile
router.get('/profile', authenticateToken, async (req, res) => {
  try {
    const userId = req.user!.id;

    const user = await userService.findById(userId);
    if (!user) {
      const response = ResponseUtil.error('User not found', 404);
      return res.status(response.statusCode).json(response);
    }

    const response = ResponseUtil.success(user, 'User profile retrieved');
    return res.status(response.statusCode).json(response);
  } catch (error) {
    const response = ResponseUtil.error('Failed to retrieve user profile', 500);
    return res.status(response.statusCode).json(response);
  }
});

// Update user profile
router.put('/profile', authenticateToken, async (req, res) => {
  try {
    const userId = req.user!.id;
    const updateData = req.body;
    
    console.log('Profile update request:', {
      userId,
      updateData: JSON.stringify(updateData, null, 2)
    });
    
    // Validate and sanitize input data
    const allowedFields = [
      'firstName', 'lastName', 'avatar', 'profile', 'preferences'
    ];
    
    const filteredData: any = {};
    for (const field of allowedFields) {
      if (updateData[field] !== undefined) {
        filteredData[field] = updateData[field];
      }
    }
    
    console.log('Filtered data for update:', JSON.stringify(filteredData, null, 2));
    
    const user = await userService.updateById(userId, filteredData);
    
    if (!user) {
      const response = ResponseUtil.error('User not found', 404);
      return res.status(response.statusCode).json(response);
    }
    
    console.log('Profile updated successfully for user:', userId);
    const response = ResponseUtil.success(user, 'Profile updated successfully');
    return res.status(response.statusCode).json(response);
  } catch (error: any) {
    console.error('Profile update error:', error);
    const response = ResponseUtil.error(
      error.message || 'Failed to update profile', 
      500
    );
    return res.status(response.statusCode).json(response);
  }
});

// Update specific profile section
router.patch('/profile/:section', authenticateToken, async (req, res) => {
  try {
    const userId = req.user!.id;
    const { section } = req.params;
    const updateData = req.body;
    
    // Validate section
    const validSections = ['basic', 'professional', 'education', 'skills', 'contact', 'preferences'];
    if (!validSections.includes(section || '')) {
      const response = ResponseUtil.error('Invalid profile section', 400);
      return res.status(response.statusCode).json(response);
    }
    
    // Map section to database field
    const sectionMapping: { [key: string]: string } = {
      'basic': 'profile',
      'professional': 'profile',
      'education': 'profile',
      'skills': 'profile',
      'contact': 'profile',
      'preferences': 'preferences'
    };
    
    const dbField = sectionMapping[section || ''];
    const updateQuery: any = { updatedAt: new Date() };
    
    if (section === 'basic') {
      // Handle basic profile fields
      if (updateData.bio !== undefined) updateQuery[`${dbField}.bio`] = updateData.bio;
      if (updateData.phoneNumber !== undefined) updateQuery[`${dbField}.phoneNumber`] = updateData.phoneNumber;
      if (updateData.location !== undefined) updateQuery[`${dbField}.location`] = updateData.location;
      if (updateData.dateOfBirth !== undefined) updateQuery[`${dbField}.dateOfBirth`] = updateData.dateOfBirth;
      if (updateData.nationality !== undefined) updateQuery[`${dbField}.nationality`] = updateData.nationality;
    } else if (section === 'professional') {
      // Handle professional fields
      if (updateData.currentPosition !== undefined) updateQuery[`${dbField}.currentPosition`] = updateData.currentPosition;
      if (updateData.currentCompany !== undefined) updateQuery[`${dbField}.currentCompany`] = updateData.currentCompany;
      if (updateData.yearsOfExperience !== undefined) updateQuery[`${dbField}.yearsOfExperience`] = updateData.yearsOfExperience;
      if (updateData.expectedSalary !== undefined) updateQuery[`${dbField}.expectedSalary`] = updateData.expectedSalary;
      if (updateData.experience !== undefined) updateQuery[`${dbField}.experience`] = updateData.experience;
    } else if (section === 'education') {
      if (updateData.education !== undefined) updateQuery[`${dbField}.education`] = updateData.education;
    } else if (section === 'skills') {
      if (updateData.skills !== undefined) updateQuery[`${dbField}.skills`] = updateData.skills;
    } else if (section === 'contact') {
      if (updateData.website !== undefined) updateQuery[`${dbField}.website`] = updateData.website;
      if (updateData.linkedin !== undefined) updateQuery[`${dbField}.linkedin`] = updateData.linkedin;
      if (updateData.github !== undefined) updateQuery[`${dbField}.github`] = updateData.github;
      if (updateData.portfolio !== undefined) updateQuery[`${dbField}.portfolio`] = updateData.portfolio;
    } else if (section === 'preferences') {
      if (updateData.notifications !== undefined) updateQuery[`${dbField}.notifications`] = updateData.notifications;
      if (updateData.privacy !== undefined) updateQuery[`${dbField}.privacy`] = updateData.privacy;
      if (updateData.jobAlerts !== undefined) updateQuery[`${dbField}.jobAlerts`] = updateData.jobAlerts;
    }
    
    const user = await userService.updateProfileSection(userId, section || '', updateData);
    
    if (!user) {
      const response = ResponseUtil.error('User not found', 404);
      return res.status(response.statusCode).json(response);
    }
    
    const response = ResponseUtil.success(user, `${section} profile updated successfully`);
    return res.status(response.statusCode).json(response);
  } catch (error: any) {
    console.error('Profile section update error:', error);
    const response = ResponseUtil.error(
      error.message || 'Failed to update profile section', 
      500
    );
    return res.status(response.statusCode).json(response);
  }
});

// Get user analytics
router.get('/analytics', authenticateToken, async (req, res) => {
  try {
    const userId = req.user!.id;

    const analytics = await userService.getUserAnalytics(userId);
    
    const response = ResponseUtil.success(
      analytics,
      'User analytics retrieved successfully'
    );

    return res.status(response.statusCode).json(response);
  } catch (error) {
    const response = ResponseUtil.error('Failed to retrieve user analytics', 500);
    return res.status(response.statusCode).json(response);
  }
});

// Link Google account
router.post('/link-google', authenticateToken, async (req, res) => {
  try {
    const userId = req.user!.id;
    const { googleId } = req.body;

    if (!googleId) {
      const response = ResponseUtil.error('Google ID is required', 400);
      return res.status(response.statusCode).json(response);
    }

    const user = await userService.linkGoogleAccount(userId, googleId);
    
    const response = ResponseUtil.success(user, 'Google account linked successfully');
    return res.status(response.statusCode).json(response);
  } catch (error: any) {
    const response = ResponseUtil.error(
      error.message || 'Failed to link Google account', 
      500
    );
    return res.status(response.statusCode).json(response);
  }
});

// Unlink Google account
router.post('/unlink-google', authenticateToken, async (req, res) => {
  try {
    const userId = req.user!.id;

    const user = await userService.unlinkGoogleAccount(userId);
    
    const response = ResponseUtil.success(user, 'Google account unlinked successfully');
    return res.status(response.statusCode).json(response);
  } catch (error: any) {
    const response = ResponseUtil.error(
      error.message || 'Failed to unlink Google account', 
      500
    );
    return res.status(response.statusCode).json(response);
  }
});

// Search users
router.get('/search', async (req, res) => {
  try {
    const result = await userService.searchUsers(req.query);
    
    const response = ResponseUtil.success(result, 'Users retrieved successfully');
    return res.status(response.statusCode).json(response);
  } catch (error: any) {
    const response = ResponseUtil.error(
      error.message || 'Failed to search users', 
      500
    );
    return res.status(response.statusCode).json(response);
  }
});

// Upload avatar
router.post('/avatar', authenticateToken, async (req, res) => {
  try {
    const userId = req.user!.id;
    
    // In a real implementation, you would handle file upload here
    // For now, we'll accept a URL from the request body
    const { avatarUrl } = req.body;
    
    if (!avatarUrl) {
      const response = ResponseUtil.error('Avatar URL is required', 400);
      return res.status(response.statusCode).json(response);
    }

    const user = await userService.updateAvatar(userId, avatarUrl);
    
    const response = ResponseUtil.success(user, 'Avatar updated successfully');
    return res.status(response.statusCode).json(response);
  } catch (error: any) {
    const response = ResponseUtil.error(
      error.message || 'Failed to update avatar', 
      500
    );
    return res.status(response.statusCode).json(response);
  }
});

// Delete avatar
router.delete('/avatar', authenticateToken, async (req, res) => {
  try {
    const userId = req.user!.id;
    const user = await userService.deleteAvatar(userId);
    
    const response = ResponseUtil.success(user, 'Avatar removed successfully');
    return res.status(response.statusCode).json(response);
  } catch (error: any) {
    const response = ResponseUtil.error(
      error.message || 'Failed to remove avatar', 
      500
    );
    return res.status(response.statusCode).json(response);
  }
});

// Upload avatar with file upload
router.post('/avatar/upload', 
  authenticateToken, 
  extractUserInfo,
  uploadSingle('avatar'),
  async (req: Request, res) => {
    try {
      const userId = req.user!.id;
      const { username } = (req as any).userInfo;
      
      const multerReq = req as Request & { file?: Express.Multer.File };
      if (!multerReq.file) {
        const response = ResponseUtil.error('No file uploaded', 400);
        return res.status(response.statusCode).json(response);
      }

      console.log('Avatar upload request:', {
        userId,
        username,
        fileSize: multerReq.file.size,
        fileType: multerReq.file.mimetype
      });

      const user = await userService.uploadAvatar(userId, multerReq.file.buffer, username);
      
      console.log('Avatar uploaded successfully for user:', userId);
      const response = ResponseUtil.success(user, 'Avatar uploaded successfully');
      return res.status(response.statusCode).json(response);
    } catch (error: any) {
      console.error('Avatar upload error:', error);
      
      // Handle specific upload errors
      if (error.message?.includes('Cloudinary is not configured')) {
        const response = ResponseUtil.error(
          'Image upload service is not configured. Please contact support.',
          503
        );
        return res.status(response.statusCode).json(response);
      }
      
      if (error.message?.includes('File too large')) {
        const response = ResponseUtil.error(
          'File is too large. Please choose a smaller image (max 2MB).',
          413
        );
        return res.status(response.statusCode).json(response);
      }
      
      if (error.message?.includes('Upload timeout')) {
        const response = ResponseUtil.error(
          'Upload timed out. Please try with a smaller image.',
          408
        );
        return res.status(response.statusCode).json(response);
      }
      
      if (error.code === 'ECONNRESET' || error.message?.includes('aborted')) {
        const response = ResponseUtil.error(
          'Connection lost during upload. Please try with a smaller image (max 2MB).',
          408
        );
        return res.status(response.statusCode).json(response);
      }
      
      const response = ResponseUtil.error(
        error.message || 'Failed to upload avatar', 
        500
      );
      return res.status(response.statusCode).json(response);
    }
  }
);

// Get optimized avatar URL
router.get('/avatar/optimized', authenticateToken, async (req, res) => {
  try {
    const userId = req.user!.id;
    const { size = 300 } = req.query;
    
    const user = await userService.findById(userId);
    if (!user) {
      const response = ResponseUtil.error('User not found', 404);
      return res.status(response.statusCode).json(response);
    }

    const optimizedUrl = userService.getOptimizedAvatarUrl(
      user.avatar || '', 
      parseInt(size as string)
    );
    
    const response = ResponseUtil.success(
      { optimizedUrl, originalUrl: user.avatar }, 
      'Optimized avatar URL generated'
    );
    return res.status(response.statusCode).json(response);
  } catch (error: any) {
    const response = ResponseUtil.error(
      error.message || 'Failed to generate optimized avatar URL', 
      500
    );
    return res.status(response.statusCode).json(response);
  }
});

export { router as userRoutes };
