import React, { useState, useEffect } from 'react';
import { Search, MapPin, Briefcase, Filter, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Link } from 'react-router-dom';
import { apiService } from '@/services/api';

interface Job {
  id: string;
  title: string;
  company: string;
  location: string;
  type: string;
  salary: {
    min?: number;
    max?: number;
    range?: string;
  };
  createdAt: string;
  viewCount: number;
}

export const JobsPage: React.FC = () => {
  const [jobs, setJobs] = useState<Job[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [location, setLocation] = useState('');

  useEffect(() => {
    const fetchJobs = async () => {
      try {
        setLoading(true);
        const params = new URLSearchParams();
        if (searchTerm) params.append('search', searchTerm);
        if (location) params.append('location', location);
        
        const response = await apiService.get(`/jobs?${params.toString()}`) as { jobs: Job[], total: number, page: number, limit: number, totalPages: number };
        setJobs(response.jobs || []);
      } catch (err) {
        setError('Failed to load jobs');
        console.error('Error fetching jobs:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchJobs();
  }, [searchTerm, location]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Search is triggered by useEffect when searchTerm or location changes
  };

  return (
    <div className="p-6">
      <div className="mb-8">
        <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Find Your Next Job</h1>
        <p className="text-gray-600 mt-2">
          Discover opportunities that match your skills and preferences
        </p>
      </div>

      {/* Search and filters */}
      <div className="card mb-6">
        <div className="card-body">
          <form onSubmit={handleSearch} className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1">
              <label htmlFor="job-search" className="sr-only">
                Search for jobs
              </label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  id="job-search"
                  name="jobSearch"
                  type="text"
                  placeholder="Job title, keywords, or company"
                  className="pl-10"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  title="Search for jobs by title, keywords, or company name"
                  aria-label="Search for jobs by title, keywords, or company name"
                />
              </div>
            </div>
            <div className="flex-1">
              <label htmlFor="job-location" className="sr-only">
                Job location
              </label>
              <div className="relative">
                <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  id="job-location"
                  name="jobLocation"
                  type="text"
                  placeholder="Location or remote"
                  className="pl-10"
                  value={location}
                  onChange={(e) => setLocation(e.target.value)}
                  title="Filter jobs by location or search for remote positions"
                  aria-label="Filter jobs by location or search for remote positions"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Button variant="outline">
                <Filter className="w-4 h-4 mr-2" />
                Filters
              </Button>
              <Button type="submit">
                <Search className="w-4 h-4 mr-2" />
                Search
              </Button>
            </div>
          </form>
        </div>
      </div>

      {/* Job listings */}
      {loading ? (
        <div className="flex items-center justify-center h-64">
          <Loader2 className="w-8 h-8 animate-spin text-primary-600" />
        </div>
      ) : error ? (
        <div className="card">
          <div className="card-body text-center">
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Error Loading Jobs</h2>
            <p className="text-gray-600">{error}</p>
          </div>
        </div>
      ) : jobs.length === 0 ? (
        <div className="card">
          <div className="card-body text-center">
            <h2 className="text-xl font-semibold text-gray-900 mb-2">No Jobs Found</h2>
            <p className="text-gray-600">Try adjusting your search criteria</p>
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          {jobs.map((job) => (
            <div key={job.id} className="card hover-lift">
              <div className="card-body">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                        <Briefcase className="w-6 h-6 text-primary-600" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">
                          {job.title}
                        </h3>
                        <p className="text-gray-600">{job.company}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-4 text-sm text-gray-500 mb-3">
                      <span className="flex items-center gap-1">
                        <MapPin className="w-4 h-4" />
                        {job.location}
                      </span>
                      <span>{job.type}</span>
                      <span>{job.salary.range || `$${job.salary.min?.toLocaleString()} - $${job.salary.max?.toLocaleString()}`}</span>
                    </div>
                    <p className="text-gray-600 text-sm">
                      Posted {new Date(job.createdAt).toLocaleDateString()} • {job.viewCount} views
                    </p>
                  </div>
                  <div className="ml-6 flex flex-col items-end gap-2">
                    <span className="text-sm text-gray-500">{new Date(job.createdAt).toLocaleDateString()}</span>
                    <Link to={`/jobs/${job.id}`}>
                      <Button size="sm">View Details</Button>
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Load more */}
      <div className="text-center mt-8">
        <Button variant="outline">Load More Jobs</Button>
      </div>
    </div>
  );
};
