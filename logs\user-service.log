info: MongoDB connected successfully {"service":"user-service","timestamp":"2025-09-21T10:47:54.502Z"}
info: 👤 User Service running on port 3002 {"service":"user-service","timestamp":"2025-09-21T10:47:54.506Z"}
info: 📝 Environment: development {"service":"user-service","timestamp":"2025-09-21T10:47:54.506Z"}
info: 🔗 Health Check: http://localhost:3002/health {"service":"user-service","timestamp":"2025-09-21T10:47:54.507Z"}
info: 🔄 Graceful shutdown initiated... {"service":"user-service","timestamp":"2025-09-21T10:51:15.983Z"}
info: ✅ HTTP server closed {"service":"user-service","timestamp":"2025-09-21T10:51:15.985Z"}
warn: <PERSON><PERSON>D<PERSON> disconnected {"service":"user-service","timestamp":"2025-09-21T10:51:16.005Z"}
info: MongoDB disconnected successfully {"service":"user-service","timestamp":"2025-09-21T10:51:16.005Z"}
info: ✅ Graceful shutdown completed {"service":"user-service","timestamp":"2025-09-21T10:51:16.005Z"}
 /health HTTP/1.1" 200 406 "-" "node" {"service":"user-service","timestamp":"2025-09-21T10:48:17.737Z"}
info: ::1 - - [21/Sep/2025:10:48:47 +0000] "GET /health HTTP/1.1" 200 406 "-" "node" {"service":"user-service","timestamp":"2025-09-21T10:48:47.722Z"}
info: ::1 - - [21/Sep/2025:10:49:17 +0000] "GET /health HTTP/1.1" 200 406 "-" "node" {"service":"user-service","timestamp":"2025-09-21T10:49:17.721Z"}
info: ::1 - - [21/Sep/2025:10:49:47 +0000] "GET /health HTTP/1.1" 200 406 "-" "node" {"service":"user-service","timestamp":"2025-09-21T10:49:47.721Z"}
info: ::1 - - [21/Sep/2025:10:50:17 +0000] "GET /health HTTP/1.1" 200 406 "-" "node" {"service":"user-service","timestamp":"2025-09-21T10:50:17.724Z"}
info: ::1 - - [21/Sep/2025:10:50:47 +0000] "GET /health HTTP/1.1" 200 406 "-" "node" {"service":"user-service","timestamp":"2025-09-21T10:50:47.727Z"}
info: ::1 - - [21/Sep/2025:10:52:23 +0000] "GET /health HTTP/1.1" 200 406 "-" "node" {"service":"user-service","timestamp":"2025-09-21T10:52:23.478Z"}
