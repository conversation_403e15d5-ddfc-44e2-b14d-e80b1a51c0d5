import { Router } from 'express';
import { ResponseUtil } from '../utils/response';
import { JobSyncService } from '../services/job-sync.service';
import { MonitoringService } from '../services/monitoring.service';
import { Job } from '../models/job.model';
import { authenticateToken } from '../middleware/auth.middleware';

const router = Router();
const jobSyncService = new JobSyncService();
const monitoringService = MonitoringService.getInstance();

// Admin-only middleware
const requireRootAdmin = (req: any, res: any, next: any) => {
  if (!req.user) {
    const response = ResponseUtil.error('Authentication required', 401);
    return res.status(response.statusCode).json(response);
  }
  
  if (req.user.role !== 'root_admin') {
    const response = ResponseUtil.error('ROOT Admin access required', 403);
    return res.status(response.statusCode).json(response);
  }
  
  next();
};

/**
 * Dashboard Overview - Get comprehensive system status
 */
router.get('/overview', authenticateToken, requireRootAdmin, async (req, res) => {
  try {
    const [systemHealth, syncStats, jobStats] = await Promise.all([
      monitoringService.getSystemHealth(),
      jobSyncService.getSyncStats(),
      getJobStatistics()
    ]);

    // Check API key status
    const apiKeyStatus = {
      adzuna: !!(process.env.ADZUNA_APP_ID && process.env.ADZUNA_API_KEY),
      indeed: !!(process.env.INDEED_PUBLISHER_ID),
      linkedin: !!(process.env.LINKEDIN_CLIENT_ID && process.env.LINKEDIN_CLIENT_SECRET),
      glassdoor: !!(process.env.GLASSDOOR_PARTNER_ID && process.env.GLASSDOOR_API_KEY),
    };

    const overview = {
      system: {
        status: systemHealth.status,
        uptime: systemHealth.services.uptime,
        memory: systemHealth.services.memory,
        database: systemHealth.services.database
      },
      sync: {
        totalJobs: syncStats.totalJobs,
        externalJobs: syncStats.externalJobs,
        internalJobs: syncStats.internalJobs,
        activeJobs: syncStats.activeJobs,
        lastSync: syncStats.lastSync
      },
      metrics: monitoringService.getMetrics(),
      recentActivity: await getRecentActivity(),
      apiKeys: apiKeyStatus
    };

    const response = ResponseUtil.success(overview, 'Dashboard overview retrieved successfully');
    return res.status(response.statusCode).json(response);
  } catch (error) {
    const response = ResponseUtil.error('Failed to retrieve dashboard overview', 500);
    return res.status(response.statusCode).json(response);
  }
});

/**
 * Job Sync Control - Start/Stop/Status
 */
router.post('/sync/start', authenticateToken, requireRootAdmin, async (req, res) => {
  try {
    const { query = 'developer', location = 'us', maxJobs = 50, categories = false } = req.body;
    
    // Input validation
    if (typeof query !== 'string' || query.length > 100) {
      const response = ResponseUtil.error('Invalid query parameter', 400);
      return res.status(response.statusCode).json(response);
    }
    
    if (typeof location !== 'string' || location.length > 50) {
      const response = ResponseUtil.error('Invalid location parameter', 400);
      return res.status(response.statusCode).json(response);
    }
    
    if (typeof maxJobs !== 'number' || maxJobs < 1 || maxJobs > 100) {
      const response = ResponseUtil.error('Invalid maxJobs parameter (must be 1-100)', 400);
      return res.status(response.statusCode).json(response);
    }

    let result;
    if (categories) {
      await jobSyncService.syncJobsByCategory();
      result = { message: 'Category-based sync started', categories: true };
    } else {
      result = await jobSyncService.syncJobs(query, location, maxJobs);
    }

    const response = ResponseUtil.success(result, 'Job sync started successfully');
    return res.status(response.statusCode).json(response);
  } catch (error) {
    const response = ResponseUtil.error('Failed to start job sync', 500);
    return res.status(response.statusCode).json(response);
  }
  
});

/**
 * Get Sync Status and Progress
 */
router.get('/sync/status', authenticateToken, requireRootAdmin, async (req, res) => {
  try {
    const stats = await jobSyncService.getSyncStats();
    const metrics = monitoringService.getMetrics();
    
    const status = {
      ...stats,
      metrics: {
        successRate: metrics.successRate,
        averageSyncTime: metrics.averageSyncTime,
        totalSyncs: metrics.totalSyncs
      },
      lastSyncTime: metrics.lastSyncTime
    };

    const response = ResponseUtil.success(status, 'Sync status retrieved successfully');
    return res.status(response.statusCode).json(response);
  } catch (error) {
    const response = ResponseUtil.error('Failed to retrieve sync status', 500);
    return res.status(response.statusCode).json(response);
  }
});

/**
 * Job Management - Get jobs with filters
 */
router.get('/jobs', authenticateToken, requireRootAdmin, async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 20, 
      status = 'active', 
      source, 
      type, 
      location,
      company,
      skills,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    const skip = (Number(page) - 1) * Number(limit);
    const filter: any = { status };
    
    if (source) filter.source = source;
    if (type) filter.type = type;
    if (location) filter.location = { $regex: location, $options: 'i' };
    if (company) filter.company = { $regex: company, $options: 'i' };
    if (skills) filter.skills = { $in: Array.isArray(skills) ? skills : [skills] };

    const sort: any = {};
    sort[sortBy as string] = sortOrder === 'desc' ? -1 : 1;

    const [jobs, total] = await Promise.all([
      Job.find(filter)
        .sort(sort)
        .skip(skip)
        .limit(Number(limit))
        .lean(),
      Job.countDocuments(filter)
    ]);

    const response = ResponseUtil.success({
      jobs,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        totalPages: Math.ceil(total / Number(limit))
      }
    }, 'Jobs retrieved successfully');

    return res.status(response.statusCode).json(response);
  } catch (error) {
    const response = ResponseUtil.error('Failed to retrieve jobs', 500);
    return res.status(response.statusCode).json(response);
  }
});

/**
 * Job Analytics - Get detailed analytics
 */
router.get('/analytics', authenticateToken, requireRootAdmin, async (req, res) => {
  try {
    const { period = '7d' } = req.query;
    const days = period === '7d' ? 7 : period === '30d' ? 30 : 1;
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const [
      jobsBySource,
      jobsByType,
      jobsByLocation,
      jobsByCompany,
      jobsByStatus,
      dailyStats
    ] = await Promise.all([
      Job.aggregate([
        { $match: { createdAt: { $gte: startDate } } },
        { $group: { _id: '$source', count: { $sum: 1 } } },
        { $sort: { count: -1 } }
      ]),
      Job.aggregate([
        { $match: { createdAt: { $gte: startDate } } },
        { $group: { _id: '$type', count: { $sum: 1 } } },
        { $sort: { count: -1 } }
      ]),
      Job.aggregate([
        { $match: { createdAt: { $gte: startDate } } },
        { $group: { _id: '$location', count: { $sum: 1 } } },
        { $sort: { count: -1 } },
        { $limit: 10 }
      ]),
      Job.aggregate([
        { $match: { createdAt: { $gte: startDate } } },
        { $group: { _id: '$company', count: { $sum: 1 } } },
        { $sort: { count: -1 } },
        { $limit: 10 }
      ]),
      Job.aggregate([
        { $match: { createdAt: { $gte: startDate } } },
        { $group: { _id: '$status', count: { $sum: 1 } } }
      ]),
      Job.aggregate([
        { $match: { createdAt: { $gte: startDate } } },
        {
          $group: {
            _id: {
              year: { $year: '$createdAt' },
              month: { $month: '$createdAt' },
              day: { $dayOfMonth: '$createdAt' }
            },
            count: { $sum: 1 }
          }
        },
        { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
      ])
    ]);

    const analytics = {
      period,
      jobsBySource,
      jobsByType,
      jobsByLocation,
      jobsByCompany,
      jobsByStatus,
      dailyStats
    };

    const response = ResponseUtil.success(analytics, 'Analytics retrieved successfully');
    return res.status(response.statusCode).json(response);
  } catch (error) {
    const response = ResponseUtil.error('Failed to retrieve analytics', 500);
    return res.status(response.statusCode).json(response);
  }
});

/**
 * System Control - Cleanup, Reset, etc.
 */
router.post('/system/cleanup', authenticateToken, requireRootAdmin, async (req, res) => {
  try {
    const { daysOld = 30 } = req.body;
    const deletedCount = await jobSyncService.cleanupOldJobs(Number(daysOld));
    
    const response = ResponseUtil.success(
      { deletedCount },
      `Cleaned up ${deletedCount} old jobs`
    );
    return res.status(response.statusCode).json(response);
  } catch (error) {
    const response = ResponseUtil.error('Cleanup failed', 500);
    return res.status(response.statusCode).json(response);
  }
});

router.post('/system/reset-metrics', authenticateToken, requireRootAdmin, async (req, res) => {
  try {
    monitoringService.resetMetrics();
    
    const response = ResponseUtil.success(null, 'Metrics reset successfully');
    return res.status(response.statusCode).json(response);
  } catch (error) {
    const response = ResponseUtil.error('Failed to reset metrics', 500);
    return res.status(response.statusCode).json(response);
  }
});

/**
 * Helper functions
 */
async function getJobStatistics() {
  const [total, active, external, bySource, byType] = await Promise.all([
    Job.countDocuments(),
    Job.countDocuments({ status: 'active' }),
    Job.countDocuments({ source: { $in: ['external', 'adzuna', 'indeed', 'linkedin', 'glassdoor'] } }),
    Job.aggregate([
      { $group: { _id: '$source', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]),
    Job.aggregate([
      { $group: { _id: '$type', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ])
  ]);

  return {
    total,
    active,
    external,
    bySource,
    byType
  };
}

async function getRecentActivity() {
  const recentJobs = await Job.find()
    .sort({ createdAt: -1 })
    .limit(10)
    .select('title company source createdAt status')
    .lean();

  return recentJobs;
}

export { router as dashboardRoutes };
