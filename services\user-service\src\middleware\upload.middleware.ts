import multer from 'multer';
import { Request, Response, NextFunction } from 'express';
import { imageService } from '../services/image.service';
import { ResponseUtil } from '../utils/response';

// Configure multer for memory storage
const storage = multer.memoryStorage();

const upload = multer({
  storage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit (increased for better compatibility)
    fieldSize: 2 * 1024 * 1024, // 2MB field size limit
    files: 1, // Only allow 1 file at a time
    fields: 10, // Allow up to 10 form fields
  },
  fileFilter: (req: any, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
    // Check file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only JPEG, PNG, and WebP images are allowed.'));
    }
  },
});

/**
 * Middleware to handle single file upload
 */
export const uploadSingle = (fieldName: string) => {
  return (req: Request, res: Response, next: NextFunction) => {
    // Set longer timeout for file uploads
    req.setTimeout(120000); // 2 minutes
    res.setTimeout(120000); // 2 minutes
    
    const uploadHandler = upload.single(fieldName);
    
    uploadHandler(req, res, (error: any) => {
      if (error) {
        console.error('Upload error:', error);
        
        // Handle specific multer errors
        if (error.code === 'LIMIT_FILE_SIZE') {
          const response = ResponseUtil.error(
            'File too large. Maximum size is 5MB.',
            413
          );
          return res.status(response.statusCode).json(response);
        }
        
        if (error.code === 'LIMIT_FIELD_SIZE') {
          const response = ResponseUtil.error(
            'Field too large. Maximum size is 2MB.',
            413
          );
          return res.status(response.statusCode).json(response);
        }
        
        if (error.code === 'ECONNRESET' || error.message === 'aborted') {
          const response = ResponseUtil.error(
            'Upload cancelled or connection lost. Please try again.',
            408
          );
          return res.status(response.statusCode).json(response);
        }
        
        const response = ResponseUtil.error(
          error.message || 'File upload failed',
          400
        );
        return res.status(response.statusCode).json(response);
      }

      // Validate the uploaded file
      const multerReq = req as Request & { file?: Express.Multer.File };
      if (multerReq.file) {
        const validation = imageService.validateImage(multerReq.file);
        if (!validation.isValid) {
          const response = ResponseUtil.error(validation.error!, 400);
          return res.status(response.statusCode).json(response);
        }
      }

      return next();
    });
  };
};

/**
 * Middleware to handle multiple file uploads
 */
export const uploadMultiple = (fieldName: string, maxCount: number = 5) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const uploadHandler = upload.array(fieldName, maxCount);
    
    uploadHandler(req, res, (error: any) => {
      if (error) {
        console.error('Upload error:', error);
        const response = ResponseUtil.error(
          error.message || 'File upload failed',
          400
        );
        return res.status(response.statusCode).json(response);
      }

      // Validate all uploaded files
      const multerReq = req as Request & { files?: Express.Multer.File[] };
      if (multerReq.files && Array.isArray(multerReq.files)) {
        for (const file of multerReq.files) {
          const validation = imageService.validateImage(file);
          if (!validation.isValid) {
            const response = ResponseUtil.error(validation.error!, 400);
            return res.status(response.statusCode).json(response);
          }
        }
      }

      return next();
    });
  };
};

/**
 * Middleware to extract username from user data for folder organization
 */
export const extractUserInfo = (req: Request, res: Response, next: NextFunction) => {
  try {
    const user = req.user as any;
    if (user) {
      // Create username from email or use existing username
      const username = user.username || user.email?.split('@')[0] || 'user';
      (req as any).userInfo = {
        username: username.replace(/[^a-zA-Z0-9]/g, '_'), // Sanitize username
        userId: user.id,
      };
    }
    return next();
  } catch (error) {
    console.error('Error extracting user info:', error);
    const response = ResponseUtil.error('Failed to extract user information', 500);
    return res.status(response.statusCode).json(response);
  }
};
