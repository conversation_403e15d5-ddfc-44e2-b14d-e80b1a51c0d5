{"name": "@job-platform/frontend-service", "version": "1.0.0", "description": "Job Application Platform - React Frontend Service", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "start": "serve -s dist -l 8080", "prestart": "npm install --production", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "dependencies": {"@headlessui/react": "^2.2.8", "axios": "^1.12.2", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.23.16", "js-cookie": "^3.0.5", "lucide-react": "^0.544.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.63.0", "react-hot-toast": "^2.6.0", "react-query": "^3.39.3", "react-router-dom": "^6.20.1", "recharts": "^3.2.1", "serve": "^14.2.5", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.18", "@types/js-cookie": "^3.0.6", "@types/react": "^18.3.24", "@types/react-dom": "^18.3.7", "@typescript-eslint/eslint-plugin": "^8.44.0", "@typescript-eslint/parser": "^8.44.0", "@vitejs/plugin-react-swc": "^4.1.0", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "autoprefixer": "^10.4.21", "eslint": "^9.36.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "postcss": "^8.5.6", "prettier": "^3.6.2", "tailwindcss": "^3.3.6", "typescript": "^5.9.2", "vite": "^7.1.6", "vitest": "^3.2.4"}, "engines": {"node": ">=22.18.0", "npm": ">=10.0.0"}}