import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { toast } from 'react-hot-toast';
import { ApiResponse, ApiError } from '@/types/api';
// Using DigitalOcean environment variables directly
import { authService } from './auth.service';

class ApiService {
  private client: AxiosInstance;
  private isRefreshing = false;
  private refreshPromise: Promise<any> | null = null;

  constructor() {
    this.client = axios.create({
      baseURL: import.meta.env.VITE_API_BASE_URL || 'https://jobs-app-ydwim.ondigitalocean.app/api/v1',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors(): void {
    // Request interceptor - Add auth token
    this.client.interceptors.request.use(
      (config) => {
        const token = authService.getAccessToken();

        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }

        // Add request ID for tracking
        config.headers['X-Request-ID'] = Math.random().toString(36).substring(2, 15);

        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor - Handle errors and token refresh
    this.client.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        return response;
      },
      async (error) => {
        const originalRequest = error.config;

        // Handle 401 errors - Token expired
        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;

          // If already refreshing, wait for it to complete
          if (this.isRefreshing) {
            return this.refreshPromise!.then(() => {
              const token = localStorage.getItem('accessToken');
              if (token) {
                originalRequest.headers.Authorization = `Bearer ${token}`;
                return this.client(originalRequest);
              } else {
                this.handleAuthError();
                return Promise.reject(error);
              }
            });
          }

          try {
            const refreshToken = authService.getRefreshToken();
            if (refreshToken && refreshToken !== 'undefined' && refreshToken !== 'null') {
              this.isRefreshing = true;
              this.refreshPromise = authService.refreshAccessToken();

              if (this.refreshPromise) {
                const response = await this.refreshPromise;
                const { accessToken } = response;

                // Retry original request
                originalRequest.headers.Authorization = `Bearer ${accessToken}`;
                return this.client(originalRequest);
              } else {
                throw new Error('Failed to create refresh promise');
              }
            } else {
              // No valid refresh token, redirect to login
              this.handleAuthError();
              return Promise.reject(error);
            }
          } catch (refreshError) {
            // Refresh failed, redirect to login and prevent further retries
            console.log('Token refresh failed, redirecting to login');
            this.handleAuthError();
            return Promise.reject(refreshError);
          } finally {
            this.isRefreshing = false;
            this.refreshPromise = null;
          }
        }

        // Handle different error types
        this.handleApiError(error);
        return Promise.reject(error);
      }
    );
  }

  private handleApiError(error: any): void {
    const apiError: ApiError = error.response?.data;

    if (apiError?.message) {
      toast.error(apiError.message);
    } else if (error.code === 'NETWORK_ERROR') {
      toast.error('Network error. Please check your connection.');
    } else if (error.code === 'TIMEOUT') {
      toast.error('Request timeout. Please try again.');
    } else {
      toast.error('An unexpected error occurred.');
    }

    // Log error for debugging
    console.error('API Error:', error);
  }

  private handleAuthError(): void {
    // Clear auth data using auth service
    authService.logout();
    
    // Redirect to login
    window.location.href = '/login';
  }

  // Generic request methods
  async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.get<ApiResponse<T>>(url, config);
    return (response.data.data || response.data) as T;
  }

  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.post<ApiResponse<T>>(url, data, config);
    return (response.data.data || response.data) as T;
  }

  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.put<ApiResponse<T>>(url, data, config);
    return (response.data.data || response.data) as T;
  }

  async patch<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.patch<ApiResponse<T>>(url, data, config);
    return (response.data.data || response.data) as T;
  }

  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.delete<ApiResponse<T>>(url, config);
    return (response.data.data || response.data) as T;
  }

  // File upload method
  async upload<T>(url: string, formData: FormData, onProgress?: (progress: number) => void): Promise<T> {
    const response = await this.client.post<ApiResponse<T>>(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(progress);
        }
      },
    });
    return (response.data.data || response.data) as T;
  }

  // Download file method
  async download(url: string, filename?: string, config?: AxiosRequestConfig): Promise<void> {
    const response = await this.client.get(url, {
      responseType: 'blob',
      ...config,
    });

    const blob = new Blob([response.data]);
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = filename || 'download';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);
  }

  // Health check
  async healthCheck(): Promise<any> {
    const response = await this.client.get('/health');
    return response.data;
  }
}

export const apiService = new ApiService();
