#!/usr/bin/env node

/**
 * JWT Authentication Debug Script - Updated for DigitalOcean Issues
 * This script helps diagnose JWT authentication issues in the DigitalOcean deployment
 * 
 * FIXES APPLIED:
 * 1. Added issuer validation to refresh token verification in auth-service
 * 2. Fixed missing session creation in Google OAuth callback
 * 3. Updated JWT middleware to validate issuer: 'job-platform-auth'
 */

const jwt = require('jsonwebtoken');

// Environment variables that should be consistent across all services
const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key';
const JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET || 'your-super-secret-refresh-key';

console.log('🔍 JWT Authentication Diagnostic Tool - DigitalOcean Fix Version\n');

// Function to decode and analyze a JWT token
function analyzeToken(token, tokenType = 'access') {
  try {
    console.log(`\n📋 Analyzing ${tokenType} token:`);
    console.log(`Token: ${token.substring(0, 50)}...`);
    
    // Decode without verification to see payload
    const decoded = jwt.decode(token, { complete: true });
    console.log('\n📄 Token Header:', JSON.stringify(decoded.header, null, 2));
    console.log('\n📄 Token Payload:', JSON.stringify(decoded.payload, null, 2));
    
    // Check token structure
    const payload = decoded.payload;
    const requiredFields = ['userId', 'email', 'iss', 'exp', 'iat'];
    const missingFields = requiredFields.filter(field => !payload[field]);
    
    if (missingFields.length > 0) {
      console.log(`❌ Missing required fields: ${missingFields.join(', ')}`);
    } else {
      console.log('✅ All required fields present');
    }
    
    // Check issuer (CRITICAL FIX)
    if (payload.iss === 'job-platform-auth') {
      console.log('✅ Correct issuer: job-platform-auth');
    } else {
      console.log(`❌ Incorrect issuer: ${payload.iss} (expected: job-platform-auth)`);
    }
    
    // Check sessionId for refresh tokens
    if (tokenType === 'refresh' && payload.sessionId) {
      console.log(`✅ Session ID present: ${payload.sessionId}`);
    } else if (tokenType === 'refresh') {
      console.log('❌ Missing sessionId in refresh token');
    }
    
    // Check expiration
    const now = Math.floor(Date.now() / 1000);
    if (payload.exp > now) {
      const timeLeft = payload.exp - now;
      console.log(`✅ Token valid for ${Math.floor(timeLeft / 60)} minutes`);
    } else {
      console.log('❌ Token expired');
    }
    
    // Try to verify with different configurations
    console.log('\n🔐 Verification attempts:');
    
    // Try with correct issuer validation (NEW FIX)
    try {
      const secret = tokenType === 'refresh' ? JWT_REFRESH_SECRET : JWT_SECRET;
      jwt.verify(token, secret, { issuer: 'job-platform-auth' });
      console.log('✅ Verified with correct secret and issuer validation (FIXED)');
    } catch (error) {
      console.log(`❌ Failed with issuer validation: ${error.message}`);
    }
    
    // Try without issuer validation (OLD WAY)
    try {
      const secret = tokenType === 'refresh' ? JWT_REFRESH_SECRET : JWT_SECRET;
      jwt.verify(token, secret);
      console.log('⚠️  Verified without issuer validation (OLD WAY - INSECURE)');
    } catch (error) {
      console.log(`❌ Failed without issuer validation: ${error.message}`);
    }
    
    return decoded.payload;
  } catch (error) {
    console.log(`❌ Failed to decode token: ${error.message}`);
    return null;
  }
}

// Function to test API endpoints
async function testEndpoint(url, token) {
  try {
    console.log(`\n🌐 Testing endpoint: ${url}`);
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log(`Status: ${response.status} ${response.statusText}`);
    
    if (response.status === 401) {
      console.log('❌ 401 Unauthorized - Check JWT issuer validation fix');
    } else if (response.status === 200) {
      console.log('✅ 200 OK - Authentication working');
    }
    
    const data = await response.text();
    try {
      const jsonData = JSON.parse(data);
      console.log('Response:', JSON.stringify(jsonData, null, 2));
    } catch {
      console.log('Response (text):', data);
    }
    
    return response.status;
  } catch (error) {
    console.log(`❌ Request failed: ${error.message}`);
    return null;
  }
}

// Function to check environment configuration
function checkEnvironment() {
  console.log('\n🔧 Environment Configuration:');
  console.log(`JWT_SECRET: ${JWT_SECRET === 'your-super-secret-jwt-key' ? '⚠️  Using default value' : '✅ Custom value set'}`);
  console.log(`JWT_REFRESH_SECRET: ${JWT_REFRESH_SECRET === 'your-super-secret-refresh-key' ? '⚠️  Using default value' : '✅ Custom value set'}`);
  
  if (JWT_SECRET === 'your-super-secret-jwt-key') {
    console.log('\n❌ WARNING: Using default JWT_SECRET in production is insecure!');
    console.log('   Set JWT_SECRET environment variable in DigitalOcean App Platform');
  }
  
  console.log('\n🔧 FIXES APPLIED:');
  console.log('✅ 1. Added issuer validation to refresh token verification');
  console.log('✅ 2. Fixed missing session creation in Google OAuth callback');
  console.log('✅ 3. Updated JWT middleware to validate issuer: "job-platform-auth"');
}

// Function to test refresh token endpoint
async function testRefreshToken(refreshToken) {
  try {
    console.log('\n🔄 Testing refresh endpoint...');
    
    const response = await fetch('https://jobs-app-ydwim.ondigitalocean.app/api/v1/auth/refresh', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ refreshToken })
    });
    
    console.log(`Refresh Status: ${response.status} ${response.statusText}`);
    
    if (response.status === 401) {
      console.log('❌ 401 Unauthorized - Possible causes:');
      console.log('   1. Session not created during Google OAuth (FIXED)');
      console.log('   2. Refresh token issuer validation missing (FIXED)');
      console.log('   3. JWT_REFRESH_SECRET mismatch between services');
    } else if (response.status === 200) {
      console.log('✅ 200 OK - Refresh token working correctly');
    }
    
    const data = await response.text();
    try {
      const jsonData = JSON.parse(data);
      console.log('Refresh Response:', JSON.stringify(jsonData, null, 2));
      
      if (jsonData.accessToken) {
        console.log('\n🎉 SUCCESS: New access token received!');
        return jsonData.accessToken;
      }
    } catch {
      console.log('Refresh Response (text):', data);
    }
  } catch (error) {
    console.log(`❌ Refresh request failed: ${error.message}`);
  }
  return null;
}

// Main diagnostic function
async function runDiagnostics() {
  checkEnvironment();
  
  // Get tokens from command line arguments or prompt user
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log('\n📝 Usage:');
    console.log('  node debug-jwt-auth.js <access_token> [refresh_token]');
    console.log('  node debug-jwt-auth.js --test-endpoints <access_token>');
    console.log('  node debug-jwt-auth.js --test-refresh <refresh_token>');
    console.log('\n💡 You can get tokens from browser localStorage after Google OAuth login');
    console.log('\n🔧 After applying fixes, you should see:');
    console.log('   ✅ Issuer validation working');
    console.log('   ✅ Session creation during OAuth');
    console.log('   ✅ Refresh tokens working');
    return;
  }
  
  if (args[0] === '--test-refresh' && args[1]) {
    // Test refresh token specifically
    const refreshToken = args[1];
    console.log('\n🧪 Testing refresh token flow...');
    analyzeToken(refreshToken, 'refresh');
    const newAccessToken = await testRefreshToken(refreshToken);
    
    if (newAccessToken) {
      console.log('\n🧪 Testing new access token...');
      analyzeToken(newAccessToken, 'access');
    }
    return;
  }
  
  if (args[0] === '--test-endpoints' && args[1]) {
    // Test endpoints mode
    const token = args[1];
    console.log('\n🧪 Testing API endpoints...');
    
    const endpoints = [
      'https://jobs-app-ydwim.ondigitalocean.app/api/v1/users/analytics',
      'https://jobs-app-ydwim.ondigitalocean.app/api/v1/resumes/analytics',
      'https://jobs-app-ydwim.ondigitalocean.app/api/v1/applications/analytics'
    ];
    
    for (const endpoint of endpoints) {
      await testEndpoint(endpoint, token);
    }
    return;
  }
  
  // Analyze tokens mode
  const accessToken = args[0];
  const refreshToken = args[1];
  
  if (accessToken) {
    analyzeToken(accessToken, 'access');
  }
  
  if (refreshToken) {
    analyzeToken(refreshToken, 'refresh');
    await testRefreshToken(refreshToken);
  }
}

// Run diagnostics
if (require.main === module) {
  runDiagnostics().catch(console.error);
}

module.exports = { analyzeToken, testEndpoint, checkEnvironment };
